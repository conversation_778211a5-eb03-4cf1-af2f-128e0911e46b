import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { map, delay } from 'rxjs/operators';
import { 
  Notification, 
  NotificationType, 
  NotificationPriority, 
  NotificationFilter,
  NotificationBadge,
  CreateNotificationRequest 
} from '../models/notification.model';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private notificationsSubject = new BehaviorSubject<Notification[]>([]);
  private badgeSubject = new BehaviorSubject<NotificationBadge>({
    total: 0,
    unread: 0,
    byType: {} as Record<NotificationType, number>,
    byPriority: {} as Record<NotificationPriority, number>
  });

  public notifications$ = this.notificationsSubject.asObservable();
  public badge$ = this.badgeSubject.asObservable();

  constructor() {
    this.initializeMockData();
  }

  getNotifications(filter?: NotificationFilter): Observable<Notification[]> {
    return this.notifications$.pipe(
      map(notifications => this.applyFilter(notifications, filter))
    );
  }

  getUnreadCount(): Observable<number> {
    return this.notifications$.pipe(
      map(notifications => notifications.filter(n => !n.isRead).length)
    );
  }

  markAsRead(notificationId: string): Observable<boolean> {
    const notifications = this.notificationsSubject.value;
    const notification = notifications.find(n => n.id === notificationId);
    
    if (notification && !notification.isRead) {
      notification.isRead = true;
      notification.readAt = new Date();
      this.notificationsSubject.next([...notifications]);
      this.updateBadge();
      return of(true).pipe(delay(200)); // Simulate API delay
    }
    
    return of(false);
  }

  markAllAsRead(): Observable<boolean> {
    const notifications = this.notificationsSubject.value;
    const updatedNotifications = notifications.map(n => ({
      ...n,
      isRead: true,
      readAt: n.readAt || new Date()
    }));
    
    this.notificationsSubject.next(updatedNotifications);
    this.updateBadge();
    return of(true).pipe(delay(500)); // Simulate API delay
  }

  archiveNotification(notificationId: string): Observable<boolean> {
    const notifications = this.notificationsSubject.value;
    const notification = notifications.find(n => n.id === notificationId);
    
    if (notification) {
      notification.isArchived = true;
      this.notificationsSubject.next([...notifications]);
      this.updateBadge();
      return of(true).pipe(delay(200)); // Simulate API delay
    }
    
    return of(false);
  }

  deleteNotification(notificationId: string): Observable<boolean> {
    const notifications = this.notificationsSubject.value;
    const filteredNotifications = notifications.filter(n => n.id !== notificationId);
    
    this.notificationsSubject.next(filteredNotifications);
    this.updateBadge();
    return of(true).pipe(delay(200)); // Simulate API delay
  }

  createNotification(request: CreateNotificationRequest): Observable<Notification> {
    const notification: Notification = {
      id: this.generateId(),
      title: request.title,
      message: request.message,
      type: request.type,
      priority: request.priority,
      isRead: false,
      isArchived: false,
      createdAt: new Date(),
      actionUrl: request.actionUrl,
      actionLabel: request.actionLabel,
      expiresAt: request.expiresAt,
      metadata: request.metadata
    };

    const notifications = this.notificationsSubject.value;
    this.notificationsSubject.next([notification, ...notifications]);
    this.updateBadge();
    
    return of(notification).pipe(delay(200)); // Simulate API delay
  }

  private applyFilter(notifications: Notification[], filter?: NotificationFilter): Notification[] {
    if (!filter) {
      return notifications.filter(n => !n.isArchived);
    }

    return notifications.filter(notification => {
      if (filter.isArchived !== undefined && notification.isArchived !== filter.isArchived) {
        return false;
      }
      
      if (filter.isRead !== undefined && notification.isRead !== filter.isRead) {
        return false;
      }
      
      if (filter.type && filter.type.length > 0 && !filter.type.includes(notification.type)) {
        return false;
      }
      
      if (filter.priority && filter.priority.length > 0 && !filter.priority.includes(notification.priority)) {
        return false;
      }
      
      if (filter.dateFrom && notification.createdAt < filter.dateFrom) {
        return false;
      }
      
      if (filter.dateTo && notification.createdAt > filter.dateTo) {
        return false;
      }
      
      return true;
    });
  }

  private updateBadge(): void {
    const notifications = this.notificationsSubject.value.filter(n => !n.isArchived);
    const unreadNotifications = notifications.filter(n => !n.isRead);
    
    const byType = {} as Record<NotificationType, number>;
    const byPriority = {} as Record<NotificationPriority, number>;
    
    // Initialize counters
    Object.values(NotificationType).forEach(type => byType[type] = 0);
    Object.values(NotificationPriority).forEach(priority => byPriority[priority] = 0);
    
    // Count unread notifications by type and priority
    unreadNotifications.forEach(notification => {
      byType[notification.type]++;
      byPriority[notification.priority]++;
    });

    const badge: NotificationBadge = {
      total: notifications.length,
      unread: unreadNotifications.length,
      byType,
      byPriority
    };

    this.badgeSubject.next(badge);
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  private initializeMockData(): void {
    const mockNotifications: Notification[] = [
      {
        id: '1',
        title: 'System Maintenance Scheduled',
        message: 'Planned maintenance window for Server-01 scheduled for tonight at 2:00 AM',
        type: NotificationType.MAINTENANCE,
        priority: NotificationPriority.MEDIUM,
        isRead: false,
        isArchived: false,
        createdAt: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
        actionUrl: '/maintenance/schedule',
        actionLabel: 'View Details'
      },
      {
        id: '2',
        title: 'Critical System Alert',
        message: 'Database connection timeout detected on production server',
        type: NotificationType.ERROR,
        priority: NotificationPriority.CRITICAL,
        isRead: false,
        isArchived: false,
        createdAt: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
        actionUrl: '/dashboard',
        actionLabel: 'View Details'
      },
      {
        id: '3',
        title: 'New User Registration',
        message: 'John Doe has registered and is pending approval',
        type: NotificationType.INFO,
        priority: NotificationPriority.LOW,
        isRead: true,
        isArchived: false,
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
        readAt: new Date(Date.now() - 1000 * 60 * 30),
        actionUrl: '/users/pending',
        actionLabel: 'Review'
      }
    ];

    this.notificationsSubject.next(mockNotifications);
    this.updateBadge();
  }
}