# Feature-Set: Störungs- und Wartungsmeldungen

## Übersicht

Dieses Feature-Set umfasst die vollständige Verwaltung von Störungsmeldungen und Wartungsfenstern durch Administratoren.

## Fachliche Anforderungen

### F-INC-001: Meldungsübersicht
**Als** Administrator  
**möchte ich** eine Übersicht aller Störungs- und Wartungsmeldungen sehen  
**damit** ich den aktuellen Status aller Meldungen im Blick behalte

#### Akzeptanzkriterien
- [ ] Tabellarische Darstellung aller Meldungen
- [ ] Unterscheidung zwischen Störungen und Wartungen (Typ-Kennzeichnung)
- [ ] Anzeige von Titel, betroffenes System, Priorität, Status und Zeitstempel
- [ ] Filterung nach Meldungstyp (Störung/Wartung)
- [ ] Filterung nach Priorität (High-Critical/Normal)
- [ ] Filterung nach Status (Aktiv/Behoben/Geplant)
- [ ] Sortierung nach Erstellungsdatum, Priorität oder Status

### F-INC-002: Störungsmeldung erstellen
**Als** Administrator  
**möchte ich** neue Störungsmeldungen erstellen können  
**damit** Benutzer über aktuelle Probleme informiert werden

#### Akzeptanzkriterien
- [ ] Formular mit Pflichtfeldern: Titel, betroffenes System, Beschreibung
- [ ] Auswahl der Priorität (High-Critical oder Normal)
- [ ] Auswahl des betroffenen Systems aus Dropdown-Liste
- [ ] Optionale Felder: Geschätzte Behebungszeit, Workaround
- [ ] Automatische Zeitstempel-Erstellung
- [ ] Validierung aller Pflichtfelder
- [ ] Bestätigungsmeldung nach Erstellung

### F-INC-003: Wartungsfenster erstellen
**Als** Administrator  
**möchte ich** geplante Wartungsfenster ankündigen können  
**damit** Benutzer über geplante Ausfälle informiert sind

#### Akzeptanzkriterien
- [ ] Formular mit Pflichtfeldern: Titel, betroffenes System, Beschreibung
- [ ] Auswahl von Start- und Endzeitpunkt der Wartung
- [ ] Auswahl des betroffenen Systems aus Dropdown-Liste
- [ ] Optionale Felder: Erwartete Auswirkungen, Kontaktinformationen
- [ ] Validierung: Endzeitpunkt muss nach Startzeitpunkt liegen
- [ ] Validierung aller Pflichtfelder
- [ ] Bestätigungsmeldung nach Erstellung

### F-INC-004: Meldung bearbeiten
**Als** Administrator  
**möchte ich** bestehende Meldungen bearbeiten können  
**damit** ich Updates und Korrekturen vornehmen kann

#### Akzeptanzkriterien
- [ ] Bearbeiten-Dialog mit vorausgefüllten Feldern
- [ ] Änderung aller Felder außer Erstellungsdatum möglich
- [ ] Automatische Aktualisierung des "Zuletzt geändert"-Zeitstempels
- [ ] Validierung wie bei Neuanlage
- [ ] Versionierung der Änderungen (optional für MVP)
- [ ] Bestätigungsmeldung nach Speichern

### F-INC-005: Meldung als behoben markieren
**Als** Administrator  
**möchte ich** Störungsmeldungen als behoben markieren können  
**damit** Benutzer über die Lösung informiert werden

#### Akzeptanzkriterien
- [ ] "Behoben"-Button bei aktiven Störungsmeldungen
- [ ] Optionales Feld für Lösungsbeschreibung
- [ ] Automatische Zeitstempel-Setzung für Behebungszeit
- [ ] Status-Änderung von "Aktiv" zu "Behoben"
- [ ] Bestätigungsmeldung nach Statusänderung
- [ ] Archivierung behobener Meldungen (optional)

### F-INC-006: Wartungsfenster abschließen
**Als** Administrator  
**möchte ich** abgeschlossene Wartungsfenster markieren können  
**damit** der Status korrekt angezeigt wird

#### Akzeptanzkriterien
- [ ] "Abgeschlossen"-Button bei laufenden Wartungen
- [ ] Optionales Feld für Abschlussbericht
- [ ] Automatische Zeitstempel-Setzung für Abschlusszeit
- [ ] Status-Änderung von "Geplant/Laufend" zu "Abgeschlossen"
- [ ] Bestätigungsmeldung nach Statusänderung

### F-INC-007: Meldung löschen
**Als** Administrator  
**möchte ich** fehlerhafte oder nicht mehr relevante Meldungen löschen können  
**damit** die Liste aktuell und korrekt bleibt

#### Akzeptanzkriterien
- [ ] Löschen-Button mit Sicherheitsabfrage
- [ ] Warnung vor unwiderruflichem Löschen
- [ ] Prüfung auf Abhängigkeiten (Benachrichtigungen)
- [ ] Bestätigungsmeldung nach erfolgreichem Löschen
- [ ] Automatische Aktualisierung der Meldungsliste

## Prioritätsstufen

| Priorität | Beschreibung | Verwendung |
|-----------|--------------|------------|
| High-Critical | Kritische Störungen | Systemausfall, Sicherheitsprobleme, Datenverlust |
| Normal | Standard-Störungen | Performance-Probleme, kleinere Funktionsfehler |

## Meldungstypen

| Typ | Beschreibung | Eigenschaften |
|-----|--------------|---------------|
| Störung | Ungeplante Probleme | Sofortige Benachrichtigung, Behebung erforderlich |
| Wartung | Geplante Arbeiten | Vorab-Ankündigung, definierter Zeitraum |

## Status-Definitionen

| Status | Störungen | Wartungen |
|--------|-----------|-----------|
| Aktiv | Problem besteht | - |
| Geplant | - | Wartung angekündigt |
| Laufend | - | Wartung in Bearbeitung |
| Behoben | Problem gelöst | - |
| Abgeschlossen | - | Wartung beendet |

## Technische Hinweise

- Echtzeit-Benachrichtigungen an Frontend-Benutzer
- Zeitstempel in UTC mit lokaler Anzeige
- Rich-Text-Editor für Beschreibungen (optional)
- E-Mail-Benachrichtigungen (optional für MVP)

## Abhängigkeiten

- Systemverwaltung muss verfügbar sein
- Benutzer-Subscription-System
- Benachrichtigungssystem
- GraphQL-Backend-API

### Hauptentitäten
```mermaid
erDiagram
    System {
        int systemID PK
        string name
        string description
        SystemStatus status
        datetime createdAt
        datetime updatedAt
    }
    
    User {
        int userID PK
        string name
        string email
        datetime registeredAt
        datetime lastActive
    }
    
    Incident {
        int incidentID PK
        string title
        string description
        IncidentType type
        Priority priority
        Status status
        int systemID FK
        datetime createdAt
        datetime resolvedAt
    }
    
    Subscription {
        int subscriptionID PK
        int userID FK
        int systemID FK
        SubscriptionType type
        datetime createdAt
    }
    
    System ||--o{ Incident : "affects"
    System ||--o{ Subscription : "subscribed to"
    User ||--o{ Subscription : "subscribes"
```