import { Component, inject, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { Subject, takeUntil } from 'rxjs';

import { AuthFacade } from '../../../store/auth/auth.facade';
import { LoginCredentials } from '../../../core/models/user.model';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule
  ],
  template: `
    <div class="login-container">
      <div class="login-card-wrapper">
        <mat-card class="login-card">
          <mat-card-header class="login-header">
            <div class="logo-section">
              <div class="logo-placeholder">SB</div>
              <div class="brand-info">
                <h1>StörungsBuddy</h1>
                <p>Administrator Login</p>
              </div>
            </div>
          </mat-card-header>

          <mat-card-content class="login-content">
            <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Email</mat-label>
                <input
                  matInput
                  type="email"
                  formControlName="email"
                  placeholder="<EMAIL>"
                  autocomplete="email"
                >
                <mat-icon matSuffix>email</mat-icon>
                <mat-error *ngIf="loginForm.get('email')?.hasError('required')">
                  Email is required
                </mat-error>
                <mat-error *ngIf="loginForm.get('email')?.hasError('email')">
                  Please enter a valid email
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Password</mat-label>
                <input
                  matInput
                  [type]="hidePassword ? 'password' : 'text'"
                  formControlName="password"
                  placeholder="Enter your password"
                  autocomplete="current-password"
                >
                <button
                  mat-icon-button
                  matSuffix
                  type="button"
                  (click)="hidePassword = !hidePassword"
                  [attr.aria-label]="'Hide password'"
                  [attr.aria-pressed]="hidePassword"
                >
                  <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
                </button>
                <mat-error *ngIf="loginForm.get('password')?.hasError('required')">
                  Password is required
                </mat-error>
                <mat-error *ngIf="loginForm.get('password')?.hasError('minlength')">
                  Password must be at least 6 characters
                </mat-error>
              </mat-form-field>

              <div class="error-message" *ngIf="authError$ | async as error">
                <mat-icon>error</mat-icon>
                <span>{{ error }}</span>
              </div>

              <button
                mat-raised-button
                color="primary"
                type="submit"
                class="login-button full-width"
                [disabled]="loginForm.invalid || (isLoading$ | async)"
              >
                <mat-spinner diameter="20" *ngIf="isLoading$ | async"></mat-spinner>
                <span *ngIf="!(isLoading$ | async)">Sign In</span>
                <span *ngIf="isLoading$ | async">Signing In...</span>
              </button>
            </form>

            <div class="login-help">
              <p class="demo-credentials">
                <strong>Demo Credentials:</strong><br>
                Email: admin&#64;stoerungsbuddy.com<br>
                Password: password123
              </p>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  `,
  styles: [`
    .login-container {
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 1rem;
    }

    .login-card-wrapper {
      width: 100%;
      max-width: 400px;
    }

    .login-card {
      padding: 0;
      border-radius: 12px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }

    .login-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 2rem 1.5rem 1.5rem;
      border-radius: 12px 12px 0 0;
      margin-bottom: 0;
    }

    .logo-section {
      display: flex;
      align-items: center;
      gap: 1rem;
      width: 100%;
    }

    .logo-placeholder {
      width: 48px;
      height: 48px;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 1.25rem;
      color: white;
      backdrop-filter: blur(10px);
    }

    .brand-info h1 {
      margin: 0;
      font-size: 1.5rem;
      font-weight: 600;
      letter-spacing: -0.025em;
    }

    .brand-info p {
      margin: 0.25rem 0 0;
      opacity: 0.9;
      font-size: 0.875rem;
    }

    .login-content {
      padding: 2rem 1.5rem;
    }

    .login-form {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .full-width {
      width: 100%;
    }

    .error-message {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: #f44336;
      font-size: 0.875rem;
      padding: 0.5rem;
      background-color: #ffebee;
      border-radius: 4px;
      border-left: 4px solid #f44336;
    }

    .login-button {
      height: 48px;
      font-size: 1rem;
      font-weight: 500;
      margin-top: 0.5rem;
    }

    .login-button mat-spinner {
      margin-right: 0.5rem;
    }

    .login-help {
      margin-top: 2rem;
      text-align: center;
    }

    .demo-credentials {
      background-color: #f5f5f5;
      padding: 1rem;
      border-radius: 8px;
      font-size: 0.875rem;
      color: #666;
      margin: 0;
      line-height: 1.5;
    }

    @media (max-width: 480px) {
      .login-container {
        padding: 0.5rem;
      }
      
      .login-header {
        padding: 1.5rem 1rem 1rem;
      }
      
      .login-content {
        padding: 1.5rem 1rem;
      }
    }
  `]
})
export class LoginComponent implements OnInit, OnDestroy {
  private fb = inject(FormBuilder);
  private authFacade = inject(AuthFacade);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private destroy$ = new Subject<void>();

  loginForm: FormGroup;
  hidePassword = true;
  returnUrl: string = '/dashboard';

  // Observables from facade
  isLoading$ = this.authFacade.isLoading$;
  authError$ = this.authFacade.error$;
  isAuthenticated$ = this.authFacade.isAuthenticated$;

  constructor() {
    this.loginForm = this.fb.group({
      email: ['<EMAIL>', [Validators.required, Validators.email]],
      password: ['password123', [Validators.required, Validators.minLength(6)]]
    });
  }

  ngOnInit() {
    // Get return URL from route parameters or default to dashboard
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';
    
    // Clear any existing auth errors
    this.authFacade.clearAuthError();

    // Redirect if already authenticated
    this.isAuthenticated$
      .pipe(takeUntil(this.destroy$))
      .subscribe(isAuthenticated => {
        if (isAuthenticated) {
          this.router.navigate([this.returnUrl]);
        }
      });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onSubmit() {
    if (this.loginForm.valid) {
      const credentials: LoginCredentials = {
        email: this.loginForm.value.email,
        password: this.loginForm.value.password
      };
      
      this.authFacade.login(credentials);
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.loginForm.controls).forEach(key => {
        this.loginForm.get(key)?.markAsTouched();
      });
    }
  }
}