import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { ApplicationEditFormComponent } from './application-edit-form.component';
import { ApplicationValidators } from '../../shared/validators/application-validators';
import { of } from 'rxjs';
import { Application } from '../../../../core/models/application.model';

describe('ApplicationEditFormComponent', () => {
  let component: ApplicationEditFormComponent;
  let fixture: ComponentFixture<ApplicationEditFormComponent>;
  let mockApplicationValidators: Partial<ApplicationValidators>;
  const mockApplication: Application = {
    identifier: '1',
    name: 'Test App',
    description: 'Test Description',
    isDeleted: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  beforeEach(async () => {
    mockApplicationValidators = {
      nameUniquenessValidator: () => () => of(null),
    };

    await TestBed.configureTestingModule({
      imports: [
        ReactiveFormsModule,
        NoopAnimationsModule,
        ApplicationEditFormComponent,
      ],
      providers: [
        { provide: ApplicationValidators, useValue: mockApplicationValidators },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ApplicationEditFormComponent);
    component = fixture.componentInstance;
    component.application = mockApplication;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize the form with application data', () => {
    expect(component.applicationForm.value).toEqual({
      name: 'Test App',
      description: 'Test Description',
    });
  });

  it('should return true if form has changes', () => {
    component.applicationForm.patchValue({ name: 'New Name' });
    expect(component.hasChanges()).toBeTrue();
  });

  it('should return false if form has no changes', () => {
    expect(component.hasChanges()).toBeFalse();
  });

  it('should emit formSubmit event on valid submission', () => {
    spyOn(component.formSubmit, 'emit');
    component.applicationForm.patchValue({ name: 'New Name' });
    component.onSubmit();
    expect(component.formSubmit.emit).toHaveBeenCalledWith({
      name: 'New Name',
      description: 'Test Description',
    });
  });

  it('should not emit formSubmit event on invalid submission', () => {
    spyOn(component.formSubmit, 'emit');
    component.applicationForm.patchValue({ name: '' });
    component.onSubmit();
    expect(component.formSubmit.emit).not.toHaveBeenCalled();
  });

  it('should emit formCancel event on cancel', () => {
    spyOn(component.formCancel, 'emit');
    component.onCancel();
    expect(component.formCancel.emit).toHaveBeenCalled();
  });
});