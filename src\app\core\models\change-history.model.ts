import { EntityType } from '../enums/entity-type.enum';

export interface ChangeHistoryEntry {
  identifier: string;
  entityType: EntityType;
  entityIdentifier: string;
  action: string;
  changes: Record<string, any>;
  userId?: string;
  userName?: string;
  timestamp: string;
  description?: string;
}

export interface ChangeHistoryFilter {
  entityType?: EntityType;
  entityIdentifier?: string;
  userId?: string;
  fromDate?: string;
  toDate?: string;
}