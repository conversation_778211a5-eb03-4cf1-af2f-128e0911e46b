# Product Requirements Document (PRD) - Störungs-Buddy Frontend

## 1. Introduction

### 1.1. Problem Statement
Administrators require an efficient and centralized platform to manage system master data, track real-time incident reports, and schedule maintenance windows. The current process is fragmented, leading to delays in communication and resolution.

### 1.2. Proposed Solution
The "Störungs-Buddy" Frontend is a web-based administration user interface (UI) that provides a single, comprehensive dashboard for all administrative tasks. It will streamline the management of applications, incidents, and user subscriptions, providing a clear overview and simplifying the workflow for system administrators.

## 2. Goals and Objectives

- **Centralize Management**: Provide a single source of truth for application master data, incident reports, and maintenance schedules.
- **Improve Efficiency**: Streamline the process of creating, updating, and resolving incidents and maintenance tasks.
- **Enhance Transparency**: Offer a clear overview of system statuses and user subscriptions for administrators.
- **Provide a Scalable Foundation**: Build a modern, robust frontend application that is easy to maintain and extend.

## 3. Target Audience

- **Primary**: System administrators who are responsible for the day-to-day management of the company's applications, systems, and incident response.
- **Secondary (Indirect)**: Company employees who will consume the status information through a separate frontend, relying on the data managed by the administrators.

## 4. Features and Requirements

### 4.1. F0: Core UI and Infrastructure
- **Description**: Establish the fundamental structure of the Angular application.
- **Requirements**:
    - [ ] Set up the basic Angular project structure with standalone components.
    - [ ] Implement a base layout with a persistent navigation menu.
    - [ ] Configure the Apollo Client to handle all GraphQL communication with the backend API.
    - [ ] Implement a basic authentication service to protect the application.

### 4.2. F1: Master Data Management (Applications)
- **Description**: A CRUD (Create, Read, Update, Delete) interface for managing applications.
- **Requirements**:
    - [ ] Administrators can view a list of all applications.
    - [ ] Administrators can add new applications with a name, description, and initial status.
    - [ ] Administrators can edit the details of existing applications.
    - [ ] Administrators can remove applications from the system.
    - [ ] The application list should be presented in a simple, non-hierarchical table or list.

### 4.3. F2: Incident and Maintenance Management
- **Description**: A system for creating and managing incident reports and scheduled maintenance windows.
- **Requirements**:
    - [ ] Administrators can create, edit, and mark reports as "resolved."
    - [ ] A clear distinction must be made between an "Incident" (Störung) and "Maintenance" (Wartung).
    - [ ] Reports must be assigned a priority level: "Normal" or "High-Critical".
    - [ ] The interface should allow for easy filtering and viewing of all active and resolved reports.

### 4.4. F3: User Subscription Management
- **Description**: An interface for managing user subscriptions to specific applications.
- **Requirements**:
    - [ ] Administrators can view a list of all users who have logged into the system.
    - [ ] Administrators can see which applications each user is subscribed to.
    - [ ] Administrators can modify these user-to-application assignments.
    - [ ] As an initial default, all new users will be automatically subscribed to all available applications.

## 5. Technical Specifications

### 5.1. Frontend Stack
- **Framework**: Angular 19 (using Standalone Components)
- **State Management**: NgRx
- **UI Framework**: Angular Material (Theme: Indigo-Pink)
- **API Integration**: Apollo Client for GraphQL

### 5.2. Development Environment
- **Package Manager**: npm
- **Node.js Version**: 20.19.1
- **Angular CLI Version**: 19.2.13

### 5.3. API
- **Type**: GraphQL
- **Endpoint**: `http://localhost:5079/graphql`
- **Authentication**: oAuth2 (simplified with a login mask for the MVP).

## 6. Assumptions and Constraints

- The backend GraphQL API is already defined and available at the specified endpoint.
- The initial focus is on the Minimum Viable Product (MVP) features outlined above.
- All development must adhere to the coding standards and architectural principles defined in the project's `.roo/rules/` directory.
- All terminal commands must be compatible with Windows Powershell.