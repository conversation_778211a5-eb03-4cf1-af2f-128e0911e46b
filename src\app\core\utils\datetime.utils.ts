/**
 * DateTime utility functions for handling Date objects and ISO strings
 * Provides type-safe conversion between Date objects and ISO string representations
 */

/**
 * Converts a Date object or ISO string to an ISO string for GraphQL operations
 * @param dateTime - Date object or ISO string
 * @returns ISO string representation
 */
export function toISOString(dateTime: Date | string): string {
  if (dateTime instanceof Date) {
    return dateTime.toISOString();
  }
  return dateTime;
}

/**
 * Converts an ISO string to a Date object
 * @param isoString - ISO string representation
 * @returns Date object
 */
export function toDate(isoString: string): Date {
  return new Date(isoString);
}

/**
 * Converts a Date object or ISO string to a Date object
 * @param dateTime - Date object or ISO string
 * @returns Date object
 */
export function ensureDate(dateTime: Date | string): Date {
  if (dateTime instanceof Date) {
    return dateTime;
  }
  return new Date(dateTime);
}

/**
 * Validates if a string is a valid ISO date string
 * @param dateString - String to validate
 * @returns true if valid ISO date string
 */
export function isValidISOString(dateString: string): boolean {
  const date = new Date(dateString);
  return !isNaN(date.getTime()) && date.toISOString() === dateString;
}

/**
 * Formats a Date object or ISO string for display
 * @param dateTime - Date object or ISO string
 * @param locale - Locale for formatting (default: 'de-DE')
 * @param options - Intl.DateTimeFormatOptions
 * @returns Formatted date string
 */
export function formatDateTime(
  dateTime: Date | string,
  locale: string = 'de-DE',
  options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }
): string {
  const date = ensureDate(dateTime);
  return date.toLocaleString(locale, options);
}

/**
 * Formats a Date object or ISO string for date input fields
 * @param dateTime - Date object or ISO string
 * @returns Date string in YYYY-MM-DD format
 */
export function formatDateForInput(dateTime: Date | string): string {
  const date = ensureDate(dateTime);
  return date.toISOString().split('T')[0];
}

/**
 * Formats a Date object or ISO string for datetime-local input fields
 * @param dateTime - Date object or ISO string
 * @returns DateTime string in YYYY-MM-DDTHH:mm format
 */
export function formatDateTimeForInput(dateTime: Date | string): string {
  const date = ensureDate(dateTime);
  const isoString = date.toISOString();
  return isoString.slice(0, 16); // Remove seconds and timezone
}

/**
 * Creates a Date object from date and time input values
 * @param dateValue - Date string in YYYY-MM-DD format
 * @param timeValue - Time string in HH:mm format
 * @returns Date object
 */
export function createDateFromInputs(dateValue: string, timeValue: string): Date {
  return new Date(`${dateValue}T${timeValue}:00.000Z`);
}

/**
 * Checks if a date/time is in the past
 * @param dateTime - Date object or ISO string
 * @returns true if the date/time is in the past
 */
export function isPastDateTime(dateTime: Date | string): boolean {
  const date = ensureDate(dateTime);
  return date.getTime() < Date.now();
}

/**
 * Checks if a date/time is in the future
 * @param dateTime - Date object or ISO string
 * @returns true if the date/time is in the future
 */
export function isFutureDateTime(dateTime: Date | string): boolean {
  const date = ensureDate(dateTime);
  return date.getTime() > Date.now();
}

/**
 * Calculates the duration between two dates in milliseconds
 * @param startTime - Start date/time
 * @param endTime - End date/time
 * @returns Duration in milliseconds
 */
export function calculateDuration(
  startTime: Date | string,
  endTime: Date | string
): number {
  const start = ensureDate(startTime);
  const end = ensureDate(endTime);
  return end.getTime() - start.getTime();
}

/**
 * Formats duration in milliseconds to human-readable string
 * @param durationMs - Duration in milliseconds
 * @param locale - Locale for formatting (default: 'de-DE')
 * @returns Formatted duration string
 */
export function formatDuration(durationMs: number, locale: string = 'de-DE'): string {
  const hours = Math.floor(durationMs / (1000 * 60 * 60));
  const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
  
  if (locale === 'de-DE') {
    if (hours > 0) {
      return `${hours}h ${minutes}min`;
    }
    return `${minutes}min`;
  }
  
  // English fallback
  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  }
  return `${minutes}m`;
}