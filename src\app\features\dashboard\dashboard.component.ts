import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectionStrategy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { Router } from '@angular/router';
import { combineLatest, map, takeUntil, Subject } from 'rxjs';

import { IncidentsFacade } from '../../store/incidents/incidents.facade';
import { ApplicationsFacade } from '../../store/applications/applications.facade';
import { Incident, IncidentType } from '../../core/models/incident.model';
import { Application } from '../../core/models/application.model';
import { DashboardMetrics, DashboardData } from './models/dashboard-metrics.interface';

import { DashboardStatsComponent } from './components/dashboard-stats/dashboard-stats.component';
import { ActiveIncidentsListComponent } from './components/active-incidents-list/active-incidents-list.component';
import { RecentApplicationsListComponent } from './components/recent-applications-list/recent-applications-list.component';

// Dialog Components
import { IncidentCreateDialogComponent, IncidentCreateDialogData } from '../incident-management/components/incident-create-dialog/incident-create-dialog.component';
import { ApplicationCreateDialogComponent } from '../application-management/dialogs/application-create-dialog/application-create-dialog.component';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    MatDialogModule,
    MatSnackBarModule,
    DashboardStatsComponent,
    ActiveIncidentsListComponent,
    RecentApplicationsListComponent
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <div class="dashboard-container">
      <div class="dashboard-header">
        <div>
          <h1>Dashboard</h1>
          <p>Willkommen im StörungsBuddy Admin-Bereich</p>
        </div>
        <button
          mat-icon-button
          (click)="refreshData()"
          [disabled]="(dashboardData$ | async)?.isLoading"
          matTooltip="Daten aktualisieren"
          class="refresh-button">
          <mat-icon [class.spinning]="(dashboardData$ | async)?.isLoading">refresh</mat-icon>
        </button>
      </div>
      
      <!-- Loading State -->
      <div *ngIf="(dashboardData$ | async)?.isLoading" class="loading-container">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Dashboard-Daten werden geladen...</p>
      </div>
      
      <!-- Error State -->
      <div *ngIf="(dashboardData$ | async)?.hasError" class="error-container">
        <mat-icon class="error-icon">error</mat-icon>
        <p>{{ (dashboardData$ | async)?.errorMessage }}</p>
        <button mat-raised-button color="primary" (click)="refreshData()">
          Erneut versuchen
        </button>
      </div>
      
      <!-- Content -->
      <div *ngIf="!(dashboardData$ | async)?.isLoading && !(dashboardData$ | async)?.hasError"
           class="dashboard-content">
        
        <!-- Top Section: Statistics and Quick Actions -->
        <div class="dashboard-top">
          <!-- Statistics Cards -->
          <app-dashboard-stats
            class="stats-section"
            [metrics]="(dashboardData$ | async)?.metrics!">
          </app-dashboard-stats>
          
          <!-- Quick Actions -->
          <mat-card class="quick-actions">
            <mat-card-header>
              <mat-card-title>Schnellzugriff</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="action-buttons">
                <button mat-raised-button color="primary" (click)="createIncident()" class="primary-action">
                  <mat-icon>add_alert</mat-icon>
                  Vorfall erstellen
                </button>
                <button mat-raised-button color="accent" (click)="createApplication()">
                  <mat-icon>add</mat-icon>
                  Anwendung hinzufügen
                </button>
                <button mat-raised-button (click)="viewIncidents()">
                  <mat-icon>list</mat-icon>
                  Alle Vorfälle
                </button>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
        
        <!-- Data Lists -->
        <div class="dashboard-lists">
          <app-active-incidents-list
            class="incidents-section"
            [incidents]="(dashboardData$ | async)?.activeIncidents || []">
          </app-active-incidents-list>
          
          <app-recent-applications-list
            class="applications-section"
            [applications]="(dashboardData$ | async)?.recentApplications || []">
          </app-recent-applications-list>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .dashboard-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 1rem;
    }

    .dashboard-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 2rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid var(--border-color);
    }

    .dashboard-header div {
      flex: 1;
    }

    .dashboard-header h1 {
      margin: 0 0 0.5rem 0;
      font-size: 1.75rem;
      font-weight: 600;
      color: var(--text-primary);
      letter-spacing: -0.025em;
    }

    .dashboard-header p {
      margin: 0;
      color: var(--text-secondary);
      font-size: 1rem;
      font-weight: 400;
    }

    .refresh-button {
      margin-left: 1rem;
      color: var(--funk-blue);
    }

    .refresh-button:disabled {
      color: var(--text-secondary);
    }

    .spinning {
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    .loading-container, .error-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 3rem;
      text-align: center;
    }

    .loading-container p {
      margin-top: 1rem;
      color: var(--text-secondary);
    }

    .error-container {
      color: var(--error-color);
    }

    .error-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
      margin-bottom: 1rem;
      color: var(--error-color);
    }

    .error-container p {
      margin: 0 0 1rem 0;
      color: var(--text-primary);
    }

    .dashboard-content {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
    }

    .dashboard-top {
      display: grid;
      grid-template-columns: 1fr auto;
      gap: 1.5rem;
      align-items: start;
    }

    .stats-section {
      grid-column: 1;
    }

    .dashboard-lists {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1.5rem;
    }

    .incidents-section {
      grid-column: 1;
    }

    .applications-section {
      grid-column: 2;
    }

    .quick-actions {
      grid-column: 2;
      border: 1px solid var(--border-color);
      box-shadow: none;
      border-radius: var(--border-radius-md);
      background-color: var(--funk-white);
      min-width: 280px;
      max-width: 320px;
    }

    .quick-actions mat-card-title {
      font-size: 1rem;
      font-weight: 600;
      color: var(--funk-blue);
      margin: 0;
    }

    .action-buttons {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
    }

    .action-buttons button {
      justify-content: flex-start;
      gap: 0.75rem;
      padding: 0.875rem 1rem;
      font-size: 0.875rem;
      font-weight: 500;
      border-radius: var(--border-radius-sm);
      transition: all 0.2s ease;
      min-height: 44px;
      width: 100%;
    }

    .action-buttons button mat-icon {
      font-size: 1.125rem;
      width: 1.125rem;
      height: 1.125rem;
    }

    .action-buttons .primary-action {
      background-color: #1976d2;
      color: white;
      border: none;
    }

    .action-buttons .primary-action:hover {
      background-color: #1565c0;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(25, 118, 210, 0.3);
    }

    .action-buttons .primary-action mat-icon {
      color: white;
    }

    .action-buttons button:not(.primary-action) {
      background-color: var(--funk-white);
      color: var(--funk-blue);
      border: 1px solid var(--border-color);
    }

    .action-buttons button:not(.primary-action):hover {
      background-color: var(--funk-light-blue);
      color: var(--funk-white);
      border-color: var(--funk-light-blue);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .action-buttons button:not(.primary-action) mat-icon {
      color: var(--funk-blue);
    }

    .action-buttons button:not(.primary-action):hover mat-icon {
      color: var(--funk-white);
    }

    /* Responsive adjustments */
    @media (max-width: 1024px) {
      .dashboard-top {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .quick-actions {
        grid-column: 1;
        max-width: none;
        min-width: auto;
      }

      .action-buttons {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 0.75rem;
      }

      .action-buttons button {
        justify-content: center;
      }
    }

    @media (max-width: 768px) {
      .dashboard-lists {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
      }

      .refresh-button {
        margin-left: 0;
        align-self: flex-end;
      }

      .dashboard-header h1 {
        font-size: 1.5rem;
      }

      .action-buttons {
        grid-template-columns: 1fr;
        gap: 0.5rem;
      }

      .action-buttons button {
        justify-content: flex-start;
      }
    }

    @media (max-width: 480px) {
      .dashboard-container {
        padding: 0.5rem;
      }

      .dashboard-header {
        margin-bottom: 1.5rem;
      }

      .action-buttons button {
        padding: 0.875rem;
        font-size: 0.8rem;
      }

      .action-buttons button mat-icon {
        font-size: 1rem;
        width: 1rem;
        height: 1rem;
      }
    }

    @media (min-width: 1200px) {
      .quick-actions {
        min-width: 300px;
        max-width: 350px;
      }
    }
  `]
})
export class DashboardComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private incidentsFacade = inject(IncidentsFacade);
  private applicationsFacade = inject(ApplicationsFacade);
  private router = inject(Router);
  private dialog = inject(MatDialog);
  private snackBar = inject(MatSnackBar);

  // Reactive data stream
  dashboardData$ = combineLatest([
    this.incidentsFacade.allIncidents$,
    this.applicationsFacade.applications$,
    this.incidentsFacade.isLoading$,
    this.applicationsFacade.loading$,
    this.incidentsFacade.error$,
    this.applicationsFacade.error$
  ]).pipe(
    takeUntil(this.destroy$),
    map(([incidents, applications, incidentsLoading, appsLoading, incidentsError, appsError]) => {
      return {
        incidents,
        applications,
        isLoading: incidentsLoading || appsLoading,
        hasError: !!(incidentsError || appsError),
        errorMessage: incidentsError || appsError || null,
        metrics: this.calculateMetrics(incidents, applications),
        activeIncidents: this.getActiveIncidents(incidents),
        recentApplications: this.getRecentApplications(applications)
      } as DashboardData;
    })
  );

  ngOnInit(): void {
    this.loadData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadData(): void {
    this.incidentsFacade.loadIncidents();
    this.applicationsFacade.loadApplications({ isDeleted: false });
  }

  refreshData(): void {
    this.incidentsFacade.clearIncidentsError();
    this.loadData();
  }

  private calculateMetrics(incidents: Incident[], applications: Application[]): DashboardMetrics {
    const now = new Date();
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    const activeIncidents = incidents.filter(i => !i.isResolved);
    const resolvedIncidents = incidents.filter(i => i.isResolved);
    const plannedMaintenance = incidents.filter(i =>
      i.type === IncidentType.WARTUNGSFENSTER && !i.isResolved
    );
    const recentIncidents = incidents.filter(i =>
      new Date(i.createdAt) >= sevenDaysAgo
    );
    const activeApplications = applications.filter(a => !a.isDeleted);
    
    return {
      totalIncidents: incidents.length,
      activeIncidents: activeIncidents.length,
      resolvedIncidents: resolvedIncidents.length,
      plannedMaintenance: plannedMaintenance.length,
      totalApplications: applications.length,
      activeApplications: activeApplications.length,
      recentActivity: recentIncidents.length
    };
  }

  private getActiveIncidents(incidents: Incident[]): Incident[] {
    return incidents
      .filter(incident => !incident.isResolved)
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 5);
  }

  private getRecentApplications(applications: Application[]): Application[] {
    return applications
      .filter(app => !app.isDeleted)
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 5);
  }

  // Dialog methods
  createIncident(): void {
    const dialogData: IncidentCreateDialogData = {
      title: 'Neuen Vorfall erstellen'
    };

    const dialogRef = this.dialog.open(IncidentCreateDialogComponent, {
      data: dialogData,
      width: '800px',
      maxWidth: '90vw',
      maxHeight: '90vh',
      disableClose: true,
      autoFocus: true,
      restoreFocus: true
    });

    dialogRef.afterClosed().pipe(takeUntil(this.destroy$)).subscribe(result => {
      if (result?.success) {
        this.showSuccessMessage('Vorfall wurde erfolgreich erstellt');
        // No need to reload - NgRx store is automatically updated by createIncidentSuccess action
      }
    });
  }

  createApplication(): void {
    const dialogRef = this.dialog.open(ApplicationCreateDialogComponent, {
      width: '500px',
      disableClose: true
    });

    dialogRef.afterClosed().pipe(takeUntil(this.destroy$)).subscribe(result => {
      if (result) {
        this.showSuccessMessage('Applikation erfolgreich erstellt');
      }
    });
  }

  viewIncidents(): void {
    this.router.navigate(['/incidents']);
  }

  private showSuccessMessage(message: string): void {
    this.snackBar.open(message, 'Schließen', {
      duration: 3000,
      horizontalPosition: 'center',
      verticalPosition: 'bottom'
    });
  }
}