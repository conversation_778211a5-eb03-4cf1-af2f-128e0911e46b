import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { ApplicationListComponent } from './components/application-list/application-list.component';
import { ApplicationDetailComponent } from './components/application-detail/application-detail.component';

const routes: Routes = [
  {
    path: '',
    component: ApplicationListComponent,
    title: 'Applikationsverwaltung'
  },
  {
    path: ':id',
    component: ApplicationDetailComponent,
    title: 'Applikation Details'
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ApplicationManagementRoutingModule { }