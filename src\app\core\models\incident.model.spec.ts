import { Incident, IncidentType, CreateIncidentInput, UpdateIncidentInput } from './incident.model';
import { Application } from './application.model';

describe('Incident Model', () => {
  const mockApplication: Application = {
    identifier: 'app-123',
    name: 'Test Application',
    description: 'Test Description',
    isDeleted: false,
    createdAt: '2024-01-01T10:00:00.000Z',
    updatedAt: '2024-01-01T10:00:00.000Z'
  };

  describe('Incident Interface', () => {
    it('should accept Date objects for time fields', () => {
      const startTime = new Date('2024-01-15T09:00:00.000Z');
      const plannedEndTime = new Date('2024-01-15T17:00:00.000Z');
      const actualEndTime = new Date('2024-01-15T16:30:00.000Z');
      const createdAt = new Date('2024-01-14T08:00:00.000Z');
      const updatedAt = new Date('2024-01-15T16:30:00.000Z');

      const incident: Incident = {
        identifier: 'inc-123',
        title: 'Test Incident',
        type: IncidentType.STOERUNG,
        description: 'Test incident description',
        startTime,
        plannedEndTime,
        actualEndTime,
        alternatives: 'Use backup system',
        isResolved: true,
        createdAt,
        updatedAt,
        applications: [mockApplication]
      };

      expect(incident.startTime).toBeInstanceOf(Date);
      expect(incident.plannedEndTime).toBeInstanceOf(Date);
      expect(incident.actualEndTime).toBeInstanceOf(Date);
      expect(incident.createdAt).toBeInstanceOf(Date);
      expect(incident.updatedAt).toBeInstanceOf(Date);
    });

    it('should accept ISO strings for time fields', () => {
      const incident: Incident = {
        identifier: 'inc-123',
        title: 'Test Incident',
        type: IncidentType.STOERUNG,
        description: 'Test incident description',
        startTime: '2024-01-15T09:00:00.000Z',
        plannedEndTime: '2024-01-15T17:00:00.000Z',
        actualEndTime: '2024-01-15T16:30:00.000Z',
        alternatives: 'Use backup system',
        isResolved: true,
        createdAt: '2024-01-14T08:00:00.000Z',
        updatedAt: '2024-01-15T16:30:00.000Z',
        applications: [mockApplication]
      };

      expect(typeof incident.startTime).toBe('string');
      expect(typeof incident.plannedEndTime).toBe('string');
      expect(typeof incident.actualEndTime).toBe('string');
      expect(typeof incident.createdAt).toBe('string');
      expect(typeof incident.updatedAt).toBe('string');
    });

    it('should handle optional time fields', () => {
      const incident: Incident = {
        identifier: 'inc-123',
        title: 'Test Incident',
        type: IncidentType.WARTUNGSFENSTER,
        startTime: new Date('2024-01-15T09:00:00.000Z'),
        isResolved: false,
        createdAt: new Date('2024-01-14T08:00:00.000Z'),
        updatedAt: new Date('2024-01-14T08:00:00.000Z'),
        applications: []
      };

      expect(incident.plannedEndTime).toBeUndefined();
      expect(incident.actualEndTime).toBeUndefined();
      expect(incident.description).toBeUndefined();
      expect(incident.alternatives).toBeUndefined();
    });

    it('should support all IncidentType values', () => {
      const stoerungIncident: Incident = {
        identifier: 'inc-1',
        title: 'Störung',
        type: IncidentType.STOERUNG,
        startTime: new Date(),
        isResolved: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        applications: []
      };

      const wartungIncident: Incident = {
        identifier: 'inc-2',
        title: 'Wartung',
        type: IncidentType.WARTUNGSFENSTER,
        startTime: new Date(),
        isResolved: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        applications: []
      };

      const keineStoerungIncident: Incident = {
        identifier: 'inc-3',
        title: 'Keine Störung',
        type: IncidentType.KEINE_STOERUNG,
        startTime: new Date(),
        isResolved: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        applications: []
      };

      expect(stoerungIncident.type).toBe(IncidentType.STOERUNG);
      expect(wartungIncident.type).toBe(IncidentType.WARTUNGSFENSTER);
      expect(keineStoerungIncident.type).toBe(IncidentType.KEINE_STOERUNG);
    });
  });

  describe('CreateIncidentInput Interface', () => {
    it('should accept Date objects for time fields', () => {
      const startTime = new Date('2024-01-15T09:00:00.000Z');
      const plannedEndTime = new Date('2024-01-15T17:00:00.000Z');

      const input: CreateIncidentInput = {
        title: 'New Incident',
        type: IncidentType.STOERUNG,
        description: 'New incident description',
        startTime,
        plannedEndTime,
        alternatives: 'Use backup',
        applicationIds: ['app-123', 'app-456']
      };

      expect(input.startTime).toBeInstanceOf(Date);
      expect(input.plannedEndTime).toBeInstanceOf(Date);
    });

    it('should accept ISO strings for time fields', () => {
      const input: CreateIncidentInput = {
        title: 'New Incident',
        type: IncidentType.STOERUNG,
        description: 'New incident description',
        startTime: '2024-01-15T09:00:00.000Z',
        plannedEndTime: '2024-01-15T17:00:00.000Z',
        alternatives: 'Use backup',
        applicationIds: ['app-123', 'app-456']
      };

      expect(typeof input.startTime).toBe('string');
      expect(typeof input.plannedEndTime).toBe('string');
    });

    it('should handle required fields only', () => {
      const input: CreateIncidentInput = {
        title: 'Minimal Incident',
        type: IncidentType.WARTUNGSFENSTER,
        startTime: new Date('2024-01-15T09:00:00.000Z'),
        applicationIds: []
      };

      expect(input.title).toBe('Minimal Incident');
      expect(input.type).toBe(IncidentType.WARTUNGSFENSTER);
      expect(input.startTime).toBeInstanceOf(Date);
      expect(input.applicationIds).toEqual([]);
      expect(input.description).toBeUndefined();
      expect(input.plannedEndTime).toBeUndefined();
      expect(input.alternatives).toBeUndefined();
    });
  });

  describe('UpdateIncidentInput Interface', () => {
    it('should accept Date objects for time fields', () => {
      const startTime = new Date('2024-01-15T09:00:00.000Z');
      const plannedEndTime = new Date('2024-01-15T17:00:00.000Z');
      const actualEndTime = new Date('2024-01-15T16:30:00.000Z');

      const input: UpdateIncidentInput = {
        identifier: 'inc-123',
        title: 'Updated Incident',
        type: IncidentType.STOERUNG,
        description: 'Updated description',
        startTime,
        plannedEndTime,
        actualEndTime,
        alternatives: 'Updated alternatives',
        applicationIds: ['app-789']
      };

      expect(input.startTime).toBeInstanceOf(Date);
      expect(input.plannedEndTime).toBeInstanceOf(Date);
      expect(input.actualEndTime).toBeInstanceOf(Date);
    });

    it('should accept ISO strings for time fields', () => {
      const input: UpdateIncidentInput = {
        identifier: 'inc-123',
        startTime: '2024-01-15T09:00:00.000Z',
        plannedEndTime: '2024-01-15T17:00:00.000Z',
        actualEndTime: '2024-01-15T16:30:00.000Z'
      };

      expect(typeof input.startTime).toBe('string');
      expect(typeof input.plannedEndTime).toBe('string');
      expect(typeof input.actualEndTime).toBe('string');
    });

    it('should handle partial updates', () => {
      const input: UpdateIncidentInput = {
        identifier: 'inc-123',
        actualEndTime: new Date('2024-01-15T16:30:00.000Z')
      };

      expect(input.identifier).toBe('inc-123');
      expect(input.actualEndTime).toBeInstanceOf(Date);
      expect(input.title).toBeUndefined();
      expect(input.startTime).toBeUndefined();
      expect(input.plannedEndTime).toBeUndefined();
    });

    it('should require identifier field', () => {
      const input: UpdateIncidentInput = {
        identifier: 'inc-123'
      };

      expect(input.identifier).toBe('inc-123');
    });
  });

  describe('Type Safety', () => {
    it('should enforce IncidentType enum values', () => {
      // This test ensures TypeScript compilation catches invalid enum values
      const validTypes = [
        IncidentType.STOERUNG,
        IncidentType.WARTUNGSFENSTER,
        IncidentType.KEINE_STOERUNG
      ];

      validTypes.forEach(type => {
        const incident: Incident = {
          identifier: 'inc-test',
          title: 'Test',
          type,
          startTime: new Date(),
          isResolved: false,
          createdAt: new Date(),
          updatedAt: new Date(),
          applications: []
        };
        expect(Object.values(IncidentType)).toContain(incident.type);
      });
    });

    it('should enforce required fields in interfaces', () => {
      // This test ensures TypeScript compilation catches missing required fields
      const requiredIncidentFields = [
        'identifier', 'title', 'type', 'startTime', 'isResolved', 'createdAt', 'updatedAt', 'applications'
      ];

      const requiredCreateInputFields = [
        'title', 'type', 'startTime', 'applicationIds'
      ];

      const requiredUpdateInputFields = [
        'identifier'
      ];

      // These assertions help document the required fields
      expect(requiredIncidentFields.length).toBe(8);
      expect(requiredCreateInputFields.length).toBe(4);
      expect(requiredUpdateInputFields.length).toBe(1);
    });
  });
});