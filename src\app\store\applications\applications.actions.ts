import { createAction, props } from '@ngrx/store';
import { Application, CreateApplicationInput, UpdateApplicationInput, ApplicationFilter } from '../../core/models/application.model';

export const loadApplications = createAction(
  '[Applications] Load Applications',
  props<{ filter: ApplicationFilter }>()
);

export const loadApplicationsSuccess = createAction(
  '[Applications] Load Applications Success',
  props<{ applications: Application[] }>()
);

export const loadApplicationsFailure = createAction(
  '[Applications] Load Applications Failure',
  props<{ error: any }>()
);

export const selectApplication = createAction(
  '[Applications] Select Application',
  props<{ identifier: string }>()
);

export const createApplication = createAction(
  '[Applications] Create Application',
  props<{ application: CreateApplicationInput }>()
);

export const createApplicationSuccess = createAction(
  '[Applications] Create Application Success',
  props<{ application: Application }>()
);

export const createApplicationFailure = createAction(
  '[Applications] Create Application Failure',
  props<{ error: any }>()
);

export const updateApplication = createAction(
  '[Applications] Update Application',
  props<{ application: UpdateApplicationInput }>()
);

export const updateApplicationSuccess = createAction(
  '[Applications] Update Application Success',
  props<{ application: Application }>()
);

export const updateApplicationFailure = createAction(
  '[Applications] Update Application Failure',
  props<{ error: any }>()
);

export const deleteApplication = createAction(
  '[Applications] Delete Application',
  props<{ identifier: string }>()
);

export const deleteApplicationSuccess = createAction(
  '[Applications] Delete Application Success',
  props<{ identifier: string }>()
);

export const deleteApplicationFailure = createAction(
  '[Applications] Delete Application Failure',
  props<{ error: any }>()
);