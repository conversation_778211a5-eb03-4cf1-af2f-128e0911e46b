# Implementierungsplan: Applikationsverwaltung (Stammdatenpflege)

## Übersicht
Dieser Implementierungsplan basiert auf dem EPIC "Applikationsverwaltung" und bietet eine detaillierte Schritt-für-Schritt-Anleitung zur Umsetzung aller Features mit konkreten Checklisten und Akzeptanzkriterien.

**Referenz:** [`docs/02-EPIC-system-management.md`](docs/02-EPIC-system-management.md)  
**Geschätzte Gesamtdauer:** 3-5 Entwicklungstage  
**Priorität:** Hoch (Grundlage für alle anderen Features)

---

## Phase 1: Foundation Setup & Applikations-Liste (Tag 1-2)

### 1.1 Projekt-Setup und Grundstruktur

#### Verzeichnisstruktur erstellen
- [x] Feature-Module Verzeichnis: [`src/app/features/application-management/`](src/app/features/application-management/)
- [x] Components Verzeichnis: [`src/app/features/application-management/components/`](src/app/features/application-management/components/)
- [x] Dialogs Verzeichnis: [`src/app/features/application-management/dialogs/`](src/app/features/application-management/dialogs/)
- [x] Core Models Verzeichnis: [`src/app/core/models/`](src/app/core/models/)
- [x] Core Services Verzeichnis: [`src/app/core/services/`](src/app/core/services/)
- [x] GraphQL Verzeichnis: [`src/app/core/graphql/`](src/app/core/graphql/)

#### Angular Module Setup
- [x] Application Management Module: [`src/app/features/application-management/application-management.module.ts`](src/app/features/application-management/application-management.module.ts)
- [x] Routing Module: [`src/app/features/application-management/application-management-routing.module.ts`](src/app/features/application-management/application-management-routing.module.ts)
- [x] Module in App-Routing einbinden: [`src/app/app.routes.ts`](src/app/app.routes.ts)

### 1.2 Core Models und Interfaces

#### Application Model
- [x] Application Interface: [`src/app/core/models/application.model.ts`](src/app/core/models/application.model.ts)
  ```typescript
  export interface Application {
    identifier: string;
    name: string;
    description?: string;
    isDeleted: boolean;
    createdAt: string;
    updatedAt: string;
  }
  
  export interface CreateApplicationInput {
    name: string;
    description?: string;
  }
  
  export interface UpdateApplicationInput {
    identifier: string;
    name?: string;
    description?: string;
    isDeleted?: boolean;
  }
  ```

#### Incident Model (für Relations)
- [x] Incident Interface: [`src/app/core/models/incident.model.ts`](src/app/core/models/incident.model.ts)
- [x] IncidentType Enum: [`src/app/core/enums/incident-type.enum.ts`](src/app/core/enums/incident-type.enum.ts)

#### Change History Model
- [x] ChangeHistoryEntry Interface: [`src/app/core/models/change-history.model.ts`](src/app/core/models/change-history.model.ts)
- [x] EntityType Enum: [`src/app/core/enums/entity-type.enum.ts`](src/app/core/enums/entity-type.enum.ts)

### 1.3 GraphQL Integration

#### GraphQL Queries
- [x] Application Queries: [`src/app/core/graphql/application.queries.ts`](src/app/core/graphql/application.queries.ts)
  ```typescript
  export const GET_ALL_APPLICATIONS = gql`
    query GetAllApplications($isDeleted: Boolean = false) {
      allApplications(isDeleted: $isDeleted) {
        identifier
        name
        description
        isDeleted
        createdAt
        updatedAt
      }
    }
  `;
  
  export const GET_APPLICATION_BY_ID = gql`
    query GetApplication($identifier: ID!) {
      application(identifier: $identifier) {
        identifier
        name
        description
        isDeleted
        createdAt
        updatedAt
      }
    }
  `;
  
  export const SEARCH_APPLICATIONS_BY_NAME = gql`
    query SearchApplicationsByName($name: String!) {
      applicationsByName(name: $name) {
        identifier
        name
        description
        isDeleted
        createdAt
        updatedAt
      }
    }
  `;
  ```

#### GraphQL Mutations
- [x] Application Mutations: [`src/app/core/graphql/application.mutations.ts`](src/app/core/graphql/application.mutations.ts)
  ```typescript
  export const CREATE_APPLICATION = gql`
    mutation CreateApplication($name: String!, $description: String) {
      createApplication(name: $name, description: $description) {
        identifier
        name
        description
        isDeleted
        createdAt
        updatedAt
      }
    }
  `;
  
  export const UPDATE_APPLICATION = gql`
    mutation UpdateApplication($identifier: ID!, $name: String, $description: String, $isDeleted: Boolean) {
      updateApplication(identifier: $identifier, name: $name, description: $description, isDeleted: $isDeleted) {
        identifier
        name
        description
        isDeleted
        createdAt
        updatedAt
      }
    }
  `;
  
  export const DELETE_APPLICATION = gql`
    mutation DeleteApplication($identifier: ID!) {
      deleteApplication(identifier: $identifier) {
        identifier
        isDeleted
      }
    }
  `;
  ```

### 1.4 Application Service

#### Service Implementation
- [x] Application Service: [`src/app/core/services/application.service.ts`](src/app/core/services/application.service.ts)
  ```typescript
  @Injectable({
    providedIn: 'root'
  })
  export class ApplicationService {
    constructor(private apollo: Apollo) {}
    
    getAllApplications(isDeleted: boolean = false): Observable<Application[]>
    getApplicationById(identifier: string): Observable<Application>
    searchApplicationsByName(name: string): Observable<Application[]>
    createApplication(input: CreateApplicationInput): Observable<Application>
    updateApplication(input: UpdateApplicationInput): Observable<Application>
    deleteApplication(identifier: string): Observable<Application>
    checkDependencies(identifier: string): Observable<boolean>
  }
  ```

#### Service Features
- [x] Error Handling mit Apollo Error Link
- [x] Loading States Management
- [x] Caching-Strategien implementieren
- [x] Optimistic Updates für CRUD-Operationen

### 1.5 Application List Component

#### Component Struktur
- [x] Component: [`src/app/features/application-management/components/application-list/application-list.component.ts`](src/app/features/application-management/components/application-list/application-list.component.ts)
- [x] Template: [`src/app/features/application-management/components/application-list/application-list.component.html`](src/app/features/application-management/components/application-list/application-list.component.html)
- [x] Styling: [`src/app/features/application-management/components/application-list/application-list.component.scss`](src/app/features/application-management/components/application-list/application-list.component.scss)

#### Component Features
- [x] Standalone Component (Angular 19)
- [x] OnPush Change Detection Strategy
- [x] Angular Material Table mit MatTableDataSource
- [x] Sortierung für alle Spalten (MatSort)
- [x] Loading Spinner (MatProgressSpinner)
- [x] Error Handling mit MatSnackBar
- [x] Responsive Design mit Angular Flex Layout

#### Template Struktur
```html
<div class="application-list-container">
  <!-- Header mit Titel und Add-Button -->
  <div class="list-header">
    <h2>Applikationsverwaltung</h2>
    <button mat-raised-button color="primary" (click)="openAddDialog()">
      <mat-icon>add</mat-icon>
      Neue Applikation
    </button>
  </div>
  
  <!-- Filter und Suche -->
  <app-application-filter 
    (filterChange)="onFilterChange($event)"
    (searchChange)="onSearchChange($event)">
  </app-application-filter>
  
  <!-- Tabelle -->
  <div class="table-container">
    <table mat-table [dataSource]="dataSource" matSort>
      <!-- Spalten: Name, Beschreibung, Status, Erstellt, Aktualisiert, Aktionen -->
    </table>
  </div>
  
  <!-- Loading und Error States -->
  <div *ngIf="loading" class="loading-container">
    <mat-spinner></mat-spinner>
  </div>
</div>
```

### 1.6 Application Filter Component

#### Component Implementation
- [x] Filter Component: [`src/app/features/application-management/components/application-filter/application-filter.component.ts`](src/app/features/application-management/components/application-filter/application-filter.component.ts)
- [x] Filter für gelöschte/aktive Applikationen
- [x] Suchfeld mit Debounce (300ms)
- [x] Clear-Filter Funktionalität

#### Filter Features
- [x] Reactive Forms für Filter-Controls
- [x] RxJS Operators: debounceTime, distinctUntilChanged
- [x] Event Emitter für Parent Communication
- [x] Accessibility: ARIA-Labels, Keyboard Navigation

---

## Phase 2: CRUD Operations & Dialogs (Tag 2-3)

### 2.1 Application Form Component

#### Shared Form Component
- [x] Form Component: [`src/app/features/application-management/components/application-form/application-form.component.ts`](src/app/features/application-management/components/application-form/application-form.component.ts)
- [x] Reactive Forms mit FormBuilder
- [x] Custom Validators für Business Logic
- [x] Input/Output Properties für Wiederverwendbarkeit

#### Form Validation
- [x] Name: Required, MinLength(2), MaxLength(100)
- [x] Name Uniqueness Validator (Async)
- [x] Description: MaxLength(500)
- [x] Real-time Validation Feedback
- [x] Accessibility: Error Messages, ARIA-Describedby

### 2.2 Add Application Dialog

#### Dialog Implementation
- [x] Add Dialog: [`src/app/features/application-management/dialogs/add-application-dialog/add-application-dialog.component.ts`](src/app/features/application-management/dialogs/add-application-dialog/add-application-dialog.component.ts)
- [x] MatDialog Integration
- [x] Form Submission mit Loading State
- [x] Success/Error Handling
- [x] Dialog Result für Parent Communication

#### Dialog Features
- [x] Responsive Dialog Size
- [x] Escape Key Handling
- [x] Focus Management
- [x] Form Reset bei Dialog Close

### 2.3 Edit Application Dialog

#### Dialog Implementation
- [x] Edit Dialog: [`src/app/features/application-management/dialogs/edit-application-dialog/edit-application-dialog.component.ts`](src/app/features/application-management/dialogs/edit-application-dialog/edit-application-dialog.component.ts)
- [x] Pre-populate Form mit bestehenden Daten
- [x] Dirty State Detection
- [x] Unsaved Changes Warning
- [x] Optimistic Updates

### 2.4 Delete Confirmation Dialog

#### Shared Delete Dialog
- [x] Delete Dialog: [`src/app/shared/components/delete-confirmation-dialog/delete-confirmation-dialog.component.ts`](src/app/shared/components/delete-confirmation-dialog/delete-confirmation-dialog.component.ts)
- [x] Dependency Check Integration
- [x] Warning Messages bei bestehenden Relations
- [x] Soft Delete Confirmation
- [x] Wiederverwendbar für andere Entitäten

#### Dependency Check Service
- [x] Service für Abhängigkeitsprüfung: [`src/app/core/services/dependency-check.service.ts`](src/app/core/services/dependency-check.service.ts)
- [x] Mock Implementation für incidentsByApplication
- [x] Warning Messages bei verknüpften Incidents
- [x] Force Delete Option implementiert

### 2.5 Notification System

#### Success/Error Notifications
- [x] MatSnackBar Integration
- [x] SnackBar Notification Service: [`src/app/core/services/snackbar-notification.service.ts`](src/app/core/services/snackbar-notification.service.ts)
- [x] Standardisierte Messages für Application Management
- [x] Action Buttons (z.B. "Undo" für Delete)
- [x] Accessibility: Screen Reader Support

---

## Phase 3: Details & Navigation (Tag 3-4)

### 3.1 Application Detail Component

#### Detail View Implementation
- [x] Detail Component: [`src/app/features/application-management/components/application-detail/application-detail.component.ts`](src/app/features/application-management/components/application-detail/application-detail.component.ts)
- [x] Route Parameter Handling
- [x] Application Data Loading
- [x] Edit/Delete Actions
- [x] Breadcrumb Navigation

#### Detail View Features
- [x] Responsive Layout mit Angular Material Cards
- [x] Tabs für verschiedene Sections
- [x] Loading Skeleton für bessere UX
- [x] Error Handling bei nicht gefundener Application

### 3.2 Related Incidents Component

#### Incidents List
- [x] Related Incidents: [`src/app/features/application-management/components/related-incidents/related-incidents.component.ts`](src/app/features/application-management/components/related-incidents/related-incidents.component.ts)
- [x] Mock Implementation für incidentsByApplication
- [x] Mini-Table mit wichtigsten Incident-Infos
- [x] Navigation zu Incident-Details (vorbereitet)
- [x] Lazy Loading für Performance

### 3.3 Change History Component

#### Audit Trail
- [x] Change History: [`src/app/features/application-management/components/change-history/change-history.component.ts`](src/app/features/application-management/components/change-history/change-history.component.ts)
- [x] Timeline-View für Changes
- [x] User Information und Timestamps
- [x] Change Description Formatting
- [x] Pagination für große History

### 3.4 Routing Configuration

#### Route Setup
- [x] Application Management Routes: [`src/app/features/application-management/application-management-routing.module.ts`](src/app/features/application-management/application-management-routing.module.ts)
  ```typescript
  const routes: Routes = [
    {
      path: '',
      component: ApplicationListComponent,
      title: 'Applikationsverwaltung'
    },
    {
      path: ':id',
      component: ApplicationDetailComponent,
      title: 'Applikation Details'
    }
  ];
  ```

#### Navigation Features
- [x] Deep Linking Support
- [x] Browser Back/Forward Handling
- [x] Route Guards für Validation (basic implementation)
- [x] Breadcrumb Service Integration (basic implementation)

---

## Phase 4: Testing & Optimierung (Tag 4-5)

### 4.1 Unit Tests

#### Component Tests
- [ ] Application List Component Tests
- [ ] Application Form Component Tests
- [ ] Application Detail Component Tests
- [ ] Filter Component Tests
- [ ] Dialog Component Tests

#### Service Tests
- [ ] Application Service Tests
- [ ] GraphQL Operations Tests
- [ ] Error Handling Tests
- [ ] Caching Tests

#### Test Setup
- [ ] Apollo Testing Module Setup
- [ ] Mock GraphQL Responses
- [ ] Component Test Harnesses
- [ ] Accessibility Tests

### 4.2 Integration Tests

#### E2E Test Scenarios
- [ ] Complete CRUD Workflow
- [ ] Search and Filter Functionality
- [ ] Navigation between Views
- [ ] Error Scenarios
- [ ] Responsive Design Tests

### 4.3 Performance Optimierung

#### Performance Measures
- [ ] OnPush Change Detection
- [ ] TrackBy Functions für ngFor
- [ ] Lazy Loading für Detail Views
- [ ] GraphQL Query Optimization
- [ ] Bundle Size Analysis

#### Accessibility
- [ ] ARIA Labels und Descriptions
- [ ] Keyboard Navigation
- [ ] Screen Reader Testing
- [ ] Color Contrast Validation
- [ ] Focus Management

---

## Akzeptanzkriterien Checkliste

### Funktionale Anforderungen
- [x] **Liste:** Tabellarische Darstellung aller Applikationen
- [x] **Filter:** Aktive/Gelöschte Applikationen filterbar
- [x] **Suche:** Live-Suche nach Applikationsname
- [x] **Sortierung:** Alle Spalten sortierbar
- [x] **Erstellen:** Neue Applikation mit Validierung
- [x] **Bearbeiten:** Bestehende Applikation editierbar
- [x] **Löschen:** Soft Delete mit Dependency Check
- [x] **Details:** Vollständige Applikationsinformationen
- [x] **Navigation:** Zwischen Liste und Details
- [x] **Relations:** Verknüpfte Incidents anzeigen

### Technische Anforderungen
- [x] **Performance:** Liste lädt < 500ms (Mock Implementation)
- [x] **Responsiveness:** Mobile und Desktop Support
- [x] **Accessibility:** WCAG 2.1 Compliance (grundlegend implementiert)
- [x] **Error Handling:** Aussagekräftige Fehlermeldungen
- [x] **Loading States:** Spinner und Skeleton Loading
- [x] **Validation:** Client-side und Server-side (vorbereitet)
- [x] **Caching:** Optimierte GraphQL Queries (vorbereitet)
- [ ] **Testing:** 80%+ Code Coverage (noch ausstehend)

### UX Anforderungen
- [x] **Feedback:** Success/Error Notifications
- [x] **Confirmation:** Bestätigungsdialoge für kritische Aktionen
- [x] **Navigation:** Intuitive Breadcrumbs (grundlegend)
- [x] **Search:** Debounced Search mit Clear-Option
- [x] **Forms:** Real-time Validation Feedback
- [x] **Loading:** Progressive Loading für bessere UX

---

## Risiken & Mitigation

### Technische Risiken
| Risiko | Wahrscheinlichkeit | Impact | Mitigation |
|--------|-------------------|--------|------------|
| GraphQL Schema Changes | Mittel | Hoch | TypeScript Interfaces, API Versioning |
| Performance bei vielen Applikationen | Niedrig | Mittel | Pagination, Virtual Scrolling |
| Concurrent Modifications | Niedrig | Mittel | Optimistic Locking, Change History |
| Browser Compatibility | Niedrig | Niedrig | Modern Browser Support, Polyfills |

### Projektrisiken
| Risiko | Wahrscheinlichkeit | Impact | Mitigation |
|--------|-------------------|--------|------------|
| API nicht verfügbar | Niedrig | Hoch | Mock Data, Offline Development |
| Design Changes | Mittel | Niedrig | Component-based Architecture |
| Scope Creep | Mittel | Mittel | Klare Phase-Abgrenzung |

---

## Definition of Done

### Code Quality
- [ ] TypeScript Strict Mode aktiviert
- [ ] ESLint und Prettier konfiguriert
- [ ] Alle Tests bestehen (Unit + Integration)
- [ ] Code Review durchgeführt
- [ ] Accessibility Tests bestanden

### Documentation
- [ ] README aktualisiert
- [ ] API Integration dokumentiert
- [ ] Component Documentation
- [ ] Deployment Guide

### Deployment
- [ ] Build erfolgreich
- [ ] E2E Tests bestanden
- [ ] Performance Tests bestanden
- [ ] Security Scan durchgeführt

---

**Status:** Phase 1-3 Implementiert, Phase 4 Testing ausstehend
**Nächster Schritt:** Phase 4 - Unit Tests und Integration Tests
**Verantwortlich:** Development Team
**Review:** Implementierung abgeschlossen, Testing erforderlich

## Implementierungsstatus

### ✅ Abgeschlossen
- **Phase 1:** Foundation Setup & Applikations-Liste (100%)
- **Phase 2:** CRUD Operations & Dialogs (95%)
- **Phase 3:** Details & Navigation (90%)

### 🔄 In Arbeit
- **Phase 4:** Testing & Optimierung (10%)

### 📋 Nächste Schritte
1. Unit Tests für alle Komponenten schreiben
2. Integration Tests implementieren
3. E2E Tests für kritische User Journeys
4. Performance-Optimierungen
5. Accessibility-Tests und Verbesserungen
6. Code Review und Dokumentation vervollständigen