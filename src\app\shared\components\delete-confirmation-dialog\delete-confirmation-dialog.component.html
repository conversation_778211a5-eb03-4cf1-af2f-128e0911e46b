<div class="modern-delete-dialog">
  <!-- Subtle header similar to normal dialogs -->
  <div class="dialog-header warning-header">
    <div class="header-content">
      <div class="header-icon warning-icon">
        <mat-icon>warning_amber</mat-icon>
      </div>
      <div class="header-text">
        <h2 mat-dialog-title>{{ data.title }}</h2>
        <p class="header-subtitle">Diese Aktion kann nicht rückgängig gemacht werden</p>
      </div>
    </div>
    <button
      mat-icon-button
      class="close-button"
      (click)="onCancel()"
      [disabled]="loading"
      aria-label="Dialog schließen"
      matTooltip="Schließen">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <mat-dialog-content class="dialog-content">
    <div class="delete-content">
      <!-- Main confirmation message -->
      <div class="confirmation-section">
        <div class="confirmation-icon">
          <mat-icon>help_outline</mat-icon>
        </div>
        <p class="confirmation-message">{{ data.message }}</p>
      </div>
      
      <!-- Legacy warning message -->
      <div *ngIf="data.warningMessage" class="warning-alert">
        <mat-icon>info_outline</mat-icon>
        <div class="warning-content">
          <span class="warning-title">Wichtiger Hinweis</span>
          <span class="warning-message">{{ data.warningMessage }}</span>
        </div>
      </div>

      <!-- Dependency Check Section -->
      <div class="dependency-section">
        <!-- Loading Dependencies -->
        <div *ngIf="checkingDependencies" class="dependency-loading">
          <div class="loading-content">
            <mat-spinner diameter="24"></mat-spinner>
            <div class="loading-text">
              <span class="loading-title">Abhängigkeiten werden überprüft</span>
              <span class="loading-subtitle">Bitte warten Sie einen Moment...</span>
            </div>
          </div>
        </div>

        <!-- Dependency Results -->
        <div *ngIf="!checkingDependencies && dependencyResult" class="dependency-results">
          <div class="dependency-summary" [class]="'dependency-' + getDependencySeverity(dependencyResult.dependencies)">
            <mat-icon [color]="getDependencyColor()">{{ getDependencyIcon() }}</mat-icon>
            <div class="summary-content">
              <span class="summary-title">Abhängigkeitsprüfung</span>
              <span class="summary-message">{{ getDependencySummary() }}</span>
            </div>
          </div>

          <!-- Warnings -->
          <mat-expansion-panel *ngIf="dependencyResult.dependencies.warnings.length > 0" class="modern-expansion-panel warnings-panel">
            <mat-expansion-panel-header>
              <mat-panel-title>
                <div class="panel-title-content">
                  <mat-icon color="accent">warning</mat-icon>
                  <span>Warnungen ({{ dependencyResult.dependencies.warnings.length }})</span>
                </div>
              </mat-panel-title>
            </mat-expansion-panel-header>
            <div class="panel-content">
              <div class="warning-list">
                <div *ngFor="let warning of dependencyResult.dependencies.warnings" class="warning-item">
                  <mat-icon>info</mat-icon>
                  <span>{{ warning }}</span>
                </div>
              </div>
            </div>
          </mat-expansion-panel>

          <!-- Blocking Reasons -->
          <mat-expansion-panel *ngIf="dependencyResult.dependencies.blockingReasons.length > 0" class="modern-expansion-panel blocking-panel" [expanded]="true">
            <mat-expansion-panel-header>
              <mat-panel-title>
                <div class="panel-title-content">
                  <mat-icon color="warn">block</mat-icon>
                  <span>Blockierende Gründe ({{ dependencyResult.dependencies.blockingReasons.length }})</span>
                </div>
              </mat-panel-title>
            </mat-expansion-panel-header>
            <div class="panel-content">
              <div class="blocking-list">
                <div *ngFor="let reason of dependencyResult.dependencies.blockingReasons" class="blocking-item">
                  <mat-icon color="warn">error</mat-icon>
                  <span>{{ reason }}</span>
                </div>
              </div>
            </div>
          </mat-expansion-panel>

          <!-- Force Delete Option -->
          <div *ngIf="showForceDeleteOption" class="force-delete-section">
            <div class="force-delete-checkbox">
              <mat-checkbox
                [(ngModel)]="forceDelete"
                (change)="onForceDeleteChange()"
                color="warn">
                <span class="checkbox-label">Trotzdem löschen (Force Delete)</span>
              </mat-checkbox>
            </div>
            
            <div *ngIf="forceDelete" class="force-delete-warning">
              <div class="danger-alert">
                <mat-icon>dangerous</mat-icon>
                <div class="danger-content">
                  <span class="danger-title">Achtung: Force Delete aktiviert!</span>
                  <p class="danger-message">Dies wird die Applikation "{{ data.entityName }}" und alle verknüpften Daten unwiderruflich löschen.</p>
                  
                  <div class="confirmation-checkbox">
                    <mat-checkbox
                      [(ngModel)]="confirmForceDelete"
                      color="warn">
                      <span class="confirm-label">Ich verstehe die Konsequenzen und möchte trotzdem löschen</span>
                    </mat-checkbox>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Legacy Dependencies Warning -->
      <div *ngIf="data.hasDependendencies && !dependencyResult" class="dependencies-warning">
        <mat-icon>link</mat-icon>
        <div class="dependencies-content">
          <span class="dependencies-title">Verknüpfte Einträge gefunden</span>
          <span class="dependencies-message">
            Diese {{ data.entityType }} hat verknüpfte Einträge.
            Das Löschen wird diese als "Soft Delete" markieren.
          </span>
        </div>
      </div>
    </div>
  </mat-dialog-content>

  <!-- Enhanced action bar -->
  <div class="dialog-actions">
    <div class="actions-content">
      <button mat-stroked-button
              class="cancel-button"
              (click)="onCancel()"
              [disabled]="loading">
        <mat-icon>close</mat-icon>
        Abbrechen
      </button>
      
      <button mat-flat-button
              [color]="confirmButtonColor"
              class="delete-button"
              (click)="onConfirm()"
              [disabled]="loading || !canDelete || (forceDelete && !confirmForceDelete)">
        <div class="button-content">
          <mat-spinner *ngIf="loading" diameter="18"></mat-spinner>
          <mat-icon *ngIf="!loading">{{ forceDelete ? 'dangerous' : 'delete_forever' }}</mat-icon>
          <span>{{ loading ? 'Wird gelöscht...' : confirmButtonText }}</span>
        </div>
      </button>
    </div>
  </div>
</div>