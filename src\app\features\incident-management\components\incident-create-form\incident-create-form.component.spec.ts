import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { Component, DebugElement } from '@angular/core';
import { By } from '@angular/platform-browser';

import { IncidentCreateFormComponent } from './incident-create-form.component';
import { DateTimePickerComponent } from '../../../../shared/components/datetime-picker/datetime-picker.component';
import { ApplicationSelectorComponent } from '../application-selector/application-selector.component';
import { CreateIncidentInput, IncidentType } from '../../../../core/models/incident.model';

// Mock ApplicationSelectorComponent
@Component({
  selector: 'app-application-selector',
  template: '<div></div>',
  standalone: true
})
class MockApplicationSelectorComponent {
  writeValue(value: any): void {}
  registerOnChange(fn: any): void {}
  registerOnTouched(fn: any): void {}
  setDisabledState(isDisabled: boolean): void {}
}

describe('IncidentCreateFormComponent', () => {
  let component: IncidentCreateFormComponent;
  let fixture: ComponentFixture<IncidentCreateFormComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        IncidentCreateFormComponent,
        ReactiveFormsModule,
        NoopAnimationsModule,
        DateTimePickerComponent
      ],
      providers: []
    })
    .overrideComponent(IncidentCreateFormComponent, {
      remove: { imports: [ApplicationSelectorComponent] },
      add: { imports: [MockApplicationSelectorComponent] }
    })
    .compileComponents();

    fixture = TestBed.createComponent(IncidentCreateFormComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with default values', () => {
    expect(component.form).toBeDefined();
    expect(component.titleControl?.value).toBe('');
    expect(component.typeControl?.value).toBe(IncidentType.STOERUNG);
    expect(component.descriptionControl?.value).toBe('');
    expect(component.startTimeControl?.value).toBeInstanceOf(Date);
    expect(component.plannedEndTimeControl?.value).toBeNull();
    expect(component.alternativesControl?.value).toBe('');
    expect(component.applicationIdsControl?.value).toEqual([]);
  });

  it('should render DateTime picker components', () => {
    const dateTimePickers = fixture.debugElement.queryAll(By.directive(DateTimePickerComponent));
    expect(dateTimePickers.length).toBe(2);
  });

  it('should have required validation on title field', () => {
    const titleControl = component.titleControl;
    expect(titleControl?.hasError('required')).toBeTruthy();
    
    titleControl?.setValue('Test Title');
    expect(titleControl?.hasError('required')).toBeFalsy();
  });

  it('should have required validation on startTime field', () => {
    const startTimeControl = component.startTimeControl;
    startTimeControl?.setValue(null);
    expect(startTimeControl?.hasError('required')).toBeTruthy();
    
    startTimeControl?.setValue(new Date());
    expect(startTimeControl?.hasError('required')).toBeFalsy();
  });

  it('should validate plannedEndTime is after startTime', () => {
    const startTime = new Date('2024-01-01T10:00:00');
    const endTimeBefore = new Date('2024-01-01T09:00:00');
    const endTimeAfter = new Date('2024-01-01T11:00:00');

    component.startTimeControl?.setValue(startTime);
    
    // Test invalid case: end time before start time
    component.plannedEndTimeControl?.setValue(endTimeBefore);
    component.plannedEndTimeControl?.updateValueAndValidity();
    expect(component.plannedEndTimeControl?.hasError('plannedEndTimeBeforeStart')).toBeTruthy();
    
    // Test valid case: end time after start time
    component.plannedEndTimeControl?.setValue(endTimeAfter);
    component.plannedEndTimeControl?.updateValueAndValidity();
    expect(component.plannedEndTimeControl?.hasError('plannedEndTimeBeforeStart')).toBeFalsy();
  });

  it('should allow null plannedEndTime (optional field)', () => {
    component.plannedEndTimeControl?.setValue(null);
    component.plannedEndTimeControl?.updateValueAndValidity();
    expect(component.plannedEndTimeControl?.valid).toBeTruthy();
  });

  it('should emit formSubmit with correct data when form is valid', () => {
    spyOn(component.formSubmit, 'emit');
    
    const testData = {
      title: 'Test Incident',
      type: IncidentType.STOERUNG,
      description: 'Test Description',
      startTime: new Date('2024-01-01T10:00:00'),
      plannedEndTime: new Date('2024-01-01T12:00:00'),
      alternatives: 'Test Alternatives',
      applicationIds: ['app1', 'app2']
    };

    component.titleControl?.setValue(testData.title);
    component.typeControl?.setValue(testData.type);
    component.descriptionControl?.setValue(testData.description);
    component.startTimeControl?.setValue(testData.startTime);
    component.plannedEndTimeControl?.setValue(testData.plannedEndTime);
    component.alternativesControl?.setValue(testData.alternatives);
    component.applicationIdsControl?.setValue(testData.applicationIds);

    component.onSubmit();

    expect(component.formSubmit.emit).toHaveBeenCalledWith({
      title: testData.title,
      type: testData.type,
      description: testData.description,
      startTime: testData.startTime.toISOString(),
      plannedEndTime: testData.plannedEndTime.toISOString(),
      alternatives: testData.alternatives,
      applicationIds: testData.applicationIds
    } as CreateIncidentInput);
  });

  it('should not emit formSubmit when form is invalid', () => {
    spyOn(component.formSubmit, 'emit');
    
    // Leave required fields empty
    component.titleControl?.setValue('');
    component.startTimeControl?.setValue(null);
    component.applicationIdsControl?.setValue([]);

    component.onSubmit();

    expect(component.formSubmit.emit).not.toHaveBeenCalled();
  });

  it('should emit formCancel when cancel is clicked', () => {
    spyOn(component.formCancel, 'emit');
    
    component.onCancel();
    
    expect(component.formCancel.emit).toHaveBeenCalled();
  });

  it('should mark all controls as touched when form is invalid on submit', () => {
    // Make form invalid
    component.titleControl?.setValue('');
    component.startTimeControl?.setValue(null);
    
    component.onSubmit();
    
    expect(component.titleControl?.touched).toBeTruthy();
    expect(component.startTimeControl?.touched).toBeTruthy();
  });

  it('should return correct error messages', () => {
    // Test title errors
    component.titleControl?.setValue('');
    component.titleControl?.markAsTouched();
    expect(component.getTitleErrorMessage()).toBe('Titel ist erforderlich');
    
    component.titleControl?.setValue('ab');
    expect(component.getTitleErrorMessage()).toBe('Titel muss mindestens 3 Zeichen lang sein');
    
    // Test start time errors
    component.startTimeControl?.setValue(null);
    component.startTimeControl?.markAsTouched();
    expect(component.getStartTimeErrorMessage()).toBe('Startzeit ist erforderlich');
    
    // Test planned end time errors
    const startTime = new Date('2024-01-01T10:00:00');
    const endTimeBefore = new Date('2024-01-01T09:00:00');
    
    component.startTimeControl?.setValue(startTime);
    component.plannedEndTimeControl?.setValue(endTimeBefore);
    component.plannedEndTimeControl?.markAsTouched();
    component.plannedEndTimeControl?.updateValueAndValidity();
    
    expect(component.getPlannedEndTimeErrorMessage()).toBe('Geplante Endzeit muss nach der Startzeit liegen');
  });

  it('should update plannedEndTime validation when startTime changes', () => {
    const startTime1 = new Date('2024-01-01T10:00:00');
    const startTime2 = new Date('2024-01-01T12:00:00');
    const endTime = new Date('2024-01-01T11:00:00');
    
    // Set initial values
    component.startTimeControl?.setValue(startTime1);
    component.plannedEndTimeControl?.setValue(endTime);
    component.plannedEndTimeControl?.updateValueAndValidity();
    
    // End time is after start time - should be valid
    expect(component.plannedEndTimeControl?.hasError('plannedEndTimeBeforeStart')).toBeFalsy();
    
    // Change start time to after end time
    component.startTimeControl?.setValue(startTime2);
    
    // Should trigger validation update and become invalid
    expect(component.plannedEndTimeControl?.hasError('plannedEndTimeBeforeStart')).toBeTruthy();
  });

  it('should display incident type names correctly', () => {
    expect(component.getIncidentTypeDisplayName(IncidentType.STOERUNG)).toBe('Störung');
    expect(component.getIncidentTypeDisplayName(IncidentType.WARTUNGSFENSTER)).toBe('Wartungsfenster');
    expect(component.getIncidentTypeDisplayName(IncidentType.KEINE_STOERUNG)).toBe('Keine Störung');
  });

  it('should handle form submission with minimal required data', () => {
    spyOn(component.formSubmit, 'emit');
    
    const startTime = new Date('2024-01-01T10:00:00');
    
    // Set only required fields
    component.titleControl?.setValue('Minimal Test');
    component.startTimeControl?.setValue(startTime);
    component.applicationIdsControl?.setValue(['app1']);
    
    component.onSubmit();
    
    expect(component.formSubmit.emit).toHaveBeenCalledWith({
      title: 'Minimal Test',
      type: IncidentType.STOERUNG,
      startTime: startTime.toISOString(),
      applicationIds: ['app1']
    } as CreateIncidentInput);
  });

  it('should handle DateTime picker integration correctly', () => {
    const dateTimePickers = fixture.debugElement.queryAll(By.directive(DateTimePickerComponent));
    
    // Should have two DateTime pickers
    expect(dateTimePickers.length).toBe(2);
    
    // Check start time picker properties
    const startTimePicker = dateTimePickers[0].componentInstance;
    expect(startTimePicker.required).toBeTruthy();
    expect(startTimePicker.dateLabel).toBe('Datum');
    expect(startTimePicker.timeLabel).toBe('Uhrzeit');
    
    // Check planned end time picker properties
    const endTimePicker = dateTimePickers[1].componentInstance;
    expect(endTimePicker.required).toBeFalsy();
    expect(endTimePicker.dateLabel).toBe('Datum');
    expect(endTimePicker.timeLabel).toBe('Uhrzeit');
  });
});