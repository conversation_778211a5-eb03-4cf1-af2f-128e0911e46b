import { gql } from 'apollo-angular';

export const GET_ALL_APPLICATIONS = gql`
  query GetAllApplications {
    allApplications {
      identifier
      name
      description
      isDeleted
      createdAt
      updatedAt
    }
  }
`;

export const GET_APPLICATION_BY_ID = gql`
  query GetApplication($identifier: UUID!) {
    application(identifier: $identifier) {
      identifier
      name
      description
      isDeleted
      createdAt
      updatedAt
    }
  }
`;

export const SEARCH_APPLICATIONS_BY_NAME = gql`
  query SearchApplicationsByName($name: String!) {
    applicationsByName(name: $name) {
      identifier
      name
      description
      isDeleted
      createdAt
      updatedAt
    }
  }
`;

export const CHECK_APPLICATION_DEPENDENCIES = gql`
  query CheckApplicationDependencies($identifier: UUID!) {
    applicationDependencies(identifier: $identifier) {
      canDelete
      warningMessage
    }
  }
`;