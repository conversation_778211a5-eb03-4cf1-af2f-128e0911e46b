import { Application } from './application.model';

export enum IncidentType {
  STOERUNG = 'STOERUNG',
  WARTUNGSFENSTER = 'WARTUNGSFENSTER',
  KEINE_STOERUNG = 'KEINE_STOERUNG'
}

/**
 * Core Incident interface with full DateTime support
 * All time fields support both Date objects and ISO string representations
 */
export interface Incident {
  identifier: string;
  title: string;
  type: IncidentType;
  description?: string;
  /** Start time of the incident - supports Date object or ISO string */
  startTime: Date | string;
  /** Planned end time of the incident - supports Date object or ISO string */
  plannedEndTime?: Date | string;
  /** Actual end time when incident was resolved - supports Date object or ISO string */
  actualEndTime?: Date | string;
  alternatives?: string;
  isResolved: boolean;
  /** Creation timestamp - supports Date object or ISO string */
  createdAt: Date | string;
  /** Last update timestamp - supports Date object or ISO string */
  updatedAt: Date | string;
  applications: Application[];
}

/**
 * Input interface for creating new incidents
 * Accepts both Date objects and ISO strings for time fields
 */
export interface CreateIncidentInput {
  title: string;
  type: IncidentType;
  description?: string;
  /** Start time - accepts Date object or ISO string */
  startTime: Date | string;
  /** Planned end time - accepts Date object or ISO string */
  plannedEndTime?: Date | string;
  alternatives?: string;
  applicationIds: string[];
}

/**
 * Input interface for updating existing incidents
 * Accepts both Date objects and ISO strings for time fields
 */
export interface UpdateIncidentInput {
  identifier: string;
  title?: string;
  type?: IncidentType;
  description?: string;
  /** Start time - accepts Date object or ISO string */
  startTime?: Date | string;
  /** Planned end time - accepts Date object or ISO string */
  plannedEndTime?: Date | string;
  /** Actual end time - accepts Date object or ISO string */
  actualEndTime?: Date | string;
  alternatives?: string;
  applicationIds?: string[];
}

export interface IncidentFilter {
  isResolved?: boolean;
  searchTerm?: string;
  applicationType?: IncidentType;
}