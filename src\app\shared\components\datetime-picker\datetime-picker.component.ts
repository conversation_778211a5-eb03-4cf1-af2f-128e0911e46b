import { Component, Input, Output, EventEmitter, forwardRef, OnInit, On<PERSON><PERSON>roy, inject, LOCALE_ID } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR, FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Subject, takeUntil, combineLatest } from 'rxjs';
import { formatDateTimeForInput, formatDateForInput, createDateFromInputs, ensureDate } from '../../../core/utils/datetime.utils';

/**
 * DateTime-Picker Komponente für die Eingabe von Datum und Uhrzeit
 * 
 * Features:
 * - Angular Material DatePicker für Datum
 * - HTML5 Time Input für Uhrzeit
 * - Deutsche Lokalisierung
 * - Accessibility (WCAG 2.1)
 * - Reactive Forms Integration
 * - Validierung
 * - ControlValueAccessor für ngModel Support
 */
@Component({
  selector: 'app-datetime-picker',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => DateTimePickerComponent),
      multi: true
    }
  ],
  template: `
    <div class="datetime-picker-container" [class.datetime-picker-disabled]="disabled">
      <!-- Datum Picker -->
      <mat-form-field appearance="outline" class="date-field">
        <mat-label>{{ dateLabel }}</mat-label>
        <input
          matInput
          [matDatepicker]="datePicker"
          [formControl]="dateControl"
          [placeholder]="datePlaceholder"
          [attr.aria-label]="dateAriaLabel"
          [attr.aria-describedby]="dateErrorId"
          readonly>
        <mat-datepicker-toggle matIconSuffix [for]="datePicker" [disabled]="disabled">
          <mat-icon matDatepickerToggleIcon>calendar_today</mat-icon>
        </mat-datepicker-toggle>
        <mat-datepicker #datePicker [disabled]="disabled"></mat-datepicker>
        
        @if (dateControl.invalid && (dateControl.dirty || dateControl.touched)) {
          <mat-error [id]="dateErrorId">
            @if (dateControl.hasError('required')) {
              {{ requiredErrorMessage }}
            }
            @if (dateControl.hasError('matDatepickerParse')) {
              {{ invalidDateErrorMessage }}
            }
            @if (dateControl.hasError('matDatepickerMin')) {
              {{ minDateErrorMessage }}
            }
            @if (dateControl.hasError('matDatepickerMax')) {
              {{ maxDateErrorMessage }}
            }
          </mat-error>
        }
      </mat-form-field>

      <!-- Zeit Input -->
      <mat-form-field appearance="outline" class="time-field">
        <mat-label>{{ timeLabel }}</mat-label>
        <input
          matInput
          type="time"
          [formControl]="timeControl"
          [placeholder]="timePlaceholder"
          [attr.aria-label]="timeAriaLabel"
          [attr.aria-describedby]="timeErrorId"
          step="60">
        <mat-icon matSuffix>access_time</mat-icon>
        
        @if (timeControl.invalid && (timeControl.dirty || timeControl.touched)) {
          <mat-error [id]="timeErrorId">
            @if (timeControl.hasError('required')) {
              {{ requiredErrorMessage }}
            }
            @if (timeControl.hasError('pattern')) {
              {{ invalidTimeErrorMessage }}
            }
          </mat-error>
        }
      </mat-form-field>

      <!-- Clear Button -->
      @if (showClearButton && !disabled && (dateControl.value || timeControl.value)) {
        <button 
          mat-icon-button 
          type="button"
          class="clear-button"
          (click)="clear()"
          [attr.aria-label]="clearAriaLabel"
          matTooltip="{{ clearTooltip }}">
          <mat-icon>clear</mat-icon>
        </button>
      }
    </div>
  `,
  styles: [`
    .datetime-picker-container {
      display: flex;
      gap: 8px;
      align-items: flex-start;
      width: fit-content;
      max-width: 100%;
    }

    .date-field {
      flex: 0 0 160px;
      min-width: 140px;
      max-width: 180px;
    }

    .time-field {
      flex: 0 0 110px;
      min-width: 100px;
    }

    .clear-button {
      margin-top: 8px;
      flex-shrink: 0;
    }

    .datetime-picker-disabled {
      opacity: 0.6;
      pointer-events: none;
    }

    /* Responsive Design */
    @media (max-width: 600px) {
      .datetime-picker-container {
        flex-direction: column;
        gap: 8px;
        width: 100%;
      }

      .date-field,
      .time-field {
        flex: 1;
        max-width: none;
      }
    }

    /* High Contrast Mode Support */
    @media (prefers-contrast: high) {
      .datetime-picker-container {
        border: 2px solid;
      }
    }

    /* Reduced Motion Support */
    @media (prefers-reduced-motion: reduce) {
      * {
        transition: none !important;
        animation: none !important;
      }
    }
  `]
})
export class DateTimePickerComponent implements ControlValueAccessor, OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();
  private readonly locale = inject(LOCALE_ID);

  // Form Controls
  readonly dateControl = new FormControl<Date | null>(null);
  readonly timeControl = new FormControl<string>('', [
    Validators.pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
  ]);

  // Input Properties
  @Input() dateLabel = 'Datum';
  @Input() timeLabel = 'Uhrzeit';
  @Input() datePlaceholder = 'TT.MM.JJJJ';
  @Input() timePlaceholder = 'HH:MM';
  @Input() required = false;
  @Input() disabled = false;
  @Input() minDate: Date | null = null;
  @Input() maxDate: Date | null = null;
  @Input() showClearButton = true;

  // Accessibility Labels
  @Input() dateAriaLabel = 'Datum auswählen';
  @Input() timeAriaLabel = 'Uhrzeit eingeben';
  @Input() clearAriaLabel = 'Datum und Uhrzeit löschen';
  @Input() clearTooltip = 'Löschen';

  // Error Messages
  @Input() requiredErrorMessage = 'Dieses Feld ist erforderlich';
  @Input() invalidDateErrorMessage = 'Ungültiges Datum';
  @Input() invalidTimeErrorMessage = 'Ungültige Uhrzeit (Format: HH:MM)';
  @Input() minDateErrorMessage = 'Datum liegt vor dem erlaubten Minimum';
  @Input() maxDateErrorMessage = 'Datum liegt nach dem erlaubten Maximum';

  // Output Events
  @Output() dateTimeChange = new EventEmitter<Date | null>();
  @Output() dateChange = new EventEmitter<Date | null>();
  @Output() timeChange = new EventEmitter<string>();

  // ControlValueAccessor
  private onChange = (value: Date | null) => {};
  private onTouched = () => {};

  // Unique IDs for accessibility
  readonly dateErrorId = `datetime-picker-date-error-${Math.random().toString(36).substr(2, 9)}`;
  readonly timeErrorId = `datetime-picker-time-error-${Math.random().toString(36).substr(2, 9)}`;

  ngOnInit(): void {
    this.setupValidation();
    this.setupValueChanges();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setupValidation(): void {
    // Set required validators if needed
    if (this.required) {
      this.dateControl.setValidators([Validators.required]);
      this.timeControl.setValidators([
        Validators.required,
        Validators.pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
      ]);
    }

    // Set min/max date validators
    if (this.minDate) {
      this.dateControl.addValidators(Validators.min(this.minDate.getTime()));
    }
    if (this.maxDate) {
      this.dateControl.addValidators(Validators.max(this.maxDate.getTime()));
    }

    this.dateControl.updateValueAndValidity();
    this.timeControl.updateValueAndValidity();
  }

  private setupValueChanges(): void {
    // Combine date and time changes
    combineLatest([
      this.dateControl.valueChanges,
      this.timeControl.valueChanges
    ]).pipe(
      takeUntil(this.destroy$)
    ).subscribe(([date, time]) => {
      this.onTouched();

      const combinedDateTime = this.combineDateTime(date, time || '');
      this.onChange(combinedDateTime);
      this.dateTimeChange.emit(combinedDateTime);
    });

    // Individual change events
    this.dateControl.valueChanges.pipe(
      takeUntil(this.destroy$)
    ).subscribe(date => {
      this.dateChange.emit(date);
    });

    this.timeControl.valueChanges.pipe(
      takeUntil(this.destroy$)
    ).subscribe(time => {
      this.timeChange.emit(time || '');
    });
  }

  private combineDateTime(date: Date | null, time: string): Date | null {
    if (!date) {
      return null;
    }

    try {
      const dateStr = formatDateForInput(date);
      // If no time is provided, use 00:00
      const timeStr = time || '00:00';
      return createDateFromInputs(dateStr, timeStr);
    } catch (error) {
      console.warn('Error combining date and time:', error);
      return null;
    }
  }

  // ControlValueAccessor Implementation
  writeValue(value: Date | string | null): void {
    if (value) {
      const date = ensureDate(value);
      
      // Set date part
      this.dateControl.setValue(date, { emitEvent: false });
      
      // Set time part (HH:MM format)
      const timeString = date.toTimeString().slice(0, 5);
      this.timeControl.setValue(timeString, { emitEvent: false });
    } else {
      this.dateControl.setValue(null, { emitEvent: false });
      this.timeControl.setValue('', { emitEvent: false });
    }
  }

  registerOnChange(fn: (value: Date | null) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
    
    if (isDisabled) {
      this.dateControl.disable({ emitEvent: false });
      this.timeControl.disable({ emitEvent: false });
    } else {
      this.dateControl.enable({ emitEvent: false });
      this.timeControl.enable({ emitEvent: false });
    }
  }

  // Public Methods
  clear(): void {
    this.dateControl.setValue(null);
    this.timeControl.setValue('');
    this.dateControl.markAsTouched();
    this.timeControl.markAsTouched();
  }

  focus(): void {
    // Focus the date input first
    const dateInput = document.querySelector(`#${this.dateErrorId}`)?.previousElementSibling as HTMLInputElement;
    dateInput?.focus();
  }

  /**
   * Validates the current input and returns validation errors
   */
  validate(): { [key: string]: any } | null {
    const errors: { [key: string]: any } = {};

    if (this.dateControl.errors) {
      errors['date'] = this.dateControl.errors;
    }

    if (this.timeControl.errors) {
      errors['time'] = this.timeControl.errors;
    }

    return Object.keys(errors).length > 0 ? errors : null;
  }

  /**
   * Gets the current combined DateTime value
   */
  get value(): Date | null {
    return this.combineDateTime(this.dateControl.value, this.timeControl.value || '');
  }

  /**
   * Checks if the component has any validation errors
   */
  get hasErrors(): boolean {
    return (this.dateControl.invalid && (this.dateControl.dirty || this.dateControl.touched)) ||
           (this.timeControl.invalid && (this.timeControl.dirty || this.timeControl.touched));
  }

  /**
   * Checks if the component is valid
   */
  get isValid(): boolean {
    return this.dateControl.valid && this.timeControl.valid;
  }
}