import { Injectable, inject } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { of } from 'rxjs';
import { map, exhaustMap, catchError } from 'rxjs/operators';
import { IncidentService } from '../../core/services/incident.service';
import * as IncidentsActions from './incidents.actions';

@Injectable()
export class IncidentsEffects {
  private actions$ = inject(Actions);
  private incidentService = inject(IncidentService);

  loadIncidents$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IncidentsActions.loadIncidents),
      exhaustMap(() =>
        this.incidentService.getAllIncidents().pipe(
          map(incidents => IncidentsActions.loadIncidentsSuccess({ incidents })),
          catchError(error => of(IncidentsActions.loadIncidentsFailure({ 
            error: error.message || 'Failed to load incidents' 
          })))
        )
      )
    )
  );

  loadMyIncidents$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IncidentsActions.loadMyIncidents),
      exhaustMap(action =>
        this.incidentService.getMyIncidents(action.isResolved).pipe(
          map(incidents => IncidentsActions.loadMyIncidentsSuccess({ incidents })),
          catchError(error => of(IncidentsActions.loadMyIncidentsFailure({
            error: error.message || 'Failed to load my incidents'
          })))
        )
      )
    )
  );

  createIncident$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IncidentsActions.createIncident),
      exhaustMap(action =>
        this.incidentService.createIncident(action.createInput).pipe(
          map(incident => IncidentsActions.createIncidentSuccess({ incident })),
          catchError(error => of(IncidentsActions.createIncidentFailure({
            error: error.message || 'Failed to create incident'
          })))
        )
      )
    )
  );

  updateIncident$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IncidentsActions.updateIncident),
      exhaustMap(action =>
        this.incidentService.updateIncident(action.updateInput).pipe(
          map(incident => IncidentsActions.updateIncidentSuccess({ incident })),
          catchError(error => of(IncidentsActions.updateIncidentFailure({
            error: error.message || 'Failed to update incident'
          })))
        )
      )
    )
  );

  deleteIncident$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IncidentsActions.deleteIncident),
      exhaustMap(action =>
        this.incidentService.deleteIncident(action.identifier).pipe(
          map(() => IncidentsActions.deleteIncidentSuccess({ identifier: action.identifier })),
          catchError(error => of(IncidentsActions.deleteIncidentFailure({
            error: error.message || 'Failed to delete incident'
          })))
        )
      )
    )
  );

  loadIncident$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IncidentsActions.loadIncident),
      exhaustMap(action =>
        this.incidentService.getIncidentById(action.id).pipe(
          map(incident => IncidentsActions.loadIncidentSuccess({ incident })),
          catchError(error => of(IncidentsActions.loadIncidentFailure({
            error: error.message || 'Failed to load incident'
          })))
        )
      )
    )
  );
}