import { Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { Application, CreateApplicationInput, UpdateApplicationInput, ApplicationFilter } from '../../core/models/application.model';
import * as ApplicationActions from './applications.actions';
import * as ApplicationSelectors from './applications.selectors';
import { ApplicationsState } from './applications.state';

@Injectable({
  providedIn: 'root',
})
export class ApplicationsFacade {
  applications$: Observable<Application[]>;
  selectedApplication$: Observable<Application | null>;
  loading$: Observable<boolean>;
  error$: Observable<any>;

  constructor(private store: Store<ApplicationsState>) {
    this.applications$ = this.store.select(
      ApplicationSelectors.selectAllApplications
    );
    this.selectedApplication$ = this.store.select(
      ApplicationSelectors.selectSelectedApplication
    );
    this.loading$ = this.store.select(
      ApplicationSelectors.selectApplicationsLoading
    );
    this.error$ = this.store.select(ApplicationSelectors.selectApplicationsError);
  }

  loadApplications(filter: ApplicationFilter): void {
    this.store.dispatch(ApplicationActions.loadApplications({ filter }));
  }

  selectApplication(identifier: string): void {
    this.store.dispatch(ApplicationActions.selectApplication({ identifier }));
  }

  createApplication(application: CreateApplicationInput): void {
    this.store.dispatch(ApplicationActions.createApplication({ application }));
  }

  updateApplication(application: UpdateApplicationInput): void {
    this.store.dispatch(ApplicationActions.updateApplication({ application }));
  }

  deleteApplication(identifier: string): void {
    this.store.dispatch(ApplicationActions.deleteApplication({ identifier }));
  }
}