import {
  toISOString,
  toDate,
  ensureDate,
  isValidISOString,
  formatDateTime,
  formatDateForInput,
  formatDateTimeForInput,
  createDateFromInputs,
  isPastDateTime,
  isFutureDateTime,
  calculateDuration,
  formatDuration
} from './datetime.utils';

describe('DateTime Utils', () => {
  const testDate = new Date('2024-01-15T14:30:45.123Z');
  const testISOString = '2024-01-15T14:30:45.123Z';

  describe('toISOString', () => {
    it('should convert Date object to ISO string', () => {
      const result = toISOString(testDate);
      expect(result).toBe(testISOString);
    });

    it('should return ISO string as-is', () => {
      const result = toISOString(testISOString);
      expect(result).toBe(testISOString);
    });
  });

  describe('toDate', () => {
    it('should convert ISO string to Date object', () => {
      const result = toDate(testISOString);
      expect(result).toBeInstanceOf(Date);
      expect(result.getTime()).toBe(testDate.getTime());
    });

    it('should handle different ISO string formats', () => {
      const isoWithoutMs = '2024-01-15T14:30:45Z';
      const result = toDate(isoWithoutMs);
      expect(result).toBeInstanceOf(Date);
      expect(result.getFullYear()).toBe(2024);
      expect(result.getMonth()).toBe(0); // January is 0
      expect(result.getDate()).toBe(15);
    });
  });

  describe('ensureDate', () => {
    it('should return Date object as-is', () => {
      const result = ensureDate(testDate);
      expect(result).toBe(testDate);
      expect(result).toBeInstanceOf(Date);
    });

    it('should convert ISO string to Date object', () => {
      const result = ensureDate(testISOString);
      expect(result).toBeInstanceOf(Date);
      expect(result.getTime()).toBe(testDate.getTime());
    });
  });

  describe('isValidISOString', () => {
    it('should return true for valid ISO strings', () => {
      const validStrings = [
        '2024-01-15T14:30:45.123Z',
        '2024-01-15T14:30:45Z',
        '2024-12-31T23:59:59.999Z'
      ];

      validStrings.forEach(str => {
        expect(isValidISOString(str)).toBe(true);
      });
    });

    it('should return false for invalid ISO strings', () => {
      const invalidStrings = [
        '2024-01-15',
        '2024-13-15T14:30:45Z', // Invalid month
        '2024-01-32T14:30:45Z', // Invalid day
        '2024-01-15T25:30:45Z', // Invalid hour
        'invalid-date',
        ''
      ];

      invalidStrings.forEach(str => {
        expect(isValidISOString(str)).toBe(false);
      });
    });
  });

  describe('formatDateTime', () => {
    it('should format Date object with default German locale', () => {
      const result = formatDateTime(testDate);
      expect(result).toMatch(/15\.01\.2024.*14:30:45/);
    });

    it('should format ISO string with default German locale', () => {
      const result = formatDateTime(testISOString);
      expect(result).toMatch(/15\.01\.2024.*14:30:45/);
    });

    it('should format with custom locale', () => {
      const result = formatDateTime(testDate, 'en-US');
      expect(result).toMatch(/1\/15\/2024.*2:30:45 PM/);
    });

    it('should format with custom options', () => {
      const options: Intl.DateTimeFormatOptions = {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      };
      const result = formatDateTime(testDate, 'de-DE', options);
      expect(result).toMatch(/15\. Januar 2024/);
    });
  });

  describe('formatDateForInput', () => {
    it('should format Date object for date input', () => {
      const result = formatDateForInput(testDate);
      expect(result).toBe('2024-01-15');
    });

    it('should format ISO string for date input', () => {
      const result = formatDateForInput(testISOString);
      expect(result).toBe('2024-01-15');
    });
  });

  describe('formatDateTimeForInput', () => {
    it('should format Date object for datetime-local input', () => {
      const result = formatDateTimeForInput(testDate);
      expect(result).toBe('2024-01-15T14:30');
    });

    it('should format ISO string for datetime-local input', () => {
      const result = formatDateTimeForInput(testISOString);
      expect(result).toBe('2024-01-15T14:30');
    });
  });

  describe('createDateFromInputs', () => {
    it('should create Date from date and time inputs', () => {
      const result = createDateFromInputs('2024-01-15', '14:30');
      expect(result).toBeInstanceOf(Date);
      expect(result.toISOString()).toBe('2024-01-15T14:30:00.000Z');
    });

    it('should handle different time formats', () => {
      const result = createDateFromInputs('2024-01-15', '09:05');
      expect(result.toISOString()).toBe('2024-01-15T09:05:00.000Z');
    });
  });

  describe('isPastDateTime', () => {
    beforeEach(() => {
      // Mock current time to a fixed point for consistent testing
      jasmine.clock().install();
      jasmine.clock().mockDate(new Date('2024-01-15T15:00:00.000Z'));
    });

    afterEach(() => {
      jasmine.clock().uninstall();
    });

    it('should return true for past dates', () => {
      const pastDate = new Date('2024-01-15T14:00:00.000Z');
      expect(isPastDateTime(pastDate)).toBe(true);
      expect(isPastDateTime('2024-01-15T14:00:00.000Z')).toBe(true);
    });

    it('should return false for future dates', () => {
      const futureDate = new Date('2024-01-15T16:00:00.000Z');
      expect(isPastDateTime(futureDate)).toBe(false);
      expect(isPastDateTime('2024-01-15T16:00:00.000Z')).toBe(false);
    });
  });

  describe('isFutureDateTime', () => {
    beforeEach(() => {
      jasmine.clock().install();
      jasmine.clock().mockDate(new Date('2024-01-15T15:00:00.000Z'));
    });

    afterEach(() => {
      jasmine.clock().uninstall();
    });

    it('should return true for future dates', () => {
      const futureDate = new Date('2024-01-15T16:00:00.000Z');
      expect(isFutureDateTime(futureDate)).toBe(true);
      expect(isFutureDateTime('2024-01-15T16:00:00.000Z')).toBe(true);
    });

    it('should return false for past dates', () => {
      const pastDate = new Date('2024-01-15T14:00:00.000Z');
      expect(isFutureDateTime(pastDate)).toBe(false);
      expect(isFutureDateTime('2024-01-15T14:00:00.000Z')).toBe(false);
    });
  });

  describe('calculateDuration', () => {
    it('should calculate duration between Date objects', () => {
      const start = new Date('2024-01-15T14:00:00.000Z');
      const end = new Date('2024-01-15T16:30:00.000Z');
      const result = calculateDuration(start, end);
      expect(result).toBe(2.5 * 60 * 60 * 1000); // 2.5 hours in milliseconds
    });

    it('should calculate duration between ISO strings', () => {
      const start = '2024-01-15T14:00:00.000Z';
      const end = '2024-01-15T15:00:00.000Z';
      const result = calculateDuration(start, end);
      expect(result).toBe(60 * 60 * 1000); // 1 hour in milliseconds
    });

    it('should calculate duration with mixed types', () => {
      const start = new Date('2024-01-15T14:00:00.000Z');
      const end = '2024-01-15T14:30:00.000Z';
      const result = calculateDuration(start, end);
      expect(result).toBe(30 * 60 * 1000); // 30 minutes in milliseconds
    });

    it('should return negative duration for reversed times', () => {
      const start = new Date('2024-01-15T16:00:00.000Z');
      const end = new Date('2024-01-15T14:00:00.000Z');
      const result = calculateDuration(start, end);
      expect(result).toBe(-2 * 60 * 60 * 1000); // -2 hours in milliseconds
    });
  });

  describe('formatDuration', () => {
    it('should format duration in hours and minutes (German)', () => {
      const duration = 2.5 * 60 * 60 * 1000; // 2.5 hours
      const result = formatDuration(duration, 'de-DE');
      expect(result).toBe('2h 30min');
    });

    it('should format duration in minutes only (German)', () => {
      const duration = 45 * 60 * 1000; // 45 minutes
      const result = formatDuration(duration, 'de-DE');
      expect(result).toBe('45min');
    });

    it('should format duration in hours and minutes (English)', () => {
      const duration = 1.25 * 60 * 60 * 1000; // 1.25 hours
      const result = formatDuration(duration, 'en-US');
      expect(result).toBe('1h 15m');
    });

    it('should format duration in minutes only (English)', () => {
      const duration = 30 * 60 * 1000; // 30 minutes
      const result = formatDuration(duration, 'en-US');
      expect(result).toBe('30m');
    });

    it('should handle zero duration', () => {
      const result = formatDuration(0, 'de-DE');
      expect(result).toBe('0min');
    });

    it('should handle very short durations', () => {
      const duration = 30 * 1000; // 30 seconds
      const result = formatDuration(duration, 'de-DE');
      expect(result).toBe('0min');
    });

    it('should use German as default locale', () => {
      const duration = 90 * 60 * 1000; // 90 minutes
      const result = formatDuration(duration);
      expect(result).toBe('1h 30min');
    });
  });
});