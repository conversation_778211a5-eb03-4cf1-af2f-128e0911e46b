# Implementierungsplan: Admin-UI Foundation Setup

## Übersicht
Dieser Plan beschreibt die schrittweise Umsetzung der Admin-UI Grundfunktionen basierend auf dem EPIC `01-EPIC-admin-ui-foundation.md`, angepasst an die Angular 19 Technologie-Stack des Projekts.

## Technologie-Stack Anpassungen
**Original EPIC:** React 18+ mit TypeScript  
**Projekt-Stack:** Angular 19 mit TypeScript, Angular Material, NgRx

### Mapping der Technologien
| EPIC Technologie | Projekt Technologie | Begründung |
|------------------|-------------------|------------|
| React Components | Angular Standalone Components | Angular 19 Standard |
| Context API | NgRx Store | Zentrales State Management |
| React Router | Angular Router | Integriertes Routing |
| Tailwind CSS | Angular Material + SCSS | Konsistentes Design System |
| React Query | Apollo Client | GraphQL Integration |

---

## Phase 1: Layout-Foundation (F-UI-003)

### 🎯 Ziel
Implementierung der Basis-Layout-Struktur mit Header-Komponente unter Verwendung von Angular Material.

### 📋 Implementierungs-Checkliste

#### 1.1 Projekt-Setup und Abhängigkeiten
- [ ] Angular Material Theme konfigurieren (Indigo-Pink)
- [ ] Material Icons hinzufügen
- [ ] SCSS Global Styles definieren
- [ ] Angular Material Module importieren

#### 1.2 Layout-Struktur erstellen
- [ ] `src/app/shared/components/layout/header/header.component.ts` erstellen
- [ ] `src/app/shared/components/layout/main-layout/main-layout.component.ts` erstellen
- [ ] `src/app/shared/components/layout/index.ts` für Barrel Exports

#### 1.3 Header-Komponente implementieren
- [ ] Material Toolbar für Header-Basis verwenden
- [ ] Logo/Branding-Bereich implementieren
- [ ] Benutzer-Dropdown mit Material Menu erstellen
- [ ] Benachrichtigungsbereich mit Badge implementieren
- [ ] Globale Suchfunktion mit Material Autocomplete

#### 1.4 Typen und Interfaces definieren
- [ ] `src/app/core/models/user.model.ts` für Benutzer-Typen
- [ ] `src/app/core/models/notification.model.ts` für Benachrichtigungen
- [ ] `src/app/core/models/index.ts` für Barrel Exports

#### 1.5 Services erstellen
- [ ] `src/app/core/services/auth.service.ts` für Authentifizierung
- [ ] `src/app/core/services/notification.service.ts` für Benachrichtigungen
- [ ] `src/app/core/services/search.service.ts` für globale Suche

#### 1.6 NgRx Store Setup
- [ ] `src/app/store/auth/auth.state.ts` für Auth-State
- [ ] `src/app/store/auth/auth.actions.ts` für Auth-Actions
- [ ] `src/app/store/auth/auth.reducer.ts` für Auth-Reducer
- [ ] `src/app/store/auth/auth.effects.ts` für Auth-Effects
- [ ] `src/app/store/auth/auth.selectors.ts` für Auth-Selectors

### ✅ Akzeptanzkriterien Phase 1
- [ ] Header ist auf allen Seiten sichtbar und responsiv
- [ ] Material Design Theme ist konsistent angewendet
- [ ] Benutzerinformationen werden aus NgRx Store angezeigt
- [ ] Abmelde-Funktionalität funktioniert über NgRx Actions
- [ ] Benachrichtigungsbereich zeigt System-Alerts mit Material Badge
- [ ] Globale Suche ist funktional mit Material Autocomplete
- [ ] Logo/Branding ist korrekt positioniert
- [ ] TypeScript Strict Mode ohne Fehler

### 🧪 Tests Phase 1
- [ ] Header Component Unit Tests
- [ ] Auth Service Unit Tests
- [ ] NgRx Auth Store Tests (Actions, Reducer, Effects, Selectors)
- [ ] E2E Tests für Header-Funktionalität

---

## Phase 2: Navigation-System (F-UI-001)

### 🎯 Ziel
Implementierung der Hauptnavigation mit Angular Material Sidenav und responsivem Design.

### 📋 Implementierungs-Checkliste

#### 2.1 Navigation-Komponenten erstellen
- [ ] `src/app/shared/components/navigation/sidebar/sidebar.component.ts` mit Material Sidenav
- [ ] `src/app/shared/components/navigation/nav-item/nav-item.component.ts` mit Material List
- [ ] `src/app/shared/components/navigation/breadcrumb/breadcrumb.component.ts`
- [ ] `src/app/shared/components/navigation/index.ts` für Barrel Exports

#### 2.2 Navigation State Management
- [ ] `src/app/store/navigation/navigation.state.ts` für Navigation-State
- [ ] `src/app/store/navigation/navigation.actions.ts` für Navigation-Actions
- [ ] `src/app/store/navigation/navigation.reducer.ts` für Navigation-Reducer
- [ ] `src/app/store/navigation/navigation.selectors.ts` für Navigation-Selectors

#### 2.3 Routing-Konfiguration
- [ ] `src/app/core/config/routes.config.ts` für Route-Definitionen
- [ ] `src/app/app.routes.ts` erweitern mit Hauptrouten
- [ ] Route Guards für geschützte Bereiche implementieren
- [ ] Lazy Loading für Feature-Module konfigurieren

#### 2.4 Responsive Navigation
- [ ] Material Breakpoint Observer für responsive Verhalten
- [ ] Mobile Navigation mit Material Sidenav Drawer
- [ ] Desktop Navigation mit Material Sidenav Side
- [ ] Navigation Toggle-Funktionalität

#### 2.5 Navigation Features
- [ ] Aktive Route Highlighting mit Angular Router
- [ ] Breadcrumb-Service für dynamische Breadcrumbs
- [ ] Navigation-Animationen mit Angular Animations
- [ ] Keyboard Navigation Support

### ✅ Akzeptanzkriterien Phase 2
- [ ] Seitliche Navigation mit Material Sidenav implementiert
- [ ] Alle Hauptbereiche (Dashboard, Systeme, Meldungen, Benutzer, Einstellungen) verfügbar
- [ ] Aktuelle Route ist visuell hervorgehoben
- [ ] Navigation ist auf mobilen Geräten kollapsibel
- [ ] Breadcrumb-Navigation funktioniert auf Unterseiten
- [ ] Smooth Transitions mit Angular Animations
- [ ] Navigation-State wird in NgRx Store verwaltet
- [ ] Keyboard Navigation funktioniert

### 🧪 Tests Phase 2
- [ ] Sidebar Component Unit Tests
- [ ] Navigation State Tests
- [ ] Routing Tests
- [ ] Responsive Behavior Tests
- [ ] E2E Navigation Tests

---

## Phase 3: Dashboard-Implementation (F-UI-002)

### 🎯 Ziel
Entwicklung der Dashboard-Startseite mit Material Cards und GraphQL-Integration.

### 📋 Implementierungs-Checkliste

#### 3.1 Dashboard-Struktur erstellen
- [ ] `src/app/features/dashboard/dashboard.component.ts` als Hauptseite
- [ ] `src/app/features/dashboard/components/stats-card/stats-card.component.ts`
- [ ] `src/app/features/dashboard/components/recent-incidents/recent-incidents.component.ts`
- [ ] `src/app/features/dashboard/components/quick-actions/quick-actions.component.ts`

#### 3.2 Dashboard Services und GraphQL
- [ ] `src/app/features/dashboard/services/dashboard.service.ts` mit Apollo Client
- [ ] `src/app/features/dashboard/graphql/dashboard.queries.ts` für GraphQL Queries
- [ ] `src/app/features/dashboard/graphql/dashboard.fragments.ts` für GraphQL Fragments
- [ ] Mock GraphQL Resolver für Entwicklung

#### 3.3 Dashboard State Management
- [ ] `src/app/store/dashboard/dashboard.state.ts` für Dashboard-State
- [ ] `src/app/store/dashboard/dashboard.actions.ts` für Dashboard-Actions
- [ ] `src/app/store/dashboard/dashboard.reducer.ts` für Dashboard-Reducer
- [ ] `src/app/store/dashboard/dashboard.effects.ts` für Dashboard-Effects mit Apollo
- [ ] `src/app/store/dashboard/dashboard.selectors.ts` für Dashboard-Selectors

#### 3.4 Dashboard UI mit Material Design
- [ ] Material Grid Layout für responsive Kacheln
- [ ] Material Cards für Stats-Anzeige
- [ ] Material Progress Spinner für Loading States
- [ ] Material Snackbar für Error Handling
- [ ] Material List für Recent Incidents

#### 3.5 Dashboard Features
- [ ] Real-time Updates mit GraphQL Subscriptions
- [ ] Refresh-Funktionalität
- [ ] Error Boundary Implementation
- [ ] Loading Skeleton Components
- [ ] Accessibility (a11y) Features

#### 3.6 Dashboard Typen und Models
- [ ] `src/app/features/dashboard/models/dashboard.model.ts` für Dashboard-Typen
- [ ] `src/app/features/dashboard/models/stats.model.ts` für Statistik-Typen
- [ ] `src/app/features/dashboard/models/incident.model.ts` für Incident-Typen

### ✅ Akzeptanzkriterien Phase 3
- [ ] Material Card Layout mit wichtigsten Kennzahlen
- [ ] Anzahl aktiver Störungen nach Priorität angezeigt
- [ ] Anzahl geplanter Wartungen korrekt dargestellt
- [ ] Anzahl verwalteter Systeme und Benutzer angezeigt
- [ ] Liste der neuesten Meldungen funktional und aktuell
- [ ] Schnellzugriff-Aktionen verfügbar und funktional
- [ ] Dashboard lädt in unter 2 Sekunden
- [ ] Real-time Updates funktionieren
- [ ] Error Handling mit Material Snackbar

### 🧪 Tests Phase 3
- [ ] Dashboard Component Unit Tests
- [ ] Dashboard Service Tests mit Apollo Testing
- [ ] Dashboard State Tests
- [ ] GraphQL Query Tests
- [ ] E2E Dashboard Tests
- [ ] Performance Tests (Lighthouse)

---

## Technische Implementierungsdetails

### 📁 Angepasste Code-Struktur (Angular)
```
src/app/
├── core/
│   ├── models/
│   │   ├── user.model.ts
│   │   ├── notification.model.ts
│   │   └── index.ts
│   ├── services/
│   │   ├── auth.service.ts
│   │   ├── notification.service.ts
│   │   ├── search.service.ts
│   │   └── index.ts
│   ├── guards/
│   │   ├── auth.guard.ts
│   │   └── index.ts
│   └── config/
│       ├── routes.config.ts
│       └── api.config.ts
├── shared/
│   ├── components/
│   │   ├── layout/
│   │   │   ├── header/
│   │   │   ├── main-layout/
│   │   │   └── index.ts
│   │   ├── navigation/
│   │   │   ├── sidebar/
│   │   │   ├── nav-item/
│   │   │   ├── breadcrumb/
│   │   │   └── index.ts
│   │   └── ui/
│   │       ├── loading-spinner/
│   │       ├── error-message/
│   │       └── index.ts
│   └── pipes/
│       └── index.ts
├── features/
│   └── dashboard/
│       ├── dashboard.component.ts
│       ├── components/
│       │   ├── stats-card/
│       │   ├── recent-incidents/
│       │   ├── quick-actions/
│       │   └── index.ts
│       ├── services/
│       │   ├── dashboard.service.ts
│       │   └── index.ts
│       ├── models/
│       │   ├── dashboard.model.ts
│       │   ├── stats.model.ts
│       │   └── index.ts
│       └── graphql/
│           ├── dashboard.queries.ts
│           ├── dashboard.fragments.ts
│           └── index.ts
└── store/
    ├── auth/
    │   ├── auth.state.ts
    │   ├── auth.actions.ts
    │   ├── auth.reducer.ts
    │   ├── auth.effects.ts
    │   ├── auth.selectors.ts
    │   └── index.ts
    ├── navigation/
    │   ├── navigation.state.ts
    │   ├── navigation.actions.ts
    │   ├── navigation.reducer.ts
    │   ├── navigation.selectors.ts
    │   └── index.ts
    ├── dashboard/
    │   ├── dashboard.state.ts
    │   ├── dashboard.actions.ts
    │   ├── dashboard.reducer.ts
    │   ├── dashboard.effects.ts
    │   ├── dashboard.selectors.ts
    │   └── index.ts
    └── index.ts
```

### 🔧 Angular-spezifische Konfigurationen

#### Angular Material Setup
- [ ] `ng add @angular/material` ausführen
- [ ] Indigo-Pink Theme konfigurieren
- [ ] Material Icons und Fonts einbinden
- [ ] Custom Theme-Variablen definieren

#### NgRx Setup
- [ ] `ng add @ngrx/store` ausführen
- [ ] `ng add @ngrx/effects` ausführen
- [ ] `ng add @ngrx/store-devtools` ausführen
- [ ] Store-Konfiguration in `app.config.ts`

#### Apollo GraphQL Setup
- [ ] `ng add apollo-angular` ausführen
- [ ] GraphQL-Endpoint konfigurieren
- [ ] Apollo Cache-Konfiguration
- [ ] GraphQL Code Generation Setup

### 📊 Performance-Ziele (Angular-angepasst)
- [ ] Initial Page Load unter 2 Sekunden
- [ ] Navigation-Wechsel unter 300ms
- [ ] Dashboard-Daten-Update unter 1 Sekunde
- [ ] Mobile Performance Score > 90 (Lighthouse)
- [ ] Bundle Size unter 2MB (gzipped)
- [ ] OnPush Change Detection Strategy verwenden

---

## Abhängigkeiten & Risiken

### 🔗 Abhängigkeiten
- [ ] Angular Material Design System muss konfiguriert sein
- [ ] GraphQL API-Endpoints für Dashboard-Daten müssen verfügbar sein
- [ ] NgRx Store-Architektur muss definiert sein
- [ ] Apollo Client muss konfiguriert sein
- [ ] Basis-Routing-Struktur muss festgelegt sein

### ⚠️ Risiken & Lösungen
| Risiko | Lösung | Verantwortlich |
|--------|--------|----------------|
| GraphQL API nicht rechtzeitig verfügbar | Mock GraphQL Server mit Apollo implementieren | Backend Team |
| Performance-Probleme bei vielen Dashboard-Kacheln | OnPush Change Detection und Virtual Scrolling | Frontend Team |
| NgRx Komplexität | Schrittweise Einführung mit einfachen States | Frontend Team |
| Material Design Customization | Custom Theme mit SCSS-Variablen | UI/UX Team |

---

## Nächste Schritte

### 🚀 Sofort starten (Woche 1)
1. Angular Material und NgRx Setup
2. Layout-Foundation mit Header-Komponente
3. Basis-Routing-Struktur

### 📈 Nach Phase 1 (Woche 2)
1. Navigation-System mit Material Sidenav
2. NgRx Store für Navigation
3. Responsive Design Implementation

### 🎯 Parallel möglich
1. GraphQL Schema Definition
2. Mock-Daten für Dashboard erstellen
3. Material Theme Customization

### 🏁 Abschluss (Woche 3)
1. Dashboard-Integration mit Apollo
2. Performance-Optimierung
3. E2E Tests und Dokumentation

---

## Angular-spezifische Entwicklungshinweise

### 🏗️ Architektur-Prinzipien
- Verwende Angular Standalone Components (Angular 19 Standard)
- Implementiere Smart/Dumb Component Pattern
- Nutze OnPush Change Detection Strategy für Performance
- Verwende Angular Signals für reaktive Programmierung

### 🎨 Styling-Guidelines
- Angular Material als Basis-Design-System
- SCSS für Custom Styles
- CSS Custom Properties für Theme-Variablen
- Mobile-First Responsive Design

### 🔄 State Management
- NgRx für komplexe Application State
- Angular Services für einfache Shared State
- RxJS Observables für reaktive Datenströme
- Apollo Cache für GraphQL-Daten

### 🧪 Testing-Strategie
- Jasmine/Karma für Unit Tests
- Angular Testing Utilities
- Apollo Angular Testing für GraphQL
- Cypress für E2E Tests

### 📱 Accessibility (a11y)
- Angular CDK a11y Module verwenden
- Material Components haben eingebaute a11y Features
- ARIA-Labels und Semantic HTML
- Keyboard Navigation Support

---

**Status:** Bereit für Implementierung  
**Geschätzte Entwicklungszeit:** 3 Wochen  
**Priorität:** Hoch (Grundlage für alle weiteren Features)  
**Technologie-Stack:** Angular 19, Material Design, NgRx, Apollo GraphQL