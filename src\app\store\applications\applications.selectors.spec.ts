import * as fromSelectors from './applications.selectors';
import { ApplicationsState } from './applications.state';
import { Application } from '../../core/models/application.model';

describe('Applications Selectors', () => {
  const mockApplication1: Application = {
    identifier: 'app1',
    name: 'Application 1',
    isDeleted: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  const mockApplication2: Application = {
    identifier: 'app2',
    name: 'Application 2',
    isDeleted: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  const initialState: ApplicationsState = {
    applications: [mockApplication1, mockApplication2],
    selectedApplication: mockApplication1,
    loading: false,
    error: null,
  };

  it('should select the applications list', () => {
    const result = fromSelectors.selectAllApplications.projector(initialState);
    expect(result).toEqual([mockApplication1, mockApplication2]);
  });

  it('should select the loading state', () => {
    const result = fromSelectors.selectApplicationsLoading.projector(initialState);
    expect(result).toBe(false);
  });

  it('should select the error state', () => {
    const result = fromSelectors.selectApplicationsError.projector(initialState);
    expect(result).toBe(null);
  });

  it('should select the selected application', () => {
    const result = fromSelectors.selectSelectedApplication.projector(initialState);
    expect(result).toEqual(mockApplication1);
  });
});