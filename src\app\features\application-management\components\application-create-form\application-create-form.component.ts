import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormGroup, NonNullableFormBuilder, Validators } from '@angular/forms';
import { ApplicationForm } from '../../shared/interfaces/application-form.interfaces';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ApplicationValidators } from '../../shared/validators/application-validators';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-application-create-form',
  templateUrl: './application-create-form.component.html',
  styleUrls: ['./application-create-form.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
  ],
})
export class ApplicationCreateFormComponent {
  @Input() loading = false;
  @Input() disabled = false;
  @Output() formSubmit = new EventEmitter<any>();
  @Output() formCancel = new EventEmitter<void>();

  applicationForm: FormGroup<ApplicationForm>;

  constructor(
    private fb: NonNullableFormBuilder,
    private applicationValidators: ApplicationValidators
  ) {
    this.applicationForm = this.fb.group({
      name: [
        '',
        [Validators.required],
        [this.applicationValidators.nameUniquenessValidator()],
      ],
      description: ['', [Validators.required]],
    });
  }

  onSubmit(): void {
    if (this.applicationForm.valid) {
      this.formSubmit.emit(this.applicationForm.getRawValue());
    }
  }

  onCancel(): void {
    this.formCancel.emit();
  }
}