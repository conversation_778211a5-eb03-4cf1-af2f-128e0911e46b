import { Injectable } from '@angular/core';
import { Incident, CreateIncidentInput, UpdateIncidentInput } from '../models/incident.model';
import { IncidentResponse, CreateIncidentInput as GraphQLCreateInput, UpdateIncidentInput as GraphQLUpdateInput } from '../graphql/types';
import { toISOString, toDate } from '../utils/datetime.utils';

/**
 * Service for adapting between frontend Incident models and GraphQL types
 * Handles DateTime conversion between Date objects and ISO strings
 */
@Injectable({
  providedIn: 'root'
})
export class IncidentAdapterService {

  /**
   * Converts GraphQL IncidentResponse to frontend Incident model
   * Converts ISO string timestamps to Date objects for better frontend handling
   * @param response - GraphQL response with ISO string timestamps
   * @returns Incident model with Date objects
   */
  fromGraphQLResponse(response: IncidentResponse): Incident {
    return {
      identifier: response.identifier,
      title: response.title,
      type: response.type,
      description: response.description,
      startTime: toDate(response.startTime),
      plannedEndTime: response.plannedEndTime ? toDate(response.plannedEndTime) : undefined,
      actualEndTime: response.actualEndTime ? toDate(response.actualEndTime) : undefined,
      alternatives: response.alternatives,
      isResolved: response.isResolved,
      createdAt: toDate(response.createdAt),
      updatedAt: toDate(response.updatedAt),
      applications: response.applications
    };
  }

  /**
   * Converts array of GraphQL IncidentResponse to frontend Incident models
   * @param responses - Array of GraphQL responses
   * @returns Array of Incident models with Date objects
   */
  fromGraphQLResponseArray(responses: IncidentResponse[]): Incident[] {
    return responses.map(response => this.fromGraphQLResponse(response));
  }

  /**
   * Converts frontend CreateIncidentInput to GraphQL input format
   * Converts Date objects to ISO strings for GraphQL transmission
   * @param input - Frontend create input with Date objects or ISO strings
   * @returns GraphQL input with ISO strings
   */
  toGraphQLCreateInput(input: CreateIncidentInput): GraphQLCreateInput {
    return {
      title: input.title,
      type: input.type,
      description: input.description,
      startTime: toISOString(input.startTime),
      plannedEndTime: input.plannedEndTime ? toISOString(input.plannedEndTime) : undefined,
      alternatives: input.alternatives,
      applicationIds: input.applicationIds
    };
  }

  /**
   * Converts frontend UpdateIncidentInput to GraphQL input format
   * Converts Date objects to ISO strings for GraphQL transmission
   * @param input - Frontend update input with Date objects or ISO strings
   * @returns GraphQL input with ISO strings
   */
  toGraphQLUpdateInput(input: UpdateIncidentInput): GraphQLUpdateInput {
    return {
      identifier: input.identifier,
      title: input.title,
      type: input.type,
      description: input.description,
      startTime: input.startTime ? toISOString(input.startTime) : undefined,
      plannedEndTime: input.plannedEndTime ? toISOString(input.plannedEndTime) : undefined,
      actualEndTime: input.actualEndTime ? toISOString(input.actualEndTime) : undefined,
      alternatives: input.alternatives,
      applicationIds: input.applicationIds
    };
  }

  /**
   * Converts frontend Incident model to CreateIncidentInput
   * Useful for creating new incidents based on existing ones
   * @param incident - Frontend Incident model
   * @returns CreateIncidentInput for GraphQL operations
   */
  toCreateInput(incident: Incident): CreateIncidentInput {
    return {
      title: incident.title,
      type: incident.type,
      description: incident.description,
      startTime: incident.startTime,
      plannedEndTime: incident.plannedEndTime,
      alternatives: incident.alternatives,
      applicationIds: incident.applications.map(app => app.identifier)
    };
  }

  /**
   * Converts frontend Incident model to UpdateIncidentInput
   * Useful for updating existing incidents
   * @param incident - Frontend Incident model
   * @param fieldsToUpdate - Optional array of field names to include in update
   * @returns UpdateIncidentInput for GraphQL operations
   */
  toUpdateInput(incident: Incident, fieldsToUpdate?: (keyof UpdateIncidentInput)[]): UpdateIncidentInput {
    const baseInput: UpdateIncidentInput = {
      identifier: incident.identifier,
      title: incident.title,
      type: incident.type,
      description: incident.description,
      startTime: incident.startTime,
      plannedEndTime: incident.plannedEndTime,
      actualEndTime: incident.actualEndTime,
      alternatives: incident.alternatives,
      applicationIds: incident.applications.map(app => app.identifier)
    };

    // If specific fields are requested, only include those
    if (fieldsToUpdate && fieldsToUpdate.length > 0) {
      const filteredInput: UpdateIncidentInput = {
        identifier: incident.identifier
      };

      fieldsToUpdate.forEach(field => {
        if (field !== 'identifier' && baseInput[field] !== undefined) {
          (filteredInput as any)[field] = baseInput[field];
        }
      });

      return filteredInput;
    }

    return baseInput;
  }

  /**
   * Creates a partial UpdateIncidentInput for resolving an incident
   * Sets actualEndTime to current time and marks as resolved
   * @param identifier - Incident identifier
   * @param actualEndTime - Optional custom end time, defaults to current time
   * @returns UpdateIncidentInput for resolving incident
   */
  createResolveInput(identifier: string, actualEndTime?: Date | string): UpdateIncidentInput {
    return {
      identifier,
      actualEndTime: actualEndTime || new Date()
    };
  }

  /**
   * Validates that required DateTime fields are present and valid
   * @param input - CreateIncidentInput or UpdateIncidentInput
   * @returns Validation result with errors if any
   */
  validateDateTimeFields(input: CreateIncidentInput | UpdateIncidentInput): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // For CreateIncidentInput, startTime is required
    if ('startTime' in input && input.startTime) {
      try {
        const startDate = input.startTime instanceof Date ? input.startTime : new Date(input.startTime);
        if (isNaN(startDate.getTime())) {
          errors.push('Invalid startTime format');
        }
      } catch {
        errors.push('Invalid startTime format');
      }
    } else if ('startTime' in input && !('identifier' in input)) {
      // startTime is required for CreateIncidentInput
      errors.push('startTime is required');
    }

    // Validate plannedEndTime if present
    if ('plannedEndTime' in input && input.plannedEndTime) {
      try {
        const plannedDate = input.plannedEndTime instanceof Date ? input.plannedEndTime : new Date(input.plannedEndTime);
        if (isNaN(plannedDate.getTime())) {
          errors.push('Invalid plannedEndTime format');
        }
      } catch {
        errors.push('Invalid plannedEndTime format');
      }
    }

    // Validate actualEndTime if present
    if ('actualEndTime' in input && input.actualEndTime) {
      try {
        const actualDate = input.actualEndTime instanceof Date ? input.actualEndTime : new Date(input.actualEndTime);
        if (isNaN(actualDate.getTime())) {
          errors.push('Invalid actualEndTime format');
        }
      } catch {
        errors.push('Invalid actualEndTime format');
      }
    }

    // Validate logical order of dates
    if ('startTime' in input && 'plannedEndTime' in input && input.startTime && input.plannedEndTime) {
      try {
        const startDate = input.startTime instanceof Date ? input.startTime : new Date(input.startTime);
        const plannedDate = input.plannedEndTime instanceof Date ? input.plannedEndTime : new Date(input.plannedEndTime);
        
        if (startDate.getTime() >= plannedDate.getTime()) {
          errors.push('plannedEndTime must be after startTime');
        }
      } catch {
        // Date parsing errors already caught above
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}