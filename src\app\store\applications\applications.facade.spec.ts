import { TestBed } from '@angular/core/testing';
import { MockStore, provideMockStore } from '@ngrx/store/testing';
import { ApplicationsFacade } from './applications.facade';
import { ApplicationsState } from './applications.state';
import * as ApplicationActions from './applications.actions';
import { Application } from '../../core/models/application.model';
import * as fromSelectors from './applications.selectors';

describe('ApplicationsFacade', () => {
  let facade: ApplicationsFacade;
  let store: MockStore<ApplicationsState>;

  const mockApplication: Application = {
    identifier: 'app1',
    name: 'Application 1',
    isDeleted: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  const initialState: ApplicationsState = {
    applications: [],
    selectedApplication: null,
    loading: false,
    error: null,
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        ApplicationsFacade,
        provideMockStore({ initialState }),
      ],
    });

    facade = TestBed.inject(ApplicationsFacade);
    store = TestBed.inject(MockStore);
    spyOn(store, 'dispatch');
  });

  it('should be created', () => {
    expect(facade).toBeTruthy();
  });

  it('should dispatch loadApplications action', () => {
    const filter = {};
    facade.loadApplications(filter);
    expect(store.dispatch).toHaveBeenCalledWith(ApplicationActions.loadApplications({ filter }));
  });

  it('should dispatch selectApplication action', () => {
    const identifier = 'app1';
    facade.selectApplication(identifier);
    expect(store.dispatch).toHaveBeenCalledWith(ApplicationActions.selectApplication({ identifier }));
  });

  it('should dispatch createApplication action', () => {
    const application = { name: 'New App' };
    facade.createApplication(application);
    expect(store.dispatch).toHaveBeenCalledWith(ApplicationActions.createApplication({ application }));
  });

  it('should dispatch updateApplication action', () => {
    const application = { identifier: 'app1', name: 'Updated App' };
    facade.updateApplication(application);
    expect(store.dispatch).toHaveBeenCalledWith(ApplicationActions.updateApplication({ application }));
  });

  it('should dispatch deleteApplication action', () => {
    const identifier = 'app1';
    facade.deleteApplication(identifier);
    expect(store.dispatch).toHaveBeenCalledWith(ApplicationActions.deleteApplication({ identifier }));
  });

  it('should select applications from the store', (done) => {
    store.overrideSelector(fromSelectors.selectAllApplications, [mockApplication]);
    facade.applications$.subscribe(applications => {
      expect(applications).toEqual([mockApplication]);
      done();
    });
  });

  it('should select the selected application from the store', (done) => {
    store.overrideSelector(fromSelectors.selectSelectedApplication, mockApplication);
    facade.selectedApplication$.subscribe(application => {
      expect(application).toEqual(mockApplication);
      done();
    });
  });

  it('should select the loading state from the store', (done) => {
    store.overrideSelector(fromSelectors.selectApplicationsLoading, true);
    facade.loading$.subscribe(loading => {
      expect(loading).toBe(true);
      done();
    });
  });

  it('should select the error state from the store', (done) => {
    const error = 'An error occurred';
    store.overrideSelector(fromSelectors.selectApplicationsError, error);
    facade.error$.subscribe(err => {
      expect(err).toBe(error);
      done();
    });
  });
});