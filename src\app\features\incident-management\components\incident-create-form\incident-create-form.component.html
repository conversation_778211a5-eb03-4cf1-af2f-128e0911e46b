<form [formGroup]="form" (ngSubmit)="onSubmit()" class="incident-create-form">
  <div class="form-container">
    
    <!-- Title Field -->
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Titel *</mat-label>
      <input 
        matInput 
        formControlName="title"
        placeholder="Titel des Vorfalls eingeben"
        maxlength="200"
        aria-describedby="title-error">
      <mat-hint align="end">{{titleControl?.value?.length || 0}}/200</mat-hint>
      <mat-error id="title-error">{{getTitleErrorMessage()}}</mat-error>
    </mat-form-field>

    <!-- Type Field -->
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Typ *</mat-label>
      <mat-select formControlName="type" aria-describedby="type-error">
        <mat-option 
          *ngFor="let type of incidentTypes" 
          [value]="type">
          {{getIncidentTypeDisplayName(type)}}
        </mat-option>
      </mat-select>
      <mat-error id="type-error">{{getTypeErrorMessage()}}</mat-error>
    </mat-form-field>

    <!-- Description Field -->
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Beschreibung</mat-label>
      <textarea 
        matInput 
        formControlName="description"
        placeholder="Detaillierte Beschreibung des Vorfalls"
        rows="4"
        maxlength="1000"
        aria-describedby="description-error">
      </textarea>
      <mat-hint align="end">{{descriptionControl?.value?.length || 0}}/1000</mat-hint>
      <mat-error id="description-error">{{getDescriptionErrorMessage()}}</mat-error>
    </mat-form-field>

    <!-- Date/Time Fields Row -->
    <div class="date-time-row">
      <!-- Start Time Field -->
      <div class="datetime-field">
        <label class="field-label">Startzeit</label>
        <app-datetime-picker
          formControlName="startTime"
          [required]="true"
          dateLabel="Datum"
          timeLabel="Uhrzeit"
          datePlaceholder="TT.MM.JJJJ"
          timePlaceholder="HH:MM"
          dateAriaLabel="Startdatum auswählen"
          timeAriaLabel="Startzeit eingeben"
          requiredErrorMessage="Startzeit ist erforderlich"
          invalidDateErrorMessage="Ungültiges Startdatum"
          invalidTimeErrorMessage="Ungültige Startzeit (Format: HH:MM)">
        </app-datetime-picker>
        <div class="error-message" *ngIf="startTimeControl?.invalid && startTimeControl?.touched">
          {{getStartTimeErrorMessage()}}
        </div>
      </div>

      <!-- Planned End Time Field -->
      <div class="datetime-field">
        <label class="field-label optional">Geplante Endzeit</label>
        <app-datetime-picker
          formControlName="plannedEndTime"
          [required]="false"
          dateLabel="Datum"
          timeLabel="Uhrzeit"
          datePlaceholder="TT.MM.JJJJ"
          timePlaceholder="HH:MM"
          dateAriaLabel="Geplantes Enddatum auswählen"
          timeAriaLabel="Geplante Endzeit eingeben"
          invalidDateErrorMessage="Ungültiges Enddatum"
          invalidTimeErrorMessage="Ungültige Endzeit (Format: HH:MM)">
        </app-datetime-picker>
        <div class="error-message" *ngIf="plannedEndTimeControl?.invalid && plannedEndTimeControl?.touched">
          {{getPlannedEndTimeErrorMessage()}}
        </div>
      </div>
    </div>

    <!-- Applications Field -->
    <div class="applications-section">
      <label class="section-label">Betroffene Anwendungen *</label>
      <app-application-selector
        formControlName="applicationIds"
        [required]="true"
        aria-describedby="applications-error">
      </app-application-selector>
      <div class="error-message" id="applications-error" *ngIf="applicationIdsControl?.invalid && applicationIdsControl?.touched">
        {{getApplicationsErrorMessage()}}
      </div>
    </div>

    <!-- Alternatives Field -->
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Alternativen</mat-label>
      <textarea 
        matInput 
        formControlName="alternatives"
        placeholder="Alternative Lösungen oder Workarounds"
        rows="3"
        maxlength="500"
        aria-describedby="alternatives-error">
      </textarea>
      <mat-hint align="end">{{alternativesControl?.value?.length || 0}}/500</mat-hint>
      <mat-error id="alternatives-error">{{getAlternativesErrorMessage()}}</mat-error>
    </mat-form-field>

  </div>

  <!-- Form Actions -->
  <div class="form-actions">
    <button
      type="button"
      mat-button
      (click)="onCancel()"
      class="cancel-button">
      <mat-icon>cancel</mat-icon>
      Abbrechen
    </button>
    
    <button
      type="submit"
      mat-raised-button
      color="primary"
      [disabled]="form.invalid"
      class="submit-button">
      <mat-icon>add</mat-icon>
      Vorfall erstellen
    </button>
  </div>
</form>