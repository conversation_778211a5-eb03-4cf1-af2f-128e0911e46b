import { Component, Input, OnInit, OnDestroy, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatChipsModule } from '@angular/material/chips';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { Subject, takeUntil, Observable, of } from 'rxjs';
import { ChangeHistoryEntry } from '../../../../core/models/change-history.model';
import { EntityType } from '../../../../core/enums/entity-type.enum';

@Component({
  selector: 'app-change-history',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatButtonModule,
    MatTooltipModule,
    MatChipsModule,
    MatPaginatorModule
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <mat-card class="change-history-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>history</mat-icon>
          Änderungshistorie
        </mat-card-title>
        <mat-card-subtitle *ngIf="!loading && totalEntries > 0">
          {{totalEntries}} Änderung(en) gefunden
        </mat-card-subtitle>
      </mat-card-header>

      <mat-card-content>
        <!-- Loading State -->
        <div *ngIf="loading" class="loading-container">
          <mat-spinner diameter="40"></mat-spinner>
          <p>Lade Änderungshistorie...</p>
        </div>

        <!-- Empty State -->
        <div *ngIf="!loading && changeHistory.length === 0" class="empty-state">
          <mat-icon class="empty-icon">history</mat-icon>
          <h3>Keine Änderungen gefunden</h3>
          <p>Für diese Applikation sind noch keine Änderungen dokumentiert.</p>
        </div>

        <!-- Timeline -->
        <div *ngIf="!loading && changeHistory.length > 0" class="timeline-container">
          <div class="timeline">
            <div 
              *ngFor="let entry of changeHistory; trackBy: trackByEntryId" 
              class="timeline-item"
              [class.timeline-item-create]="entry.action === 'CREATE'"
              [class.timeline-item-update]="entry.action === 'UPDATE'"
              [class.timeline-item-delete]="entry.action === 'DELETE'">
              
              <!-- Timeline Marker -->
              <div class="timeline-marker">
                <mat-icon [class]="getActionIconClass(entry.action)">
                  {{getActionIcon(entry.action)}}
                </mat-icon>
              </div>

              <!-- Timeline Content -->
              <div class="timeline-content">
                <div class="timeline-header">
                  <div class="timeline-title">
                    <mat-chip [class]="getActionChipClass(entry.action)">
                      {{getActionLabel(entry.action)}}
                    </mat-chip>
                    <span class="timeline-entity">{{getEntityLabel(entry.entityType)}}</span>
                  </div>
                  <div class="timeline-meta">
                    <span class="timeline-user" [matTooltip]="'Benutzer: ' + (entry.userName || entry.userId || 'Unbekannt')">
                      <mat-icon>person</mat-icon>
                      {{entry.userName || entry.userId || 'Unbekannt'}}
                    </span>
                    <span class="timeline-date" [matTooltip]="entry.timestamp | date:'full'">
                      <mat-icon>schedule</mat-icon>
                      {{entry.timestamp | date:'short'}}
                    </span>
                  </div>
                </div>

                <!-- Change Description -->
                <div class="timeline-description" *ngIf="entry.description">
                  <p>{{entry.description}}</p>
                </div>

                <!-- Change Details -->
                <div class="timeline-details" *ngIf="entry.changes && getChangesArray(entry.changes).length > 0">
                  <div class="changes-header">
                    <mat-icon>edit</mat-icon>
                    <span>Geänderte Felder:</span>
                  </div>
                  <div class="changes-list">
                    <div
                      *ngFor="let change of getChangesArray(entry.changes)"
                      class="change-item"
                      [matTooltip]="getChangeTooltip(change)">
                      <span class="field-name">{{change.field}}</span>
                      <div class="field-values" *ngIf="change.oldValue || change.newValue">
                        <span class="old-value" *ngIf="change.oldValue">
                          <mat-icon>remove</mat-icon>
                          {{change.oldValue}}
                        </span>
                        <mat-icon class="arrow-icon">arrow_forward</mat-icon>
                        <span class="new-value" *ngIf="change.newValue">
                          <mat-icon>add</mat-icon>
                          {{change.newValue}}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Pagination -->
        <mat-paginator 
          *ngIf="!loading && totalEntries > pageSize"
          [length]="totalEntries"
          [pageSize]="pageSize"
          [pageSizeOptions]="pageSizeOptions"
          [pageIndex]="currentPage"
          (page)="onPageChange($event)"
          showFirstLastButtons>
        </mat-paginator>
      </mat-card-content>
    </mat-card>
  `,
  styles: [`
    .change-history-card {
      margin: 16px 0;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 32px;
      gap: 16px;
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 32px;
      text-align: center;
      color: rgba(0, 0, 0, 0.6);
    }

    .empty-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    .timeline-container {
      margin-top: 16px;
    }

    .timeline {
      position: relative;
      padding-left: 32px;
    }

    .timeline::before {
      content: '';
      position: absolute;
      left: 16px;
      top: 0;
      bottom: 0;
      width: 2px;
      background: #e0e0e0;
    }

    .timeline-item {
      position: relative;
      margin-bottom: 24px;
      padding-bottom: 16px;
    }

    .timeline-item:last-child {
      margin-bottom: 0;
    }

    .timeline-marker {
      position: absolute;
      left: -24px;
      top: 8px;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: white;
      border: 2px solid #e0e0e0;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1;
    }

    .timeline-item-create .timeline-marker {
      border-color: #4caf50;
      background: #e8f5e8;
    }

    .timeline-item-update .timeline-marker {
      border-color: #2196f3;
      background: #e3f2fd;
    }

    .timeline-item-delete .timeline-marker {
      border-color: #f44336;
      background: #ffebee;
    }

    .timeline-content {
      background: white;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      padding: 16px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .timeline-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 12px;
      flex-wrap: wrap;
      gap: 8px;
    }

    .timeline-title {
      display: flex;
      align-items: center;
      gap: 8px;
      flex-wrap: wrap;
    }

    .timeline-entity {
      font-weight: 500;
      color: rgba(0, 0, 0, 0.7);
    }

    .timeline-meta {
      display: flex;
      gap: 16px;
      color: rgba(0, 0, 0, 0.6);
      font-size: 0.875rem;
      flex-wrap: wrap;
    }

    .timeline-user,
    .timeline-date {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .timeline-user mat-icon,
    .timeline-date mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    .timeline-description {
      margin-bottom: 12px;
      color: rgba(0, 0, 0, 0.8);
    }

    .timeline-details {
      border-top: 1px solid #f0f0f0;
      padding-top: 12px;
    }

    .changes-header {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
      margin-bottom: 8px;
      color: rgba(0, 0, 0, 0.7);
    }

    .changes-header mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    .changes-list {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .change-item {
      background: #f8f9fa;
      border-radius: 4px;
      padding: 8px 12px;
    }

    .field-name {
      font-weight: 500;
      color: rgba(0, 0, 0, 0.8);
      display: block;
      margin-bottom: 4px;
    }

    .field-values {
      display: flex;
      align-items: center;
      gap: 8px;
      flex-wrap: wrap;
    }

    .old-value,
    .new-value {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 0.875rem;
    }

    .old-value {
      background: #ffebee;
      color: #c62828;
    }

    .new-value {
      background: #e8f5e8;
      color: #2e7d32;
    }

    .old-value mat-icon,
    .new-value mat-icon {
      font-size: 14px;
      width: 14px;
      height: 14px;
    }

    .arrow-icon {
      color: rgba(0, 0, 0, 0.5);
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    /* Action Chip Styling */
    .action-chip-create {
      background-color: #4caf50;
      color: white;
    }

    .action-chip-update {
      background-color: #2196f3;
      color: white;
    }

    .action-chip-delete {
      background-color: #f44336;
      color: white;
    }

    /* Action Icon Styling */
    .action-icon-create {
      color: #4caf50;
    }

    .action-icon-update {
      color: #2196f3;
    }

    .action-icon-delete {
      color: #f44336;
    }

    mat-card-header {
      margin-bottom: 16px;
    }

    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    mat-paginator {
      margin-top: 16px;
    }

    @media (max-width: 768px) {
      .timeline {
        padding-left: 24px;
      }

      .timeline-marker {
        left: -20px;
        width: 24px;
        height: 24px;
      }

      .timeline-header {
        flex-direction: column;
        align-items: flex-start;
      }

      .timeline-meta {
        flex-direction: column;
        gap: 8px;
      }

      .field-values {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
      }

      .arrow-icon {
        transform: rotate(90deg);
      }
    }

    @media (max-width: 600px) {
      .change-history-card {
        margin: 8px 0;
      }

      .timeline-content {
        padding: 12px;
      }
    }
  `]
})
export class ChangeHistoryComponent implements OnInit, OnDestroy {
  @Input() entityId!: string;
  @Input() entityType: EntityType = EntityType.APPLICATION;
  @Input() maxInitialItems = 10;

  changeHistory: ChangeHistoryEntry[] = [];
  loading = false;
  totalEntries = 0;
  currentPage = 0;
  pageSize = 10;
  pageSizeOptions = [5, 10, 25, 50];

  private destroy$ = new Subject<void>();

  constructor() {}

  ngOnInit(): void {
    if (this.entityId) {
      this.loadChangeHistory();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadChangeHistory(): void {
    this.loading = true;
    
    // TODO: Replace with actual service call when change history service is available
    this.getMockChangeHistory()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (entries) => {
          const startIndex = this.currentPage * this.pageSize;
          const endIndex = startIndex + this.pageSize;
          this.changeHistory = entries.slice(startIndex, endIndex);
          this.totalEntries = entries.length;
          this.loading = false;
        },
        error: (error) => {
          console.error('Error loading change history:', error);
          this.changeHistory = [];
          this.loading = false;
        }
      });
  }

  private getMockChangeHistory(): Observable<ChangeHistoryEntry[]> {
    // Mock data - replace with actual service call
    const mockEntries: ChangeHistoryEntry[] = [
      {
        identifier: '1',
        entityIdentifier: this.entityId,
        entityType: this.entityType,
        action: 'CREATE',
        description: 'Applikation wurde erstellt',
        userId: 'admin',
        userName: 'Administrator',
        timestamp: new Date('2024-01-15T10:30:00Z').toISOString(),
        changes: {
          name: {
            oldValue: null,
            newValue: 'Neue Applikation'
          },
          description: {
            oldValue: null,
            newValue: 'Beschreibung der neuen Applikation'
          }
        }
      },
      {
        identifier: '2',
        entityIdentifier: this.entityId,
        entityType: this.entityType,
        action: 'UPDATE',
        description: 'Beschreibung wurde aktualisiert',
        userId: 'user1',
        userName: 'Benutzer 1',
        timestamp: new Date('2024-01-16T14:20:00Z').toISOString(),
        changes: {
          description: {
            oldValue: 'Alte Beschreibung',
            newValue: 'Neue, verbesserte Beschreibung'
          }
        }
      },
      {
        identifier: '3',
        entityIdentifier: this.entityId,
        entityType: this.entityType,
        action: 'UPDATE',
        description: 'Name wurde geändert',
        userId: 'admin',
        userName: 'Administrator',
        timestamp: new Date('2024-01-17T09:15:00Z').toISOString(),
        changes: {
          name: {
            oldValue: 'Alte Applikation',
            newValue: 'Neue Applikation'
          }
        }
      }
    ];

    return of(mockEntries.reverse()); // Newest first
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadChangeHistory();
  }

  trackByEntryId(index: number, entry: ChangeHistoryEntry): string {
    return entry.identifier;
  }

  getChangesArray(changes: Record<string, any>): any[] {
    return Object.keys(changes).map(field => ({
      field,
      oldValue: changes[field]?.oldValue,
      newValue: changes[field]?.newValue
    }));
  }

  getActionLabel(action: string): string {
    switch (action) {
      case 'CREATE':
        return 'Erstellt';
      case 'UPDATE':
        return 'Geändert';
      case 'DELETE':
        return 'Gelöscht';
      default:
        return 'Unbekannt';
    }
  }

  getActionIcon(action: string): string {
    switch (action) {
      case 'CREATE':
        return 'add_circle';
      case 'UPDATE':
        return 'edit';
      case 'DELETE':
        return 'delete';
      default:
        return 'help';
    }
  }

  getActionChipClass(action: string): string {
    switch (action) {
      case 'CREATE':
        return 'action-chip-create';
      case 'UPDATE':
        return 'action-chip-update';
      case 'DELETE':
        return 'action-chip-delete';
      default:
        return '';
    }
  }

  getActionIconClass(action: string): string {
    switch (action) {
      case 'CREATE':
        return 'action-icon-create';
      case 'UPDATE':
        return 'action-icon-update';
      case 'DELETE':
        return 'action-icon-delete';
      default:
        return '';
    }
  }

  getEntityLabel(entityType: EntityType): string {
    switch (entityType) {
      case EntityType.APPLICATION:
        return 'Applikation';
      case EntityType.INCIDENT:
        return 'Incident';
      case EntityType.USER:
        return 'Benutzer';
      default:
        return 'Entität';
    }
  }

  getChangeTooltip(change: any): string {
    if (change.oldValue && change.newValue) {
      return `${change.field}: "${change.oldValue}" → "${change.newValue}"`;
    } else if (change.newValue) {
      return `${change.field}: Neuer Wert "${change.newValue}"`;
    } else if (change.oldValue) {
      return `${change.field}: Wert "${change.oldValue}" entfernt`;
    }
    return change.field;
  }
}