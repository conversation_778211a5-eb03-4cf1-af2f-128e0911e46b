<div class="application-edit-dialog">
  <!-- Dialog Header -->
  <div class="dialog-header" mat-dialog-title>
    <div class="header-content">
      <mat-icon class="header-icon">edit_note</mat-icon>
      <h2 class="dialog-title">Applikation bearbeiten</h2>
    </div>
    <button
      mat-icon-button
      class="close-button"
      (click)="onCancel()"
      aria-label="Dialog schließen"
      matTooltip="Schließen">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <!-- Dialog Content -->
  <div class="dialog-content" mat-dialog-content>
    <!-- Loading Overlay -->
    <div class="loading-overlay" *ngIf="loading$ | async">
      <mat-spinner diameter="40" aria-label="Wird gespeichert..."></mat-spinner>
      <p class="loading-text">Änderungen werden gespeichert...</p>
    </div>

    <!-- Error Message -->
    <div class="error-banner" *ngIf="error$ | async as error">
      <mat-icon class="error-icon">error</mat-icon>
      <div class="error-content">
        <h4><PERSON><PERSON> beim Speichern</h4>
        <p>{{ error.message || 'Ein unbekannter Fehler ist aufgetreten.' }}</p>
      </div>
    </div>

    <!-- Edit Form -->
    <div class="form-container" [class.disabled]="loading$ | async">
      <app-application-edit-form
        [application]="data"
        [loading]="(loading$ | async) ?? false"
        [disabled]="(loading$ | async) ?? false"
        (formSubmit)="onSubmit($event)"
        (formCancel)="onCancel()">
      </app-application-edit-form>
    </div>
  </div>
</div>