import { ApplicationConfig, provideZoneChangeDetection, isDevMode, inject } from '@angular/core';
import { provideRouter } from '@angular/router';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { provideHttpClient, withInterceptors } from '@angular/common/http';

import { routes } from './app.routes';
import { provideStore } from '@ngrx/store';
import { provideEffects } from '@ngrx/effects';
import { provideStoreDevtools } from '@ngrx/store-devtools';
import { provideApollo } from 'apollo-angular';
import { InMemoryCache } from '@apollo/client/core';
import { HttpLink } from 'apollo-angular/http';

import { authFeature } from './store/auth/auth.feature';
import { AuthEffects } from './store/auth/auth.effects';
import { authInterceptor } from './core/interceptors/auth.interceptor';
import { incidentsFeature } from './store/incidents/incidents.feature';
import { IncidentsEffects } from './store/incidents/incidents.effects';
import { applicationsReducer } from './store/applications/applications.reducer';
import { ApplicationsEffects } from './store/applications/applications.effects';

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes),
    provideAnimationsAsync(),
    provideHttpClient(withInterceptors([authInterceptor])),
    provideStore({
      [authFeature.name]: authFeature.reducer,
      [incidentsFeature.name]: incidentsFeature.reducer,
      applications: applicationsReducer
    }),
    provideEffects([AuthEffects, IncidentsEffects, ApplicationsEffects]),
    provideStoreDevtools({
      maxAge: 25,
      logOnly: !isDevMode(),
      autoPause: true,
      trace: false,
      traceLimit: 75
    }),
    provideApollo(() => {
      const httpLink = inject(HttpLink);
      
      return {
        link: httpLink.create({
          uri: 'http://localhost:5079/graphql', // Backend URL corrected
        }),
        cache: new InMemoryCache({
          typePolicies: {
            Application: {
              keyFields: ['identifier'],
            },
            Incident: {
              keyFields: ['identifier'],
            },
          },
        }),
        defaultOptions: {
          watchQuery: {
            errorPolicy: 'all',
          },
          query: {
            errorPolicy: 'all',
          },
        },
      };
    })
  ]
};
