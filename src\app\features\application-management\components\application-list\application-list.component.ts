import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { Subject, takeUntil, debounceTime, distinctUntilChanged, startWith, Observable } from 'rxjs';

// Angular Material
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatChipsModule } from '@angular/material/chips';

// Models and Services
import { Application, ApplicationFilter } from '../../../../core/models/application.model';
import { ApplicationsFacade } from '../../../../store/applications/applications.facade';

// Components
import { ApplicationFilterComponent } from '../application-filter/application-filter.component';
import { ApplicationCreateDialogComponent } from '../../dialogs/application-create-dialog/application-create-dialog.component';
import { ApplicationEditDialogComponent } from '../../dialogs/application-edit-dialog/application-edit-dialog.component';
import { DeleteConfirmationDialogComponent } from '../../../../shared/components/delete-confirmation-dialog/delete-confirmation-dialog.component';

@Component({
  selector: 'app-application-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatTableModule,
    MatSortModule,
    MatPaginatorModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatDialogModule,
    MatTooltipModule,
    MatChipsModule,
    ApplicationFilterComponent
  ],
  templateUrl: './application-list.component.html',
  styleUrls: ['./application-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ApplicationListComponent implements OnInit, OnDestroy {
  @ViewChild(MatSort) set sort(sort: MatSort) {
    if (sort) {
      this.dataSource.sort = sort;
    }
  }
  @ViewChild(MatPaginator) set paginator(paginator: MatPaginator) {
    if (paginator) {
      this.dataSource.paginator = paginator;
    }
  }

  displayedColumns: string[] = ['name', 'description', 'status', 'createdAt', 'updatedAt', 'actions'];
  dataSource = new MatTableDataSource<Application>([]);
  
  loading$: Observable<boolean>;
  error$: Observable<any>;

  private destroy$ = new Subject<void>();
  private filter: ApplicationFilter = {};

  constructor(
    private applicationsFacade: ApplicationsFacade,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {
    this.loading$ = this.applicationsFacade.loading$;
    this.error$ = this.applicationsFacade.error$;
  }

  ngOnInit(): void {
    this.loadApplications();
    this.applicationsFacade.applications$.pipe(takeUntil(this.destroy$)).subscribe(applications => {
      this.dataSource.data = applications;
    });

    this.error$.pipe(takeUntil(this.destroy$)).subscribe((error: any) => {
      if (error) {
        this.showErrorMessage(error.message || 'Ein unbekannter Fehler ist aufgetreten.');
      }
    });
  }


  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadApplications(): void {
    this.applicationsFacade.loadApplications(this.filter);
  }

  onFilterChange(filter: ApplicationFilter): void {
    console.log('📥 ApplicationList.onFilterChange received filter:', filter);
    
    // For "all" filter (empty object), we need to reset isDeleted
    if (Object.keys(filter).length === 0) {
      // Empty filter means "show all" - remove isDeleted filter
      const { isDeleted, ...filterWithoutIsDeleted } = this.filter;
      this.filter = { ...filterWithoutIsDeleted };
      console.log('🔄 Reset isDeleted filter for "all" applications');
    } else {
      this.filter = { ...this.filter, ...filter };
    }
    
    console.log('🔄 Updated internal filter:', this.filter);
    this.loadApplications();
  }

  onSearchChange(searchTerm: string): void {
    console.log('🔍 ApplicationList.onSearchChange received searchTerm:', searchTerm);
    this.filter = { ...this.filter, searchTerm: searchTerm.trim() || undefined };
    console.log('🔄 Updated internal filter:', this.filter);
    this.loadApplications();
  }

  openAddDialog(): void {
    const dialogRef = this.dialog.open(ApplicationCreateDialogComponent, {
      width: '500px',
      disableClose: true
    });

    dialogRef.afterClosed().pipe(takeUntil(this.destroy$)).subscribe(result => {
      if (result) {
        this.showSuccessMessage('Applikation erfolgreich erstellt');
      }
    });
  }

  openEditDialog(application: Application): void {
    const dialogRef = this.dialog.open(ApplicationEditDialogComponent, {
      width: '500px',
      disableClose: true,
      data: application
    });

    dialogRef.afterClosed().pipe(takeUntil(this.destroy$)).subscribe(result => {
      if (result) {
        this.showSuccessMessage('Applikation erfolgreich aktualisiert');
      }
    });
  }

  openDeleteDialog(application: Application): void {
    const dialogRef = this.dialog.open(DeleteConfirmationDialogComponent, {
      width: '400px',
      data: {
        title: 'Applikation löschen',
        message: `Möchten Sie die Applikation "${application.name}" wirklich löschen?`,
        entityType: 'Application',
        entityId: application.identifier
      }
    });

    dialogRef.afterClosed().pipe(takeUntil(this.destroy$)).subscribe(result => {
      if (result) {
        this.deleteApplication(application.identifier);
      }
    });
  }

  private deleteApplication(identifier: string): void {
    this.applicationsFacade.deleteApplication(identifier);
  }

  getStatusChipColor(isDeleted: boolean): string {
    return isDeleted ? 'warn' : 'primary';
  }

  getStatusText(isDeleted: boolean): string {
    return isDeleted ? 'Gelöscht' : 'Aktiv';
  }

  formatDate(dateString: string): string {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('de-DE', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getResultsSummaryText(): string {
    const count = this.dataSource.data.length;
    const hasSearch = this.filter.searchTerm && this.filter.searchTerm.trim();
    const statusText = this.getStatusFilterText();
    
    if (hasSearch) {
      return `${count} ${count === 1 ? 'Applikation' : 'Applikationen'} gefunden für "${this.filter.searchTerm}" (${statusText})`;
    } else {
      return `${count} ${statusText} ${count === 1 ? 'Applikation' : 'Applikationen'}`;
    }
  }

  private getStatusFilterText(): string {
    if (this.filter.isDeleted === true) {
      return 'gelöschte';
    } else if (this.filter.isDeleted === false) {
      return 'aktive';
    } else {
      return 'gesamt';
    }
  }

  private showSuccessMessage(message: string): void {
    this.snackBar.open(message, 'Schließen', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showErrorMessage(message: string): void {
    this.snackBar.open(message, 'Schließen', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }
}