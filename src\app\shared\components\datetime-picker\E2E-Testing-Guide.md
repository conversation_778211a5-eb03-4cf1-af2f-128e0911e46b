# E2E Testing Guide für DateTime-Picker

Da Playwright nicht im Projekt konfiguriert ist, hier eine Anleitung für manuelle E2E-Tests und zukünftige Automatisierung.

## Manuelle Test-Szenarien

### 1. Grundfunktionalität
- [ ] Datum-Input wird angezeigt
- [ ] Zeit-Input wird angezeigt
- [ ] Datepicker-Toggle öffnet Kalender
- [ ] Datum kann ausgewählt werden
- [ ] Zeit kann eingegeben werden (HH:MM Format)
- [ ] Clear-Button erscheint bei gesetzten Werten
- [ ] Clear-Button löscht beide Werte

### 2. Validierung
- [ ] Required-Fehler bei Pflichtfeldern
- [ ] Ungültige Zeit wird abgelehnt
- [ ] Min/Max-Datum wird validiert
- [ ] Formular-Submit nur bei gültigen Werten

### 3. Accessibility
- [ ] Tab-Navigation funktioniert
- [ ] ARIA-Labels sind vorhanden
- [ ] Screen Reader Unterstützung
- [ ] Keyboard-Navigation im Datepicker
- [ ] Fehler werden angekündigt

### 4. Responsive Design
- [ ] Mobile Layout (< 600px): Vertikale Anordnung
- [ ] Tablet Layout: Horizontale Anordnung
- [ ] Desktop Layout: Optimale Abstände

### 5. Integration
- [ ] ngModel funktioniert
- [ ] Reactive Forms Integration
- [ ] Event-Emitter funktionieren
- [ ] ControlValueAccessor korrekt

## Automatisierte Tests Setup

### Playwright Installation
```bash
npm install -D @playwright/test
npx playwright install
```

### Cypress Alternative
```bash
npm install -D cypress
npx cypress open
```

### Test-Konfiguration
```typescript
// playwright.config.ts
import { defineConfig } from '@playwright/test';

export default defineConfig({
  testDir: './src/app/shared/components/datetime-picker',
  testMatch: '**/*.e2e.spec.ts',
  use: {
    baseURL: 'http://localhost:4200',
  },
});
```

## Test-Daten

### Gültige Eingaben
- Datum: Heute, Zukunft, Vergangenheit
- Zeit: 00:00, 12:00, 23:59, 14:30

### Ungültige Eingaben
- Zeit: 25:00, 12:70, abc:def
- Datum: Vor minDate, Nach maxDate

## Browser-Kompatibilität

### Zu testende Browser
- Chrome (Desktop/Mobile)
- Firefox (Desktop/Mobile)
- Safari (Desktop/Mobile)
- Edge (Desktop)

### Bekannte Einschränkungen
- HTML5 Time Input Support variiert
- Datepicker Styling unterschiedlich
- Mobile Touch-Interaktionen