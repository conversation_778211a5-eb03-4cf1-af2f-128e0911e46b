import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

// Angular Material Modules
import { MatTableModule } from '@angular/material/table';
import { MatSortModule } from '@angular/material/sort';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCardModule } from '@angular/material/card';
import { MatTabsModule } from '@angular/material/tabs';
import { MatChipsModule } from '@angular/material/chips';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatCheckboxModule } from '@angular/material/checkbox';

import { IncidentManagementRoutingModule } from './incident-management-routing.module';

// Import all components
import { IncidentListComponent } from './components/incident-list/incident-list.component';
import { IncidentCreateDialogComponent } from './components/incident-create-dialog/incident-create-dialog.component';
import { IncidentCreateFormComponent } from './components/incident-create-form/incident-create-form.component';
import { IncidentEditDialogComponent } from './components/incident-edit-dialog/incident-edit-dialog.component';
import { IncidentEditFormComponent } from './components/incident-edit-form/incident-edit-form.component';
import { ApplicationSelectorComponent } from './components/application-selector/application-selector.component';

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    IncidentManagementRoutingModule,
    
    // Angular Material
    MatTableModule,
    MatSortModule,
    MatPaginatorModule,
    MatButtonModule,
    MatIconModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatCardModule,
    MatTabsModule,
    MatChipsModule,
    MatTooltipModule,
    MatAutocompleteModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatCheckboxModule,
    
    // Standalone Components
    IncidentListComponent,
    IncidentCreateDialogComponent,
    IncidentCreateFormComponent,
    IncidentEditDialogComponent,
    IncidentEditFormComponent,
    ApplicationSelectorComponent
  ]
})
export class IncidentManagementModule { }