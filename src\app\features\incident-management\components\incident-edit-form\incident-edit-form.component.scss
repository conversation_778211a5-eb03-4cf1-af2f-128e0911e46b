.incident-edit-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 24px;
  max-width: 100%;

  .full-width {
    width: 100%;
  }

  .date-time-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }

    .datetime-field {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
      width: fit-content;
      min-width: 280px;

      .field-label {
        font-size: 0.875rem;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.87);
        margin-bottom: 0.25rem;

        &::after {
          content: ' *';
          color: #f44336;
        }

        &.optional::after {
          content: '';
        }
      }

      .error-message {
        font-size: 0.75rem;
        color: #f44336;
        margin-top: 0.25rem;
        margin-left: 0.75rem;
        line-height: 1.2;
      }
    }

    .date-field {
      width: 100%;
    }
  }

  .applications-section {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .section-label {
      font-weight: 500;
      color: var(--mat-text-primary);
      font-size: 0.875rem;
      margin-bottom: 4px;

      &::after {
        content: ' *';
        color: var(--mat-error);
      }
    }

    .error-message {
      color: var(--mat-error);
      font-size: 0.75rem;
      margin-top: 4px;
      display: flex;
      align-items: center;
      gap: 4px;

      &::before {
        content: '⚠';
        font-size: 0.875rem;
      }
    }
  }

  .resolution-section {
    padding: 16px 0;
    border-top: 1px solid var(--mat-divider-color);

    mat-checkbox {
      font-weight: 500;
    }
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 16px;
    border-top: 1px solid var(--mat-divider-color);

    button {
      min-width: 120px;

      mat-icon {
        margin-right: 8px;
        animation: spin 1s linear infinite;
      }
    }
  }

  // Form field customizations
  mat-form-field {
    .mat-mdc-form-field-subscript-wrapper {
      margin-top: 4px;
    }

    .mat-mdc-form-field-hint-wrapper {
      padding-top: 4px;
    }

    textarea {
      resize: vertical;
      min-height: 80px;
    }
  }

  // Error state styling
  .mat-mdc-form-field.mat-form-field-invalid {
    .mat-mdc-form-field-outline-thick {
      border-color: var(--mat-error);
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .incident-edit-form {
    padding: 16px;
    gap: 16px;

    .date-time-row {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .form-actions {
      flex-direction: column-reverse;
      gap: 8px;

      button {
        width: 100%;
      }
    }
  }
}

@media (max-width: 480px) {
  .incident-edit-form {
    padding: 12px;
    gap: 12px;

    .applications-section {
      .section-label {
        font-size: 0.8125rem;
      }
    }
  }
}

// Animation for loading spinner
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .incident-edit-form {
    .applications-section {
      .error-message {
        border: 1px solid var(--mat-error);
        padding: 8px;
        border-radius: 4px;
        background-color: var(--mat-error-container);
      }
    }

    .resolution-section,
    .form-actions {
      border-top-width: 2px;
    }
  }
}

// Dark theme adjustments
.dark-theme {
  .incident-edit-form {
    .applications-section {
      .section-label {
        color: var(--mat-text-primary-on-dark);
      }
    }

    .resolution-section,
    .form-actions {
      border-top-color: var(--mat-divider-color-on-dark);
    }
  }
}

// Focus management
.incident-edit-form {
  mat-form-field {
    &:focus-within {
      .mat-mdc-form-field-outline-thick {
        border-width: 2px;
      }
    }
  }

  button {
    &:focus {
      outline: 2px solid var(--mat-primary);
      outline-offset: 2px;
    }
  }
}

// Print styles
@media print {
  .incident-edit-form {
    .form-actions {
      display: none;
    }

    mat-form-field {
      break-inside: avoid;
    }
  }
}