// GraphQL Input Types for Application and Incident Operations
// These types should match the GraphQL schema on the backend

// Incident Type Enum
export enum IncidentType {
  STOERUNG = 'STOERUNG',
  WARTUNGSFENSTER = 'WARTUNGSFENSTER',
  KEINE_STOERUNG = 'KEINE_STOERUNG'
}

// Application Input Types
export interface CreateApplicationInput {
  name: string;
  description?: string;
}

export interface UpdateApplicationInput {
  identifier: string;
  name?: string;
  description?: string;
  isDeleted?: boolean;
}

// GraphQL Response Types
export interface IncidentResponse {
  identifier: string;
  title: string;
  type: IncidentType;
  description?: string;
  /** Start time - received as ISO string from GraphQL, can be converted to Date */
  startTime: string;
  /** Planned end time - received as ISO string from GraphQL, can be converted to Date */
  plannedEndTime?: string;
  /** Actual end time - received as ISO string from GraphQL, can be converted to Date */
  actualEndTime?: string;
  alternatives?: string;
  isResolved: boolean;
  /** Creation timestamp - received as ISO string from GraphQL, can be converted to Date */
  createdAt: string;
  /** Last update timestamp - received as ISO string from GraphQL, can be converted to Date */
  updatedAt: string;
  applications: ApplicationResponse[];
}

export interface ApplicationResponse {
  identifier: string;
  name: string;
  description?: string;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
}

// Mutation Response Types
export interface CreateApplicationResponse {
  createApplication: ApplicationResponse;
}

export interface UpdateApplicationResponse {
  updateApplication: ApplicationResponse;
}

export interface DeleteApplicationResponse {
  deleteApplication: ApplicationResponse;
}

// Query Response Types
export interface GetAllApplicationsResponse {
  allApplications: ApplicationResponse[];
}

export interface GetApplicationByIdResponse {
  application: ApplicationResponse;
}

export interface SearchApplicationsByNameResponse {
  applicationsByName: ApplicationResponse[];
}

export interface GetAllIncidentsResponse {
  incidents: IncidentResponse[];
}

export interface GetMyIncidentsResponse {
  myIncidents: IncidentResponse[];
}

// Incident Input Types
export interface CreateIncidentInput {
  title: string;
  type: IncidentType;
  description?: string;
  /** Start time - will be serialized to ISO string for GraphQL */
  startTime: string;
  /** Planned end time - will be serialized to ISO string for GraphQL */
  plannedEndTime?: string;
  alternatives?: string;
  applicationIds: string[];
}

export interface UpdateIncidentInput {
  identifier: string;
  title?: string;
  type?: IncidentType;
  description?: string;
  /** Start time - will be serialized to ISO string for GraphQL */
  startTime?: string;
  /** Planned end time - will be serialized to ISO string for GraphQL */
  plannedEndTime?: string;
  /** Actual end time - will be serialized to ISO string for GraphQL */
  actualEndTime?: string;
  alternatives?: string;
  applicationIds?: string[];
}

// Incident Mutation Response Types
export interface CreateIncidentResponse {
  createIncident: IncidentResponse;
}

export interface UpdateIncidentResponse {
  updateIncident: IncidentResponse;
}

export interface ResolveIncidentResponse {
  resolveIncident: IncidentResponse;
}

export interface DeleteIncidentResponse {
  deleteIncident: boolean;
}