import { Injectable } from '@angular/core';
import { Apollo } from 'apollo-angular';
import { Observable, map, catchError, throwError, of, delay } from 'rxjs';
import { FetchPolicy } from '@apollo/client/core';

import { Incident, IncidentFilter, UpdateIncidentInput, CreateIncidentInput } from '../models/incident.model';
import {
  GET_ALL_INCIDENTS,
  GET_MY_INCIDENTS
} from '../graphql/incident.queries';
import {
  UPDATE_INCIDENT,
  CREATE_INCIDENT,
  DELETE_INCIDENT
} from '../graphql/incident.mutations';
import { UpdateIncidentResponse, CreateIncidentResponse, DeleteIncidentResponse, GetAllIncidentsResponse } from '../graphql/types';

@Injectable({
  providedIn: 'root'
})
export class IncidentService {
  private useMockData = false; // Set to false when backend is available
  
  // Mock data for development
  private mockIncidents: Incident[] = [];

  constructor(private apollo: Apollo) {}

  /**
   * Get all incidents
   */
  getAllIncidents(): Observable<Incident[]> {
    console.log('🔍 IncidentService.getAllIncidents called');
    console.log('📊 useMockData flag:', this.useMockData);
    
    if (this.useMockData) {
      console.log('🎭 Using mock data for getAllIncidents');
      return of(this.mockIncidents).pipe(delay(300)); // Simulate network delay
    }

    console.log('🚀 Executing GraphQL query GET_ALL_INCIDENTS');

    return this.apollo.query<{ incidents: Incident[] }>({
      query: GET_ALL_INCIDENTS
    }).pipe(
      map(result => {
        console.log('✅ GraphQL query successful, raw result:', result);
        console.log('📋 Incidents data:', result.data.incidents);
        return result.data.incidents;
      }),
      catchError(error => {
        console.error('❌ GraphQL query failed in getAllIncidents:', error);
        return this.handleError(error);
      })
    );
  }

  /**
   * Get a single incident by ID
   */
  getIncidentById(id: string): Observable<Incident> {
    console.log('🔍 IncidentService.getIncidentById called with id:', id);
    console.log('📊 useMockData flag:', this.useMockData);

    if (this.useMockData) {
      console.log('🎭 Using mock data for getIncidentById');
      const incident = this.mockIncidents.find(i => i.identifier === id);
      if (!incident) {
        return throwError(() => new Error('Vorfall nicht gefunden'));
      }
      console.log('🎭 Mock found incident:', incident);
      return of(incident).pipe(delay(300)); // Simulate network delay
    }

    console.log('🚀 Executing GraphQL query GET_ALL_INCIDENTS to find incident by id');

    return this.apollo.query<GetAllIncidentsResponse>({
      query: GET_ALL_INCIDENTS,
      fetchPolicy: 'network-only' as FetchPolicy
    }).pipe(
      map(result => {
        console.log('✅ GraphQL query successful, raw result:', result);
        const incident = result.data.incidents.find(inc => inc.identifier === id);
        console.log('📋 Found incident:', incident);

        if (!incident) {
          throw new Error('Vorfall nicht gefunden');
        }
        return incident;
      }),
      catchError(error => {
        console.error('❌ GraphQL query failed in getIncidentById:', error);
        return this.handleError(error);
      })
    );
  }

  /**
   * Get incidents for the current user with optional filtering by resolution status
   */
  getMyIncidents(isResolved?: boolean): Observable<Incident[]> {
    console.log('🔍 IncidentService.getMyIncidents called with isResolved:', isResolved);
    console.log('📊 useMockData flag:', this.useMockData);
    
    if (this.useMockData) {
      console.log('🎭 Using mock data for getMyIncidents');
      let filteredIncidents = this.mockIncidents;
      
      if (isResolved !== undefined) {
        filteredIncidents = this.mockIncidents.filter(incident => 
          incident.isResolved === isResolved
        );
      }
      
      console.log('🎭 Mock filtered incidents:', filteredIncidents);
      return of(filteredIncidents).pipe(delay(300)); // Simulate network delay
    }

    console.log('🚀 Executing GraphQL query GET_MY_INCIDENTS');
    console.log('📝 GraphQL variables:', { isResolved });

    return this.apollo.query<{ myIncidents: Incident[] }>({
      query: GET_MY_INCIDENTS,
      variables: { isResolved },
      fetchPolicy: 'network-only' as FetchPolicy
    }).pipe(
      map(result => {
        console.log('✅ GraphQL query successful, raw result:', result);
        console.log('📋 My incidents data:', result.data.myIncidents);
        return result.data.myIncidents;
      }),
      catchError(error => {
        console.error('❌ GraphQL query failed in getMyIncidents:', error);
        return this.handleError(error);
      })
    );
  }

  /**
   * Create a new incident
   */
  createIncident(createInput: CreateIncidentInput): Observable<Incident> {
    console.log('🔍 IncidentService.createIncident called with input:', createInput);
    console.log('📊 useMockData flag:', this.useMockData);
    
    if (this.useMockData) {
      console.log('🎭 Using mock data for createIncident');
      const newIncident: Incident = {
        identifier: `incident-${Date.now()}`,
        title: createInput.title,
        type: createInput.type,
        description: createInput.description,
        startTime: createInput.startTime,
        plannedEndTime: createInput.plannedEndTime,
        actualEndTime: undefined,
        alternatives: createInput.alternatives,
        isResolved: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        applications: [] // Mock applications would be populated here
      };
      
      this.mockIncidents.push(newIncident);
      console.log('🎭 Mock created incident:', newIncident);
      return of(newIncident).pipe(delay(500));
    }

    console.log('🚀 Executing GraphQL mutation CREATE_INCIDENT');
    console.log('📝 GraphQL variables:', { input: createInput });

    return this.apollo.mutate<CreateIncidentResponse>({
      mutation: CREATE_INCIDENT,
      variables: { input: createInput }
    }).pipe(
      map(result => {
        console.log('✅ GraphQL mutation successful, raw result:', result);
        console.log('📋 Created incident data:', result.data?.createIncident);
        
        if (!result.data?.createIncident) {
          throw new Error('Keine Daten vom Server erhalten');
        }
        
        return result.data.createIncident;
      }),
      catchError(error => {
        console.error('❌ GraphQL mutation failed in createIncident:', error);
        return this.handleError(error);
      })
    );
  }

  /**
   * Update an existing incident
   */
  updateIncident(updateInput: UpdateIncidentInput): Observable<Incident> {
    console.log('🔍 IncidentService.updateIncident called with input:', updateInput);
    console.log('📊 useMockData flag:', this.useMockData);
    
    if (this.useMockData) {
      console.log('🎭 Using mock data for updateIncident');
      const incidentIndex = this.mockIncidents.findIndex(i => i.identifier === updateInput.identifier);
      
      if (incidentIndex === -1) {
        return throwError(() => new Error('Vorfall nicht gefunden'));
      }
      
      const updatedIncident: Incident = {
        ...this.mockIncidents[incidentIndex],
        ...(updateInput.title && { title: updateInput.title }),
        ...(updateInput.type && { type: updateInput.type }),
        ...(updateInput.description !== undefined && { description: updateInput.description }),
        ...(updateInput.startTime && { startTime: updateInput.startTime }),
        ...(updateInput.plannedEndTime !== undefined && { plannedEndTime: updateInput.plannedEndTime }),
        ...(updateInput.actualEndTime !== undefined && { actualEndTime: updateInput.actualEndTime }),
        ...(updateInput.alternatives !== undefined && { alternatives: updateInput.alternatives }),
        updatedAt: new Date().toISOString()
      };
      
      this.mockIncidents[incidentIndex] = updatedIncident;
      console.log('🎭 Mock updated incident:', updatedIncident);
      return of(updatedIncident).pipe(delay(500));
    }

    console.log('🚀 Executing GraphQL mutation UPDATE_INCIDENT');
    console.log('📝 GraphQL variables:', { input: updateInput });

    return this.apollo.mutate<UpdateIncidentResponse>({
      mutation: UPDATE_INCIDENT,
      variables: { input: updateInput },
      errorPolicy: 'all'
    }).pipe(
      map(result => {
        if (result.errors) {
          console.error('GraphQL errors:', result.errors);
          throw new Error(this.getGraphQLErrorMessage(result.errors));
        }
        
        if (!result.data?.updateIncident) {
          throw new Error('Keine Daten vom Server erhalten');
        }
        
        console.log('✅ Update successful:', result.data.updateIncident);
        return result.data.updateIncident;
      }),
      catchError(error => {
        console.error('❌ Update incident error:', error);
        return throwError(() => this.getErrorMessage(error));
      })
    );
  }

  /**
   * Delete an incident
   */
  deleteIncident(identifier: string): Observable<boolean> {
    console.log('🔍 IncidentService.deleteIncident called with identifier:', identifier);
    console.log('📊 useMockData flag:', this.useMockData);
    
    if (this.useMockData) {
      console.log('🎭 Using mock data for deleteIncident');
      const incidentIndex = this.mockIncidents.findIndex(i => i.identifier === identifier);
      
      if (incidentIndex === -1) {
        return throwError(() => new Error('Vorfall nicht gefunden'));
      }
      
      this.mockIncidents.splice(incidentIndex, 1);
      console.log('🎭 Mock deleted incident with identifier:', identifier);
      return of(true).pipe(delay(500));
    }

    console.log('🚀 Executing GraphQL mutation DELETE_INCIDENT');
    console.log('📝 GraphQL variables:', { identifier });

    return this.apollo.mutate<DeleteIncidentResponse>({
      mutation: DELETE_INCIDENT,
      variables: { identifier }
    }).pipe(
      map(result => {
        console.log('✅ GraphQL mutation successful, raw result:', result);
        console.log('📋 Delete result:', result.data?.deleteIncident);
        
        if (result.data?.deleteIncident === undefined) {
          throw new Error('Keine Antwort vom Server erhalten');
        }
        
        return result.data.deleteIncident;
      }),
      catchError(error => {
        console.error('❌ GraphQL mutation failed in deleteIncident:', error);
        return this.handleError(error);
      })
    );
  }

  /**
   * Handle GraphQL errors with German user messages
   */
  private handleError(error: any): Observable<never> {
    console.error('🚨 IncidentService Error Details:', error);
    console.error('🔍 Error type:', typeof error);
    console.error('🔍 Error constructor:', error.constructor.name);
    
    if (error.graphQLErrors) {
      console.error('📋 GraphQL Errors:', error.graphQLErrors);
    }
    
    if (error.networkError) {
      console.error('🌐 Network Error:', error.networkError);
    }
    
    if (error.message) {
      console.error('💬 Error Message:', error.message);
    }
    
    let errorMessage = this.getErrorMessage(error);
    
    console.error('🎯 Final error message to user:', errorMessage);
    return throwError(() => new Error(errorMessage));
  }

  /**
   * Get user-friendly German error message
   */
  private getErrorMessage(error: any): string {
    if (error.networkError) {
      return 'Netzwerkfehler. Bitte versuchen Sie es später erneut.';
    }
    
    if (error.graphQLErrors?.length > 0) {
      return this.getGraphQLErrorMessage(error.graphQLErrors);
    }
    
    return error.message || 'Ein unbekannter Fehler ist aufgetreten.';
  }

  /**
   * Get user-friendly German GraphQL error message
   */
  private getGraphQLErrorMessage(errors: readonly any[]): string {
    const errorMessages = errors.map(error => {
      switch (error.extensions?.code) {
        case 'VALIDATION_ERROR':
          return 'Validierungsfehler: Bitte überprüfen Sie Ihre Eingaben.';
        case 'NOT_FOUND':
          return 'Der Vorfall wurde nicht gefunden.';
        case 'UNAUTHORIZED':
          return 'Sie haben keine Berechtigung für diese Aktion.';
        case 'FORBIDDEN':
          return 'Zugriff verweigert. Sie haben nicht die erforderlichen Rechte.';
        case 'CONFLICT':
          return 'Konflikt: Der Vorfall wurde bereits von einem anderen Benutzer geändert.';
        case 'BAD_REQUEST':
          return 'Ungültige Anfrage. Bitte überprüfen Sie Ihre Eingaben.';
        case 'INTERNAL_ERROR':
          return 'Interner Serverfehler. Bitte versuchen Sie es später erneut.';
        default:
          return error.message || 'GraphQL-Fehler aufgetreten.';
      }
    });
    
    return errorMessages.join(' ');
  }
}