.application-detail-container {
  padding: 24px;
  max-width: 1000px;
  margin: 0 auto;

  .detail-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 24px;

    .back-button {
      color: #1976d2;
    }

    h2 {
      margin: 0;
      color: #1976d2;
      font-weight: 500;
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px;
    gap: 16px;

    p {
      margin: 0;
      color: #666;
    }
  }

  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    padding: 48px;
    background-color: #ffebee;
    border: 1px solid #f44336;
    border-radius: 8px;
    color: #c62828;
    text-align: center;

    mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
    }

    span {
      font-size: 16px;
      margin-bottom: 8px;
    }
  }

  .detail-content {
    .info-card {
      margin-bottom: 24px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      mat-card-header {
        mat-card-title {
          display: flex;
          align-items: center;
          gap: 16px;
          flex-wrap: wrap;

          .status-chip {
            font-size: 12px;
            height: 24px;
          }
        }
      }

      .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 16px;
        margin-top: 16px;

        .info-item {
          display: flex;
          flex-direction: column;
          gap: 4px;

          label {
            font-weight: 600;
            color: #666;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }

          span {
            font-size: 16px;
            color: #333;
            word-break: break-word;

            &.identifier {
              font-family: 'Courier New', monospace;
              background-color: #f5f5f5;
              padding: 4px 8px;
              border-radius: 4px;
              font-size: 14px;
            }
          }
        }
      }

      mat-card-actions {
        display: flex;
        gap: 12px;
        padding: 16px 24px;

        button {
          display: flex;
          align-items: center;
          gap: 8px;
        }
      }
    }

    .detail-tabs {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;

      .tab-content {
        padding: 24px;
        min-height: 200px;

        p {
          margin: 0 0 16px 0;
          color: #666;

          &:last-child {
            margin-bottom: 0;
          }

          em {
            color: #999;
            font-style: italic;
          }
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .application-detail-container {
    padding: 16px;

    .detail-content {
      .info-card {
        .info-grid {
          grid-template-columns: 1fr;
          gap: 12px;
        }

        mat-card-actions {
          flex-direction: column;
          align-items: stretch;

          button {
            justify-content: center;
          }
        }
      }

      .detail-tabs {
        .tab-content {
          padding: 16px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .application-detail-container {
    padding: 8px;

    .detail-header {
      h2 {
        font-size: 20px;
      }
    }

    .detail-content {
      .info-card {
        mat-card-header {
          mat-card-title {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
          }
        }
      }
    }
  }
}