import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { of } from 'rxjs';

import { IncidentEditFormComponent } from './incident-edit-form.component';
import { ApplicationService } from '../../../../core/services/application.service';
import { Incident, IncidentType, UpdateIncidentInput } from '../../../../core/models/incident.model';
import { Application } from '../../../../core/models/application.model';

describe('IncidentEditFormComponent', () => {
  let component: IncidentEditFormComponent;
  let fixture: ComponentFixture<IncidentEditFormComponent>;
  let mockApplicationService: jasmine.SpyObj<ApplicationService>;

  const mockApplications: Application[] = [
    {
      identifier: '1',
      name: 'Test App 1',
      description: 'Test Description 1',
      isDeleted: false,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    }
  ];

  const mockIncident: Incident = {
    identifier: 'test-id',
    title: 'Test Incident',
    type: IncidentType.STOERUNG,
    description: 'Test Description',
    startTime: '2024-01-01T10:00:00Z',
    plannedEndTime: '2024-01-01T12:00:00Z',
    actualEndTime: undefined,
    alternatives: 'Test Alternatives',
    isResolved: false,
    createdAt: '2024-01-01T09:00:00Z',
    updatedAt: '2024-01-01T09:00:00Z',
    applications: mockApplications
  };

  beforeEach(async () => {
    const spy = jasmine.createSpyObj('ApplicationService', ['getAllApplications', 'searchApplicationsByName']);

    await TestBed.configureTestingModule({
      imports: [
        IncidentEditFormComponent,
        ReactiveFormsModule,
        NoopAnimationsModule
      ],
      providers: [
        { provide: ApplicationService, useValue: spy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(IncidentEditFormComponent);
    component = fixture.componentInstance;
    mockApplicationService = TestBed.inject(ApplicationService) as jasmine.SpyObj<ApplicationService>;

    mockApplicationService.getAllApplications.and.returnValue(of(mockApplications));
    mockApplicationService.searchApplicationsByName.and.returnValue(of(mockApplications));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with default values', () => {
    fixture.detectChanges();
    
    expect(component.editForm).toBeDefined();
    expect(component.editForm.get('title')?.value).toBe('');
    expect(component.editForm.get('type')?.value).toBe(IncidentType.STOERUNG);
    expect(component.editForm.get('isResolved')?.value).toBe(false);
  });

  it('should populate form when incident is provided', () => {
    component.incident = mockIncident;
    fixture.detectChanges();
    
    expect(component.editForm.get('title')?.value).toBe(mockIncident.title);
    expect(component.editForm.get('type')?.value).toBe(mockIncident.type);
    expect(component.editForm.get('description')?.value).toBe(mockIncident.description);
    expect(component.editForm.get('applicationIds')?.value).toEqual(mockIncident.applications.map(a => a.identifier));
  });

  it('should emit formSubmit when form is valid and submitted', () => {
    spyOn(component.formSubmit, 'emit');
    component.incident = mockIncident;
    fixture.detectChanges();

    component.editForm.patchValue({
      title: 'Updated Title',
      type: IncidentType.WARTUNGSFENSTER,
      applicationIds: [mockApplications[0].identifier]
    });

    component.onSubmit();

    expect(component.formSubmit.emit).toHaveBeenCalled();
    const emittedValue = (component.formSubmit.emit as jasmine.Spy).calls.mostRecent().args[0] as UpdateIncidentInput;
    expect(emittedValue.title).toBe('Updated Title');
    expect(emittedValue.type).toBe(IncidentType.WARTUNGSFENSTER);
  });

  it('should not emit formSubmit when form is invalid', () => {
    spyOn(component.formSubmit, 'emit');
    component.incident = mockIncident;
    fixture.detectChanges();

    component.editForm.patchValue({
      title: '', // Invalid - required field
    });

    component.onSubmit();

    expect(component.formSubmit.emit).not.toHaveBeenCalled();
  });

  it('should emit formCancel when cancel is clicked', () => {
    spyOn(component.formCancel, 'emit');
    
    component.onCancel();
    
    expect(component.formCancel.emit).toHaveBeenCalled();
  });

  it('should return correct field error messages', () => {
    fixture.detectChanges();
    
    const titleControl = component.editForm.get('title');
    titleControl?.markAsTouched();
    titleControl?.setErrors({ required: true });
    
    expect(component.getFieldError('title')).toBe('Titel ist erforderlich');
    
    titleControl?.setErrors({ maxlength: { requiredLength: 200, actualLength: 250 } });
    expect(component.getFieldError('title')).toBe('Maximal 200 Zeichen erlaubt');
  });

  it('should validate form correctly', () => {
    fixture.detectChanges();
    
    // Form should be invalid initially (empty title)
    expect(component.isFormValid).toBeFalsy();
    
    // Fill required fields
    component.editForm.patchValue({
      title: 'Test Title',
      startTime: new Date(),
      applicationIds: [mockApplications[0].identifier]
    });
    
    expect(component.isFormValid).toBeTruthy();
  });

  it('should show application error when no applications selected', () => {
    component.editForm.get('applicationIds')?.setValue([]);
    component.editForm.get('applicationIds')?.markAsTouched();
    
    expect(component.hasApplicationError).toBeTruthy();
  });

  it('should parse and format dates correctly', () => {
    const testDate = new Date('2024-01-01T10:00:00Z');
    
    // Test private method through component behavior
    component.incident = {
      ...mockIncident,
      startTime: testDate.toISOString()
    };
    
    fixture.detectChanges();
    
    expect(component.editForm.get('startTime')?.value).toEqual(testDate);
  });

  it('should get correct incident type labels', () => {
    expect(component.getIncidentTypeLabel(IncidentType.STOERUNG)).toBe('Störung');
    expect(component.getIncidentTypeLabel(IncidentType.WARTUNGSFENSTER)).toBe('Wartungsfenster');
    expect(component.getIncidentTypeLabel(IncidentType.KEINE_STOERUNG)).toBe('Keine Störung');
  });

  it('should handle field error checking correctly', () => {
    fixture.detectChanges();
    
    const titleControl = component.editForm.get('title');
    
    // No error initially
    expect(component.hasFieldError('title')).toBeFalsy();
    
    // Error when touched and invalid
    titleControl?.markAsTouched();
    titleControl?.setErrors({ required: true });
    
    expect(component.hasFieldError('title')).toBeTruthy();
  });
});