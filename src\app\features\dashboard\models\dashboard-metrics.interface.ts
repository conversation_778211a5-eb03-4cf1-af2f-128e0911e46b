export interface DashboardMetrics {
  totalIncidents: number;
  activeIncidents: number;
  resolvedIncidents: number;
  plannedMaintenance: number;
  totalApplications: number;
  activeApplications: number;
  recentActivity: number;
}

import { Incident } from '../../../core/models/incident.model';
import { Application } from '../../../core/models/application.model';

export interface DashboardData {
  incidents: Incident[];
  applications: Application[];
  isLoading: boolean;
  hasError: boolean;
  errorMessage: string | null;
  metrics: DashboardMetrics;
  activeIncidents: Incident[];
  recentApplications: Application[];
}