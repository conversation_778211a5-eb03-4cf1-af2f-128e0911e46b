# AI Prompt Requirements Document (AI-PRD) - Störungs-Buddy Frontend

This document provides all necessary information, rules, and requirements for an AI agent to develop the Störungs-Buddy Frontend application.

## 1. Project Overview

The "Störungs-Buddy" project aims to create a robust administration user interface (UI) and a backend API. This document focuses on the **Frontend Admin-UI**.

- **Primary Goal**: Develop an Admin-UI for managing system master data, incident reports, and maintenance windows.
- **Target Audience**: System administrators responsible for data and incident management.
- **End Users (Indirect)**: Employees who consume the data via a separate frontend.

## 2. Guiding Principles & Rules

All development must adhere to the rules defined in the `.roo/rules/` directory. These rules are the single source of truth for coding standards, architecture, and technology constraints.

- **[Clean Code Rules](/.roo/rules/clean-code.md)**: Guidelines for writing self-explanatory, simple, and maintainable code.
- **[Frontend Technology Rules](/.roo/rules/frontend-rules-angular-ngrx-tailwind-scss.md)**: Specific standards for using Angular, NgRx, and SCSS.
- **[Technical Constraints](/.roo/rules/technical-constraints.md)**: The definitive guide to the project's technology stack and architecture.
- **[Operating System](/.roo/rules/operating-system.md)**: All terminal commands must be compatible with Windows Powershell.

## 3. Technology Stack

The technical foundation is defined in detail in [`/.roo/rules/technical-constraints.md`](/.roo/rules/technical-constraints.md). A summary is provided below:

- **Framework**: Angular 19 (Standalone Components)
- **State Management**: NgRx (Store, Effects)
- **UI Framework**: Angular Material (Indigo-Pink Theme)
- **API Integration**: Apollo Client for GraphQL
- **Package Manager**: npm
- **Node.js**: 20.19.1
- **Angular CLI**: 19.2.13

## 4. Development Workflow & Commands

Use the Angular CLI for all development tasks.

- **Start Development Server**:
  ```bash
  ng serve
  ```
  Navigate to `http://localhost:4200/`.

- **Build for Production**:
  ```bash
  ng build
  ```
  Artifacts are stored in the `dist/` directory.

- **Run Unit Tests**:
  ```bash
  ng test
  ```

- **Generate Components**:
  ```bash
  ng generate component component-name
  ```

## 5. API Specification (GraphQL)

The frontend communicates with a GraphQL backend.

- **Endpoint**: `http://localhost:5079/graphql`
- **Authentication**: oAuth2 (MVP: Simple login form).

### Example Operations:

**Query Systems:**
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"query": "{ systems { systemID name description status } }"}' \
  http://localhost:5079/graphql
```

**Create a System:**
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"query": "mutation { createSystem(input: { name: \"Test System\", description: \"Test Description\", status: OPERATIONAL }) { systemID name description status } }"}' \
  http://localhost:5079/graphql
```

## 6. MVP Features (Actionable Prompts)

The following features constitute the Minimum Viable Product (MVP). They should be implemented sequentially.

### F0: Admin-UI Base Setup
**Prompt**: `Initialize the core Admin-UI structure. This includes setting up the main application layout with navigation, implementing the Apollo Client for GraphQL communication, and creating a basic authentication service.`

- [ ] Build Angular project structure.
- [ ] Set up Apollo Client for GraphQL communication.
- [ ] Implement authentication service.
- [ ] Create base layout and navigation.

### F1: Master Data Management
**Prompt**: `Implement the master data management feature. Create a CRUD interface for "Applications". Administrators must be able to add, edit, and remove applications. The interface should be a non-hierarchical list.`

- [ ] Create components for listing, adding, editing, and deleting applications.
- [ ] Integrate with the GraphQL API for all CRUD operations.

### F2: Incident & Maintenance Management
**Prompt**: `Implement the management feature for incident and maintenance reports. Administrators must be able to create, edit, and resolve these reports. The system must distinguish between "Störung" (Incident) and "Wartung" (Maintenance) and allow setting a priority level (e.g., High-Critical, Normal).`

- [ ] Create components to manage incident and maintenance reports.
- [ ] Implement forms for creating/editing reports with fields for type and priority.
- [ ] Connect to the GraphQL API to persist changes.

### F3: User Subscription Management
**Prompt**: `Implement the user subscription management feature. Create a view for administrators to see and manage which users are subscribed to which systems. Initially, all registered users are subscribed to all applications. The administrator should be able to modify these assignments.`

- [ ] Create a component to display user-to-system mappings.
- [ ] Implement functionality for an administrator to manage these subscriptions.
- [ ] Note: Users are sourced from a list of authenticated users.