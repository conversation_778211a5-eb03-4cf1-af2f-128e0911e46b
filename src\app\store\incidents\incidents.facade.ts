import { Injectable, inject } from '@angular/core';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { Incident, IncidentType, UpdateIncidentInput, CreateIncidentInput } from '../../core/models/incident.model';
import * as IncidentsActions from './incidents.actions';
import * as IncidentsSelectors from './incidents.selectors';

@Injectable({
  providedIn: 'root'
})
export class IncidentsFacade {
  private store = inject(Store);

  // Selectors
  readonly allIncidents$ = this.store.select(IncidentsSelectors.selectAllIncidents);
  readonly isLoading$ = this.store.select(IncidentsSelectors.selectIncidentsLoading);
  readonly error$ = this.store.select(IncidentsSelectors.selectIncidentsError);
  readonly resolvedIncidents$ = this.store.select(IncidentsSelectors.selectResolvedIncidents);
  readonly unresolvedIncidents$ = this.store.select(IncidentsSelectors.selectUnresolvedIncidents);
  readonly incidentsStatus$ = this.store.select(IncidentsSelectors.selectIncidentsStatus);
  
  // Create selectors
  readonly isCreating$ = this.store.select(IncidentsSelectors.selectIncidentsCreating);
  readonly createError$ = this.store.select(IncidentsSelectors.selectIncidentsCreateError);
  
  // Update selectors
  readonly isUpdating$ = this.store.select(IncidentsSelectors.selectIncidentsUpdating);
  readonly updateError$ = this.store.select(IncidentsSelectors.selectIncidentsUpdateError);
  
  // Delete selectors
  readonly isDeleting$ = this.store.select(IncidentsSelectors.selectIncidentsDeleting);
  readonly deleteError$ = this.store.select(IncidentsSelectors.selectIncidentsDeleteError);

  // Selected incident selectors
  readonly selectedIncident$ = this.store.select(IncidentsSelectors.selectSelectedIncident);
  readonly selectedIncidentLoading$ = this.store.select(IncidentsSelectors.selectSelectedIncidentLoading);
  readonly selectedIncidentError$ = this.store.select(IncidentsSelectors.selectSelectedIncidentError);

  // Actions
  loadIncidents(): void {
    this.store.dispatch(IncidentsActions.loadIncidents());
  }

  loadIncident(id: string): void {
    this.store.dispatch(IncidentsActions.loadIncident({ id }));
  }

  loadMyIncidents(isResolved?: boolean): void {
    this.store.dispatch(IncidentsActions.loadMyIncidents({ isResolved }));
  }

  createIncident(createInput: CreateIncidentInput): void {
    this.store.dispatch(IncidentsActions.createIncident({ createInput }));
  }

  updateIncident(updateInput: UpdateIncidentInput): void {
    this.store.dispatch(IncidentsActions.updateIncident({ updateInput }));
  }

  deleteIncident(identifier: string): void {
    this.store.dispatch(IncidentsActions.deleteIncident({ identifier }));
  }

  clearIncidentsError(): void {
    this.store.dispatch(IncidentsActions.clearIncidentsError());
  }

  // Convenience methods
  getIncidentsByType(type: IncidentType): Observable<Incident[]> {
    return this.store.select(IncidentsSelectors.selectIncidentsByType(type));
  }
}