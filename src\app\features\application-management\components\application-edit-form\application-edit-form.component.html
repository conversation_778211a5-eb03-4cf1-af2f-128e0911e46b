<form [formGroup]="applicationForm" (ngSubmit)="onSubmit()" class="application-edit-form">
  <!-- Name Field -->
  <mat-form-field appearance="outline" class="full-width">
    <mat-label>Name *</mat-label>
    <input
      matInput
      formControlName="name"
      placeholder="Name der Applikation eingeben"
      [disabled]="disabled"
      maxlength="100"
      aria-label="Name der Applikation">
    <mat-hint align="end">{{ applicationForm.get('name')?.value?.length || 0 }}/100</mat-hint>
    <mat-error *ngIf="applicationForm.get('name')?.hasError('required')">
      Name ist erforderlich.
    </mat-error>
    <mat-error *ngIf="applicationForm.get('name')?.hasError('uniqueName')">
      Name muss eindeutig sein.
    </mat-error>
  </mat-form-field>

  <!-- Description Field -->
  <mat-form-field appearance="outline" class="full-width">
    <mat-label>Beschreibung *</mat-label>
    <textarea
      matInput
      formControlName="description"
      placeholder="Detaillierte Beschreibung der Applikation"
      [disabled]="disabled"
      maxlength="500"
      rows="4"
      aria-label="Beschreibung der Applikation">
    </textarea>
    <mat-hint align="end">{{ applicationForm.get('description')?.value?.length || 0 }}/500</mat-hint>
    <mat-error *ngIf="applicationForm.get('description')?.hasError('required')">
      Beschreibung ist erforderlich.
    </mat-error>
  </mat-form-field>

  <!-- Form Actions -->
  <div class="form-actions">
    <button
      type="button"
      mat-button
      (click)="onCancel()"
      [disabled]="loading"
      aria-label="Bearbeitung abbrechen">
      Abbrechen
    </button>
    
    <button
      type="submit"
      mat-raised-button
      color="primary"
      [disabled]="applicationForm.invalid || !hasChanges() || loading"
      aria-label="Änderungen speichern">
      <mat-icon *ngIf="loading">hourglass_empty</mat-icon>
      <span *ngIf="!loading">Speichern</span>
      <span *ngIf="loading">Wird gespeichert...</span>
    </button>
  </div>
</form>