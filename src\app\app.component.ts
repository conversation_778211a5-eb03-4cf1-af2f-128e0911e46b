import { Component, inject, OnInit } from '@angular/core';
import { MainLayoutComponent } from './shared/components/layout/main-layout/main-layout.component';
import { AuthFacade } from './store/auth/auth.facade';

@Component({
  selector: 'app-root',
  imports: [MainLayoutComponent],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})
export class AppComponent implements OnInit {
  title = 'stoerungsbuddy-frontend';
  
  private authFacade = inject(AuthFacade);

  ngOnInit() {
    // Initialize auth state on app startup (hydrate from localStorage)
    this.authFacade.hydrateAuth();
  }
}
