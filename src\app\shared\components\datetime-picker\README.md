# DateTime-Picker Komponente

Eine wiederverwendbare Angular-Komponente für die Eingabe von Datum und Uhrzeit, basierend auf Angular Material.

## Features

- ✅ **Angular Material Integration**: Nutzt Material DatePicker und native HTML5 Time Input
- ✅ **Deutsche Lokalisierung**: Standardmäßig auf Deutsch konfiguriert
- ✅ **Accessibility (WCAG 2.1)**: Vollständige Barrierefreiheit mit ARIA-Labels und Keyboard-Navigation
- ✅ **Reactive Forms**: Vollständige Integration mit Angular Reactive Forms
- ✅ **ControlValueAccessor**: Unterstützt ngModel und FormControl
- ✅ **Validierung**: Eingebaute Validierung für Datum, Zeit und Pflichtfelder
- ✅ **Responsive Design**: Optimiert für Desktop und Mobile
- ✅ **TypeScript Strict**: Vollständige Typsicherheit
- ✅ **DateTime Utilities**: Integration mit den DateTime-Utilities aus Task 1

## Installation

Die Komponente ist bereits im `shared/components` Verzeichnis verfügbar und kann direkt importiert werden:

```typescript
import { DateTimePickerComponent } from './shared/components/datetime-picker/datetime-picker.component';

@Component({
  // ...
  imports: [DateTimePickerComponent]
})
export class MyComponent { }
```

## Grundlegende Verwendung

### Template-Driven Forms

```html
<app-datetime-picker
  [(ngModel)]="selectedDateTime"
  [required]="true"
  dateLabel="Startzeit"
  timeLabel="Uhrzeit">
</app-datetime-picker>
```

### Reactive Forms

```typescript
// Component
export class MyComponent {
  form = this.fb.group({
    startTime: [null, Validators.required],
    endTime: [null]
  });

  constructor(private fb: FormBuilder) {}
}
```

```html
<!-- Template -->
<form [formGroup]="form">
  <app-datetime-picker
    formControlName="startTime"
    dateLabel="Startzeit"
    timeLabel="Uhrzeit"
    [required]="true">
  </app-datetime-picker>
  
  <app-datetime-picker
    formControlName="endTime"
    dateLabel="Endzeit"
    timeLabel="Uhrzeit">
  </app-datetime-picker>
</form>
```

## API Referenz

### Input Properties

| Property | Type | Default | Beschreibung |
|----------|------|---------|--------------|
| `dateLabel` | `string` | `'Datum'` | Label für das Datumsfeld |
| `timeLabel` | `string` | `'Uhrzeit'` | Label für das Zeitfeld |
| `datePlaceholder` | `string` | `'TT.MM.JJJJ'` | Placeholder für das Datumsfeld |
| `timePlaceholder` | `string` | `'HH:MM'` | Placeholder für das Zeitfeld |
| `required` | `boolean` | `false` | Ob die Eingabe erforderlich ist |
| `disabled` | `boolean` | `false` | Ob die Komponente deaktiviert ist |
| `minDate` | `Date \| null` | `null` | Minimales erlaubtes Datum |
| `maxDate` | `Date \| null` | `null` | Maximales erlaubtes Datum |
| `showClearButton` | `boolean` | `true` | Ob der Löschen-Button angezeigt wird |

### Accessibility Properties

| Property | Type | Default | Beschreibung |
|----------|------|---------|--------------|
| `dateAriaLabel` | `string` | `'Datum auswählen'` | ARIA-Label für Datumsfeld |
| `timeAriaLabel` | `string` | `'Uhrzeit eingeben'` | ARIA-Label für Zeitfeld |
| `clearAriaLabel` | `string` | `'Datum und Uhrzeit löschen'` | ARIA-Label für Löschen-Button |
| `clearTooltip` | `string` | `'Löschen'` | Tooltip für Löschen-Button |

### Error Message Properties

| Property | Type | Default | Beschreibung |
|----------|------|---------|--------------|
| `requiredErrorMessage` | `string` | `'Dieses Feld ist erforderlich'` | Fehlermeldung für Pflichtfelder |
| `invalidDateErrorMessage` | `string` | `'Ungültiges Datum'` | Fehlermeldung für ungültiges Datum |
| `invalidTimeErrorMessage` | `string` | `'Ungültige Uhrzeit (Format: HH:MM)'` | Fehlermeldung für ungültige Zeit |
| `minDateErrorMessage` | `string` | `'Datum liegt vor dem erlaubten Minimum'` | Fehlermeldung für zu frühes Datum |
| `maxDateErrorMessage` | `string` | `'Datum liegt nach dem erlaubten Maximum'` | Fehlermeldung für zu spätes Datum |

### Output Events

| Event | Type | Beschreibung |
|-------|------|--------------|
| `dateTimeChange` | `EventEmitter<Date \| null>` | Wird ausgelöst wenn sich das kombinierte DateTime ändert |
| `dateChange` | `EventEmitter<Date \| null>` | Wird ausgelöst wenn sich nur das Datum ändert |
| `timeChange` | `EventEmitter<string>` | Wird ausgelöst wenn sich nur die Zeit ändert |

### Public Methods

| Method | Return Type | Beschreibung |
|--------|-------------|--------------|
| `clear()` | `void` | Löscht alle Eingaben |
| `focus()` | `void` | Setzt den Fokus auf das Datumsfeld |
| `validate()` | `ValidationErrors \| null` | Validiert die aktuelle Eingabe |

### Public Properties

| Property | Type | Beschreibung |
|----------|------|--------------|
| `value` | `Date \| null` | Der aktuelle kombinierte DateTime-Wert |
| `hasErrors` | `boolean` | Ob die Komponente Validierungsfehler hat |
| `isValid` | `boolean` | Ob die Komponente gültig ist |

## Erweiterte Verwendung

### Mit Validierung

```typescript
export class IncidentFormComponent {
  form = this.fb.group({
    startTime: [null, [Validators.required]],
    endTime: [null]
  }, {
    validators: [this.dateRangeValidator]
  });

  // Custom Validator für Datumsbereich
  dateRangeValidator(control: AbstractControl): ValidationErrors | null {
    const startTime = control.get('startTime')?.value;
    const endTime = control.get('endTime')?.value;
    
    if (startTime && endTime && startTime >= endTime) {
      return { dateRange: 'Endzeit muss nach Startzeit liegen' };
    }
    
    return null;
  }
}
```

```html
<form [formGroup]="form">
  <app-datetime-picker
    formControlName="startTime"
    dateLabel="Störungsbeginn"
    timeLabel="Uhrzeit"
    [required]="true"
    [minDate]="today">
  </app-datetime-picker>
  
  <app-datetime-picker
    formControlName="endTime"
    dateLabel="Geplantes Ende"
    timeLabel="Uhrzeit"
    [minDate]="form.get('startTime')?.value">
  </app-datetime-picker>
  
  @if (form.hasError('dateRange')) {
    <mat-error>{{ form.getError('dateRange') }}</mat-error>
  }
</form>
```

### Mit Event Handling

```typescript
export class MyComponent {
  selectedDateTime: Date | null = null;

  onDateTimeChange(dateTime: Date | null): void {
    console.log('DateTime changed:', dateTime);
    
    if (dateTime) {
      // Verwende DateTime-Utilities für Formatierung
      const formatted = formatDateTime(dateTime, 'de-DE');
      console.log('Formatted:', formatted);
      
      // Prüfe ob in der Zukunft
      const isFuture = isFutureDateTime(dateTime);
      console.log('Is future:', isFuture);
    }
  }

  onDateChange(date: Date | null): void {
    console.log('Date changed:', date);
  }

  onTimeChange(time: string): void {
    console.log('Time changed:', time);
  }
}
```

```html
<app-datetime-picker
  [(ngModel)]="selectedDateTime"
  (dateTimeChange)="onDateTimeChange($event)"
  (dateChange)="onDateChange($event)"
  (timeChange)="onTimeChange($event)">
</app-datetime-picker>
```

### Programmatische Steuerung

```typescript
export class MyComponent {
  @ViewChild(DateTimePickerComponent) dateTimePicker!: DateTimePickerComponent;

  setCurrentDateTime(): void {
    this.dateTimePicker.writeValue(new Date());
  }

  clearDateTime(): void {
    this.dateTimePicker.clear();
  }

  focusDateTime(): void {
    this.dateTimePicker.focus();
  }

  validateDateTime(): void {
    const errors = this.dateTimePicker.validate();
    if (errors) {
      console.log('Validation errors:', errors);
    }
  }

  checkStatus(): void {
    console.log('Has errors:', this.dateTimePicker.hasErrors);
    console.log('Is valid:', this.dateTimePicker.isValid);
    console.log('Current value:', this.dateTimePicker.value);
  }
}
```

## Integration mit DateTime-Utilities

Die Komponente nutzt die DateTime-Utilities aus Task 1:

```typescript
import { 
  formatDateTime, 
  formatDateForInput, 
  createDateFromInputs,
  ensureDate,
  isPastDateTime,
  isFutureDateTime 
} from '../../../core/utils/datetime.utils';

// Beispiel: Formatierung für Anzeige
const displayValue = formatDateTime(dateTimePickerValue, 'de-DE');

// Beispiel: Validierung
const isPast = isPastDateTime(dateTimePickerValue);
const isFuture = isFutureDateTime(dateTimePickerValue);
```

## Styling und Theming

Die Komponente nutzt Angular Material Theming und kann über CSS-Variablen angepasst werden:

```scss
// Custom Styling
app-datetime-picker {
  .datetime-picker-container {
    gap: 16px; // Abstand zwischen Feldern
  }
  
  .date-field {
    flex: 2; // Mehr Platz für Datum
  }
  
  .time-field {
    flex: 1; // Weniger Platz für Zeit
  }
}

// Responsive Anpassungen
@media (max-width: 600px) {
  app-datetime-picker .datetime-picker-container {
    flex-direction: column;
  }
}
```

## Accessibility Features

- **Keyboard Navigation**: Vollständige Tastaturnavigation
- **Screen Reader**: ARIA-Labels und -Beschreibungen
- **High Contrast**: Unterstützung für hohen Kontrast
- **Reduced Motion**: Respektiert Benutzereinstellungen für reduzierte Bewegung
- **Focus Management**: Sichtbare Fokusindikatoren
- **Error Announcements**: Fehlermeldungen werden von Screen Readern angekündigt

## Browser-Unterstützung

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Performance

- **Tree Shaking**: Nur verwendete Teile werden gebündelt
- **OnPush Change Detection**: Optimierte Change Detection
- **Lazy Loading**: Kann in lazy-loaded Modulen verwendet werden
- **Memory Management**: Automatische Cleanup bei Component Destroy

## Testing

Umfassende Tests sind verfügbar in `datetime-picker.component.spec.ts`:

```bash
# Tests ausführen
ng test --include="**/datetime-picker.component.spec.ts"
```

## Troubleshooting

### Häufige Probleme

1. **Zeit wird nicht angezeigt**: Stelle sicher, dass der Browser HTML5 Time Input unterstützt
2. **Validierung funktioniert nicht**: Prüfe ob `required` korrekt gesetzt ist
3. **Datum wird nicht gesetzt**: Verwende `writeValue()` oder `[(ngModel)]` korrekt
4. **Styling-Probleme**: Importiere Angular Material Theme korrekt

### Debug-Tipps

```typescript
// Debug aktueller Wert
console.log('Current value:', dateTimePickerComponent.value);

// Debug Validierungsstatus
console.log('Validation errors:', dateTimePickerComponent.validate());

// Debug Form Controls
console.log('Date control:', dateTimePickerComponent.dateControl.value);
console.log('Time control:', dateTimePickerComponent.timeControl.value);
```

## Nächste Schritte

Diese Komponente ist bereit für die Verwendung in:
- Task 3: Incident-Formulare
- Task 4: Zeitbasierte Filterung
- Weitere DateTime-bezogene Features

## Changelog

### Version 1.0.0 (Task 2)
- ✅ Initiale Implementierung
- ✅ Angular Material Integration
- ✅ Deutsche Lokalisierung
- ✅ Accessibility Features
- ✅ Comprehensive Testing
- ✅ DateTime-Utilities Integration