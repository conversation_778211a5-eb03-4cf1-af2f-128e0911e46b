<div class="incident-edit-dialog">
  <!-- <PERSON><PERSON> Header -->
  <div class="dialog-header" mat-dialog-title>
    <div class="header-content">
      <mat-icon class="header-icon">edit</mat-icon>
      <h2 class="dialog-title">{{ dialogTitle }}</h2>
    </div>
    <button 
      mat-icon-button 
      class="close-button"
      (click)="onCloseDialog()"
      [disabled]="!canClose"
      aria-label="Dialog schließen"
      matTooltip="Schließen">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <!-- Dialog Content -->
  <div class="dialog-content" mat-dialog-content>
    <!-- Loading Overlay -->
    <div class="loading-overlay" *ngIf="loading">
      <mat-spinner diameter="40" aria-label="Wird gespeichert..."></mat-spinner>
      <p class="loading-text">Änderungen werden gespeichert...</p>
    </div>

    <!-- Error Message -->
    <div class="error-banner" *ngIf="error && !loading">
      <mat-icon class="error-icon">error</mat-icon>
      <div class="error-content">
        <h4>Fehler beim Speichern</h4>
        <p>{{ error }}</p>
      </div>
      <button 
        mat-icon-button 
        (click)="error = null"
        aria-label="Fehlermeldung schließen">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <!-- Edit Form -->
    <div class="form-container" [class.disabled]="loading">
      <app-incident-edit-form
        [incident]="data.incident"
        [loading]="loading"
        [disabled]="loading"
        (formSubmit)="onFormSubmit($event)"
        (formCancel)="onFormCancel()">
      </app-incident-edit-form>
    </div>
  </div>

  <!-- Dialog Footer (Optional - form has its own actions) -->
  <div class="dialog-footer" mat-dialog-actions *ngIf="false">
    <!-- Footer content if needed -->
  </div>
</div>