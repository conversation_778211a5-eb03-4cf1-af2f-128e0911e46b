<div class="incident-list-container">
  <!-- Header mit Titel und Add-Button -->
  <div class="list-header">
    <h2>Störungsverwaltung</h2>
    <button mat-raised-button color="primary" class="modern-button-base" [disabled]="loading" (click)="onCreateIncident()">
      <mat-icon>add</mat-icon>
      Neue Störung
    </button>
  </div>

  <!-- Error Message -->
  <div *ngIf="error && !loading" class="error-container">
    <mat-icon color="warn">error</mat-icon>
    <span>{{ error }}</span>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="loading" class="loading-container">
    <mat-spinner diameter="50"></mat-spinner>
    <p>Lade Störungen...</p>
  </div>

  <!-- Tabelle -->
  <div class="table-container" *ngIf="!loading">
    <table mat-table [dataSource]="dataSource" matSort class="incident-table">
      
      <!-- Title Column -->
      <ng-container matColumnDef="title">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Titel</th>
        <td mat-cell *matCellDef="let incident">
          <a [routerLink]="['/incidents', incident.identifier]"
             class="incident-link"
             matTooltip="Details anzeigen">
            {{ incident.title }}
          </a>
        </td>
      </ng-container>

      <!-- Type Column -->
      <ng-container matColumnDef="type">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Typ</th>
        <td mat-cell *matCellDef="let incident">
          <mat-chip [color]="getIncidentTypeColor(incident.type)" selected>
            {{ getIncidentTypeText(incident.type) }}
          </mat-chip>
        </td>
      </ng-container>

      <!-- Description Column -->
      <ng-container matColumnDef="description">
        <th mat-header-cell *matHeaderCellDef>Beschreibung</th>
        <td mat-cell *matCellDef="let incident">
          <span [matTooltip]="incident.description || 'Keine Beschreibung'" 
                class="description-text">
            {{ incident.description || '-' }}
          </span>
        </td>
      </ng-container>

      <!-- Start Time Column -->
      <ng-container matColumnDef="startTime">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Startzeit</th>
        <td mat-cell *matCellDef="let incident">
          {{ formatDate(incident.startTime) }}
        </td>
      </ng-container>

      <!-- Planned End Time Column -->
      <ng-container matColumnDef="plannedEndTime">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Geplantes Ende</th>
        <td mat-cell *matCellDef="let incident">
          {{ formatDate(incident.plannedEndTime) }}
        </td>
      </ng-container>

      <!-- Resolution Status Column -->
      <ng-container matColumnDef="isResolved">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
        <td mat-cell *matCellDef="let incident">
          <mat-chip [color]="getStatusChipColor(incident.isResolved)" selected>
            {{ getStatusText(incident.isResolved) }}
          </mat-chip>
        </td>
      </ng-container>

      <!-- Applications Column -->
      <ng-container matColumnDef="applications">
        <th mat-header-cell *matHeaderCellDef>Betroffene Applikationen</th>
        <td mat-cell *matCellDef="let incident">
          <div class="applications-container">
            <mat-chip-set>
              <mat-chip *ngFor="let app of incident.applications.slice(0, 3)" 
                        [matTooltip]="app.description || ''" 
                        color="primary">
                {{ app.name }}
              </mat-chip>
              <mat-chip *ngIf="incident.applications.length > 3"
                        [matTooltip]="getAdditionalApplicationNames(incident.applications, 3)">
                +{{ incident.applications.length - 3 }}
              </mat-chip>
            </mat-chip-set>
            <span *ngIf="incident.applications.length === 0">-</span>
          </div>
        </td>
      </ng-container>

      <!-- Actions Column -->
      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef>Aktionen</th>
        <td mat-cell *matCellDef="let incident">
          <div class="action-buttons">
            <button mat-icon-button
                    class="modern-button-base"
                    [matTooltip]="'Details anzeigen'"
                    [routerLink]="['/incidents', incident.identifier]">
              <mat-icon>visibility</mat-icon>
            </button>
            
            <button mat-icon-button
                    class="modern-button-base"
                    [matTooltip]="'Bearbeiten'"
                    (click)="onEditIncident(incident)">
              <mat-icon>edit</mat-icon>
            </button>
            
            <button mat-icon-button
                    class="modern-button-base"
                    [matTooltip]="'Löschen'"
                    [disabled]="isDeleting"
                    color="warn"
                    (click)="onDeleteIncident(incident)">
              <mat-icon *ngIf="!isDeleting">delete</mat-icon>
              <mat-spinner *ngIf="isDeleting" diameter="20"></mat-spinner>
            </button>
          </div>
        </td>
      </ng-container>

      <!-- Table Header and Rows -->
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;" 
          [class.resolved-row]="row.isResolved"></tr>
    </table>

    <!-- No Data Message -->
    <div *ngIf="dataSource.data.length === 0" class="no-data-container">
      <mat-icon>inbox</mat-icon>
      <h3>Keine Störungen gefunden</h3>
      <p>Es wurden keine Störungen gefunden, die Ihren Suchkriterien entsprechen.</p>
      <button mat-raised-button color="primary" class="modern-button-base">
        <mat-icon>add</mat-icon>
        Erste Störung erstellen
      </button>
    </div>

    <!-- Paginator -->
    <mat-paginator 
      *ngIf="dataSource.data.length > 0"
      [pageSizeOptions]="[10, 25, 50, 100]"
      [pageSize]="25"
      showFirstLastButtons>
    </mat-paginator>
  </div>
</div>