import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

// Angular Material Modules
import { MatTableModule } from '@angular/material/table';
import { MatSortModule } from '@angular/material/sort';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCardModule } from '@angular/material/card';
import { MatTabsModule } from '@angular/material/tabs';
import { MatChipsModule } from '@angular/material/chips';
import { MatTooltipModule } from '@angular/material/tooltip';

import { ApplicationManagementRoutingModule } from './application-management-routing.module';

// Import all components
import { ApplicationListComponent } from './components/application-list/application-list.component';
import { ApplicationDetailComponent } from './components/application-detail/application-detail.component';
import { ApplicationFilterComponent } from './components/application-filter/application-filter.component';
import { ApplicationCreateFormComponent } from './components/application-create-form/application-create-form.component';
import { ApplicationEditFormComponent } from './components/application-edit-form/application-edit-form.component';
import { ApplicationCreateDialogComponent } from './dialogs/application-create-dialog/application-create-dialog.component';
import { ApplicationEditDialogComponent } from './dialogs/application-edit-dialog/application-edit-dialog.component';

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ApplicationManagementRoutingModule,
    
    // Angular Material
    MatTableModule,
    MatSortModule,
    MatPaginatorModule,
    MatButtonModule,
    MatIconModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatCardModule,
    MatTabsModule,
    MatChipsModule,
    MatTooltipModule,
    
    // Standalone Components
    ApplicationListComponent,
    ApplicationDetailComponent,
    ApplicationFilterComponent,
    ApplicationCreateFormComponent,
    ApplicationEditFormComponent,
    ApplicationCreateDialogComponent,
    ApplicationEditDialogComponent
  ]
})
export class ApplicationManagementModule { }