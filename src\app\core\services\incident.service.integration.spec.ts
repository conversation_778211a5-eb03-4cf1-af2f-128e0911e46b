import { TestBed } from '@angular/core/testing';
import { IncidentAdapterService } from './incident-adapter.service';
import { IncidentType, CreateIncidentInput, UpdateIncidentInput } from '../models/incident.model';
import { IncidentResponse } from '../graphql/types';

describe('GraphQL DateTime Integration Tests', () => {
  let adapterService: IncidentAdapterService;

  const mockIncidentResponse: IncidentResponse = {
    identifier: 'inc-123',
    title: 'Test Incident',
    type: IncidentType.STOERUNG,
    description: 'Test incident description',
    startTime: '2024-01-15T09:00:00.000Z',
    plannedEndTime: '2024-01-15T17:00:00.000Z',
    actualEndTime: '2024-01-15T16:30:00.000Z',
    alternatives: 'Use backup system',
    isResolved: true,
    createdAt: '2024-01-14T08:00:00.000Z',
    updatedAt: '2024-01-15T16:30:00.000Z',
    applications: []
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [IncidentAdapterService]
    });

    adapterService = TestBed.inject(IncidentAdapterService);
  });

  describe('GraphQL Response to Frontend Model Conversion', () => {
    it('should convert GraphQL response with ISO strings to Incident model with Date objects', () => {
      const incident = adapterService.fromGraphQLResponse(mockIncidentResponse);
      
      // Verify that DateTime fields are converted to Date objects
      expect(incident.startTime).toBeInstanceOf(Date);
      expect(incident.plannedEndTime).toBeInstanceOf(Date);
      expect(incident.actualEndTime).toBeInstanceOf(Date);
      expect(incident.createdAt).toBeInstanceOf(Date);
      expect(incident.updatedAt).toBeInstanceOf(Date);
      
      // Verify that Date values match original ISO strings
      expect((incident.startTime as Date).toISOString()).toBe(mockIncidentResponse.startTime);
      expect((incident.plannedEndTime as Date).toISOString()).toBe(mockIncidentResponse.plannedEndTime!);
      expect((incident.actualEndTime as Date).toISOString()).toBe(mockIncidentResponse.actualEndTime!);
      expect((incident.createdAt as Date).toISOString()).toBe(mockIncidentResponse.createdAt);
      expect((incident.updatedAt as Date).toISOString()).toBe(mockIncidentResponse.updatedAt);
    });

    it('should handle optional DateTime fields correctly', () => {
      const responseWithoutOptionalFields: IncidentResponse = {
        ...mockIncidentResponse,
        plannedEndTime: undefined,
        actualEndTime: undefined,
        description: undefined
      };

      const incident = adapterService.fromGraphQLResponse(responseWithoutOptionalFields);
      
      expect(incident.startTime).toBeInstanceOf(Date);
      expect(incident.createdAt).toBeInstanceOf(Date);
      expect(incident.updatedAt).toBeInstanceOf(Date);
      expect(incident.plannedEndTime).toBeUndefined();
      expect(incident.actualEndTime).toBeUndefined();
      expect(incident.description).toBeUndefined();
    });
  });

  describe('Frontend Model to GraphQL Input Conversion', () => {
    it('should convert CreateIncidentInput with Date objects to GraphQL format', () => {
      const input: CreateIncidentInput = {
        title: 'New Incident',
        type: IncidentType.STOERUNG,
        description: 'New incident description',
        startTime: new Date('2024-01-15T09:00:00.000Z'),
        plannedEndTime: new Date('2024-01-15T17:00:00.000Z'),
        alternatives: 'Use backup',
        applicationIds: ['app-123']
      };

      const graphqlInput = adapterService.toGraphQLCreateInput(input);
      
      expect(graphqlInput.startTime).toBe('2024-01-15T09:00:00.000Z');
      expect(graphqlInput.plannedEndTime).toBe('2024-01-15T17:00:00.000Z');
      expect(graphqlInput.title).toBe('New Incident');
      expect(graphqlInput.applicationIds).toEqual(['app-123']);
    });

    it('should convert UpdateIncidentInput with Date objects to GraphQL format', () => {
      const input: UpdateIncidentInput = {
        identifier: 'inc-123',
        title: 'Updated Incident',
        startTime: new Date('2024-01-15T10:00:00.000Z'),
        actualEndTime: new Date('2024-01-15T16:30:00.000Z')
      };

      const graphqlInput = adapterService.toGraphQLUpdateInput(input);
      
      expect(graphqlInput.identifier).toBe('inc-123');
      expect(graphqlInput.startTime).toBe('2024-01-15T10:00:00.000Z');
      expect(graphqlInput.actualEndTime).toBe('2024-01-15T16:30:00.000Z');
      expect(graphqlInput.title).toBe('Updated Incident');
    });

    it('should handle partial updates correctly', () => {
      const partialUpdate: UpdateIncidentInput = {
        identifier: 'inc-123',
        actualEndTime: new Date('2024-01-15T16:30:00.000Z')
      };

      const graphqlInput = adapterService.toGraphQLUpdateInput(partialUpdate);
      
      expect(graphqlInput.identifier).toBe('inc-123');
      expect(graphqlInput.actualEndTime).toBe('2024-01-15T16:30:00.000Z');
      expect(graphqlInput.title).toBeUndefined();
      expect(graphqlInput.startTime).toBeUndefined();
    });
  });

  describe('DateTime Validation', () => {
    it('should validate valid DateTime fields', () => {
      const validInput: CreateIncidentInput = {
        title: 'Test',
        type: IncidentType.STOERUNG,
        startTime: new Date('2024-01-15T09:00:00.000Z'),
        plannedEndTime: new Date('2024-01-15T17:00:00.000Z'),
        applicationIds: []
      };

      const validation = adapterService.validateDateTimeFields(validInput);
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toEqual([]);
    });

    it('should detect invalid DateTime formats', () => {
      const invalidInput: CreateIncidentInput = {
        title: 'Test',
        type: IncidentType.STOERUNG,
        startTime: 'invalid-date-format',
        applicationIds: []
      };

      const validation = adapterService.validateDateTimeFields(invalidInput);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('Invalid startTime format');
    });

    it('should validate DateTime field relationships', () => {
      const invalidInput: CreateIncidentInput = {
        title: 'Test',
        type: IncidentType.STOERUNG,
        startTime: new Date('2024-01-15T17:00:00.000Z'),
        plannedEndTime: new Date('2024-01-15T09:00:00.000Z'), // Before start time
        applicationIds: []
      };

      const validation = adapterService.validateDateTimeFields(invalidInput);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('plannedEndTime must be after startTime');
    });
  });

  describe('GraphQL Schema Compatibility', () => {
    it('should ensure all DateTime fields are properly typed for GraphQL', () => {
      // Test that our adapter correctly handles the GraphQL DateTime scalar type
      const testDate = new Date('2024-01-15T14:30:45.123Z');
      const testISOString = '2024-01-15T14:30:45.123Z';

      // Verify that Date objects are converted to ISO strings for GraphQL
      const createInput: CreateIncidentInput = {
        title: 'Test',
        type: IncidentType.STOERUNG,
        startTime: testDate,
        plannedEndTime: testDate,
        applicationIds: []
      };

      const graphqlInput = adapterService.toGraphQLCreateInput(createInput);
      expect(graphqlInput.startTime).toBe(testISOString);
      expect(graphqlInput.plannedEndTime).toBe(testISOString);

      // Verify that ISO strings from GraphQL are converted to Date objects
      const graphqlResponse: IncidentResponse = {
        ...mockIncidentResponse,
        startTime: testISOString,
        plannedEndTime: testISOString,
        actualEndTime: testISOString,
        createdAt: testISOString,
        updatedAt: testISOString
      };

      const incident = adapterService.fromGraphQLResponse(graphqlResponse);
      expect(incident.startTime).toBeInstanceOf(Date);
      expect((incident.startTime as Date).getTime()).toBe(testDate.getTime());
    });

    it('should maintain data integrity through round-trip conversion', () => {
      // Test that data remains consistent through GraphQL -> Frontend -> GraphQL conversion
      const originalResponse = mockIncidentResponse;
      
      // Convert GraphQL response to frontend model
      const frontendIncident = adapterService.fromGraphQLResponse(originalResponse);
      
      // Convert frontend model back to GraphQL input
      const updateInput = adapterService.toUpdateInput(frontendIncident);
      const graphqlInput = adapterService.toGraphQLUpdateInput(updateInput);
      
      // Verify that the round-trip maintains data integrity
      expect(graphqlInput.startTime).toBe(originalResponse.startTime);
      expect(graphqlInput.plannedEndTime).toBe(originalResponse.plannedEndTime);
      expect(graphqlInput.actualEndTime).toBe(originalResponse.actualEndTime);
      expect(graphqlInput.title).toBe(originalResponse.title);
      expect(graphqlInput.type).toBe(originalResponse.type);
    });
  });
});