# Implementierungsplan: Authentifizierung und Autorisierung

## Übersicht

Dieser Plan beschreibt die schrittweise Implementierung der Authentifizierung und Autorisierung für die StoerungsBuddy Frontend v2 Anwendung basierend auf Angular 19, Ng<PERSON><PERSON>, Apollo Client und Angular Material.

## Architektur-Übersicht

### Technologie-Stack
- **Frontend**: Angular 19 (Standalone Components)
- **State Management**: NgRx Store + Effects
- **HTTP Client**: Apollo Client für GraphQL + Angular HttpClient für REST
- **UI Framework**: Angular Material (Indigo-Pink Theme)
- **Authentication**: JWT Token basiert
- **Styling**: SCSS + Tailwind CSS

### Authentication Flow
1. **Login**: REST API (`POST /api/auth/login`) → JWT Token
2. **Token Storage**: Secure HTTP-Only Cookies oder localStorage
3. **GraphQL Authentication**: <PERSON><PERSON> in Authorization Header
4. **Authorization**: Role-based (USER/ADMIN) mit Guards
5. **Logout**: Token invalidation + State cleanup

## Phase 1: Grundstruktur und Typen (Priorität: Hoch)

### 1.1 Authentication Models & Interfaces

#### Checkliste:
- [ ] **User Interface** erstellen
  ```typescript
  interface User {
    id: string;
    email: string;
    displayName: string;
    role: 'USER' | 'ADMIN';
  }
  ```

- [ ] **Authentication State Interface** definieren
  ```typescript
  interface AuthState {
    user: User | null;
    token: string | null;
    isAuthenticated: boolean;
    isLoading: boolean;
    error: string | null;
  }
  ```

- [ ] **Login/Logout DTOs** erstellen
  ```typescript
  interface LoginRequest {
    email: string;
    password: string;
  }
  
  interface LoginResponse {
    token: string;
    user: User;
  }
  ```

### 1.2 GraphQL Queries & Mutations

#### Checkliste:
- [ ] **currentUser Query** implementieren (GraphQL)
  ```graphql
  query GetCurrentUser {
    currentUser {
      identifier
      email
      displayName
      role
    }
  }
  ```

- [ ] **GraphQL Service Setup** für Authentication
  - [ ] Apollo Interceptor für Authorization Header
  - [ ] Error Handling für Authentication Errors (401, 403)
  - [ ] Token Refresh Mechanismus (falls verfügbar)

### 1.3 HTTP Services

#### Checkliste:
- [ ] **AuthenticationService** erstellen
  - [ ] `login(credentials)` - REST API Call
  - [ ] `logout()` - Token cleanup
  - [ ] `getCurrentUser()` - GraphQL Query
  - [ ] Token Management (get/set/remove)

- [ ] **HTTP Interceptor** für JWT Token
  - [ ] Automatisches Hinzufügen des Authorization Headers
  - [ ] Error Handling für expired tokens
  - [ ] Redirect zu Login bei 401 Errors

## Phase 2: NgRx State Management (Priorität: Hoch)

### 2.1 Authentication Store

#### Checkliste:
- [ ] **Auth Actions** definieren
  ```typescript
  // Login Actions
  login = createAction('[Auth] Login', props<{ credentials: LoginRequest }>());
  loginSuccess = createAction('[Auth] Login Success', props<{ response: LoginResponse }>());
  loginFailure = createAction('[Auth] Login Failure', props<{ error: string }>());
  
  // Logout Actions
  logout = createAction('[Auth] Logout');
  logoutSuccess = createAction('[Auth] Logout Success');
  
  // Load Current User Actions
  loadCurrentUser = createAction('[Auth] Load Current User');
  loadCurrentUserSuccess = createAction('[Auth] Load Current User Success', props<{ user: User }>());
  loadCurrentUserFailure = createAction('[Auth] Load Current User Failure', props<{ error: string }>());
  ```

- [ ] **Auth Reducer** implementieren
  - [ ] Initial State Definition
  - [ ] Login State Handling
  - [ ] Logout State Handling
  - [ ] Current User State Handling
  - [ ] Loading & Error States

- [ ] **Auth Effects** implementieren
  - [ ] Login Effect (REST API Call)
  - [ ] Load Current User Effect (GraphQL Query)
  - [ ] Logout Effect (Cleanup)
  - [ ] Token Storage Effects
  - [ ] Error Handling Effects

### 2.2 Auth Selectors

#### Checkliste:
- [ ] **Selectors** erstellen
  ```typescript
  selectAuthState = createFeatureSelector<AuthState>('auth');
  selectUser = createSelector(selectAuthState, state => state.user);
  selectIsAuthenticated = createSelector(selectAuthState, state => state.isAuthenticated);
  selectIsLoading = createSelector(selectAuthState, state => state.isLoading);
  selectError = createSelector(selectAuthState, state => state.error);
  selectUserRole = createSelector(selectUser, user => user?.role);
  ```

### 2.3 Auth Facade

#### Checkliste:
- [ ] **AuthFacade Service** erstellen
  - [ ] Observable Streams für Components
  - [ ] Action Dispatching Methods
  - [ ] Convenience Methods für Role Checking
  - [ ] Loading State Management

## Phase 3: Authentication Guards (Priorität: Hoch)

### 3.1 Route Guards

#### Checkliste:
- [ ] **AuthGuard** implementieren
  - [ ] Functional Guard mit `inject()`
  - [ ] Check für Authentication Status
  - [ ] Redirect zu Login wenn nicht authentifiziert
  - [ ] Integration mit NgRx Store

- [ ] **RoleGuard** implementieren
  - [ ] Role-based Access Control
  - [ ] ADMIN/USER Permissions
  - [ ] Fehlerbehandlung für insufficient permissions

- [ ] **LoginGuard** implementieren
  - [ ] Redirect bereits authentifizierte User
  - [ ] Prevent access to Login-Page wenn bereits eingeloggt

### 3.2 Guards Testing

#### Checkliste:
- [ ] Unit Tests für AuthGuard
- [ ] Unit Tests für RoleGuard
- [ ] Unit Tests für LoginGuard
- [ ] Integration Tests für Route Protection

## Phase 4: Login/Logout UI Components (Priorität: Hoch)

### 4.1 Login Component

#### Checkliste:
- [ ] **Login Component** (Standalone)
  - [ ] Angular Material Form Design
  - [ ] Reactive Forms mit Validation
  - [ ] Email/Password Input Fields
  - [ ] Remember Me Checkbox (Optional)
  - [ ] Error Message Display
  - [ ] Loading State Indication

- [ ] **Login Form Validation**
  - [ ] Email Format Validation
  - [ ] Required Field Validation
  - [ ] Custom Validators
  - [ ] Error Message Mapping

- [ ] **Login Component Integration**
  - [ ] NgRx Store Integration
  - [ ] AuthFacade Usage
  - [ ] Navigation nach Login
  - [ ] Error Handling

### 4.2 Navigation Integration

#### Checkliste:
- [ ] **Navigation Component Updates**
  - [ ] User Info Display
  - [ ] Logout Button Integration
  - [ ] Role-based Menu Items
  - [ ] Responsive Design

- [ ] **Header/Toolbar Updates**
  - [ ] Authentication Status Display
  - [ ] User Avatar/Menu
  - [ ] Logout Functionality

## Phase 5: Token Management & Security (Priorität: Mittel)

### 5.1 Token Storage Strategy

#### Checkliste:
- [ ] **Token Storage Service**
  - [ ] Secure Storage Implementation
  - [ ] HttpOnly Cookies vs localStorage Evaluation
  - [ ] Token Expiry Handling
  - [ ] Security Best Practices

- [ ] **Token Refresh Mechanism**
  - [ ] Auto-refresh vor Expiry
  - [ ] Refresh Token Handling (wenn verfügbar)
  - [ ] Fallback zu Re-Authentication

### 5.2 Security Enhancements

#### Checkliste:
- [ ] **XSS Protection**
  - [ ] Token Storage Security
  - [ ] CSP Headers Integration
  - [ ] Input Sanitization

- [ ] **CSRF Protection**
  - [ ] CSRF Token Handling
  - [ ] SameSite Cookie Configuration

## Phase 6: Error Handling & UX (Priorität: Mittel)

### 6.1 Error Handling

#### Checkliste:
- [ ] **Authentication Error Service**
  - [ ] Centralized Error Handling
  - [ ] User-friendly Error Messages
  - [ ] Error Logging
  - [ ] Retry Mechanisms

- [ ] **GraphQL Error Handling**
  - [ ] AUTH_NOT_AUTHORIZED Handling
  - [ ] Network Error Handling
  - [ ] Error Message Translation

### 6.2 User Experience

#### Checkliste:
- [ ] **Loading States**
  - [ ] Login Process Indicators
  - [ ] Skeleton Loaders
  - [ ] Progress Bars

- [ ] **Notifications**
  - [ ] Success Messages
  - [ ] Error Notifications
  - [ ] Snackbar Integration

## Phase 7: Testing (Priorität: Mittel)

### 7.1 Unit Tests

#### Checkliste:
- [ ] **Service Tests**
  - [ ] AuthenticationService Tests
  - [ ] AuthFacade Tests
  - [ ] Token Management Tests

- [ ] **Store Tests**
  - [ ] Actions Tests
  - [ ] Reducers Tests
  - [ ] Effects Tests
  - [ ] Selectors Tests

### 7.2 Component Tests

#### Checkliste:
- [ ] **Login Component Tests**
  - [ ] Form Validation Tests
  - [ ] User Interaction Tests
  - [ ] Error Handling Tests

- [ ] **Guard Tests**
  - [ ] Authorization Tests
  - [ ] Route Protection Tests
  - [ ] Role-based Access Tests

### 7.3 Integration Tests

#### Checkliste:
- [ ] **End-to-End Tests**
  - [ ] Login Flow Tests
  - [ ] Logout Flow Tests
  - [ ] Protected Route Tests
  - [ ] Role-based Feature Tests

## Phase 8: Development Setup & Configuration (Priorität: Niedrig)

### 8.1 Environment Configuration

#### Checkliste:
- [ ] **Development Environment**
  - [ ] Test User Credentials Setup
  - [ ] API Endpoint Configuration
  - [ ] Mock Authentication (Optional)

- [ ] **Production Configuration**
  - [ ] Secure Token Storage
  - [ ] HTTPS Enforcement
  - [ ] Security Headers

### 8.2 Documentation

#### Checkliste:
- [ ] **API Documentation**
  - [ ] Authentication Flow Documentation
  - [ ] Error Codes Documentation
  - [ ] Integration Examples

- [ ] **Developer Guide**
  - [ ] Setup Instructions
  - [ ] Testing Guidelines
  - [ ] Troubleshooting Guide

## Implementierungsreihenfolge

### Sprint 1 (Woche 1-2)
- Phase 1: Grundstruktur und Typen
- Phase 2: NgRx State Management
- Phase 3: Authentication Guards

### Sprint 2 (Woche 3-4)
- Phase 4: Login/Logout UI Components
- Phase 5: Token Management & Security (Basics)

### Sprint 3 (Woche 5-6)
- Phase 6: Error Handling & UX
- Phase 7: Testing (Unit Tests)

### Sprint 4 (Woche 7-8)
- Phase 7: Integration Tests
- Phase 8: Documentation & Finalisierung

## Technische Erkenntnisse aus GraphQL Learnings

### Angewendete Best Practices:
1. **Konsistente GraphQL-Typen**: Verwendung von korrekten Backend-Schema-Typen
2. **Vollständige Response-Objekte**: Immer vollständige User-Objekte zurückgeben
3. **Error Handling**: Spezifische GraphQL Error Codes behandeln
4. **Type Safety**: Strikte TypeScript-Interfaces für alle API-Interaktionen
5. **Cache Management**: Apollo Cache Updates nach Authentication State Changes

### Authentication-spezifische Anpassungen:
- JWT Token in Apollo Client Context
- Authorization Header Management
- GraphQL Error Extension Handling (`AUTH_NOT_AUTHORIZED`)
- Optimistic Updates für bessere UX

## Risiken und Mitigation

### Identifizierte Risiken:
1. **Token Security**: Sichere Speicherung von JWT Tokens
   - **Mitigation**: HttpOnly Cookies + Secure Flags
2. **GraphQL Schema Changes**: Backend API Änderungen
   - **Mitigation**: Schema Validation + Versionierung
3. **Browser Compatibility**: Token Storage across browsers
   - **Mitigation**: Fallback Strategies + Testing

## Erfolgskriterien

### Definition of Done:
- [ ] Alle Akzeptanzkriterien erfüllt
- [ ] Unit Tests > 80% Coverage
- [ ] E2E Tests für kritische Flows
- [ ] Security Review bestanden
- [ ] Performance Benchmarks erfüllt
- [ ] Documentation vollständig

### Performance Ziele:
- Login Response Time < 2s
- Navigation nach Authentication < 500ms
- Bundle Size Impact < 50kb
- First Contentful Paint nicht beeinträchtigt

## Nächste Schritte

1. **Sofort**: Phase 1 starten - Grundstruktur erstellen
2. **Diese Woche**: NgRx Store Setup und Guards implementieren
3. **Nächste Woche**: Login UI und Integration
4. **Testing**: Parallel zu Entwicklung

*Dieser Plan wird iterativ aktualisiert basierend auf Implementierungserfahrungen und Backend-API-Änderungen.*