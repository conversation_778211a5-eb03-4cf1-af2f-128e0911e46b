# EPIC: Störungs- und Wartungsmeldungen Management

## 1. EPIC Header
- **Titel:** Incident Management System
- **Ziel:** Vollständige Verwaltung von Störungsmeldungen und Wartungsfenstern durch Administratoren
- **Referenz:** F-INC-001 bis F-INC-007

## 2. Phasen-Aufbau

### Phase 1: Grundlegende Meldungsübersicht (F-INC-001)
**Kurzbeschreibung:** Implementierung der tabellarischen Übersicht aller Störungs- und Wartungsmeldungen mit Filter- und Sortierfunktionen.

#### Checkliste Phase 1
- [ ] Erstelle `src/app/features/incident-management/` Ordnerstruktur
- [ ] Implementiere `incident-list.component.ts` mit Angular Material Table
- [ ] Erstelle `incident.model.ts` für TypeScript Interfaces
- [ ] Implementiere `incident.service.ts` für API-Kommunikation
- [ ] Erstelle GraphQL Queries für Meldungsabruf
- [ ] Implementiere Filter-Komponenten (Typ, Status, Auflösung)
- [ ] Implementiere Sortierung nach Datum, Priorität, Status
- [ ] E<PERSON><PERSON> `incident-management.module.ts` für Feature-Module

#### Akzeptanzkriterien Phase 1
- [ ] Tabellarische Darstellung aller Meldungen sichtbar
- [ ] Unterscheidung zwischen Störungen und Wartungen durch Typ-Kennzeichnung
- [ ] Anzeige von Titel, betroffenes System, Priorität, Status und Zeitstempel
- [ ] Funktionsfähige Filterung nach Meldungstyp, Priorität und Status
- [ ] Sortierung nach allen relevanten Spalten möglich
- [ ] Responsive Design auf allen Bildschirmgrößen

### Phase 2: Meldungserstellung (F-INC-002, F-INC-003)
**Kurzbeschreibung:** Implementierung der Formulare zur Erstellung neuer Störungsmeldungen und Wartungsfenster.

#### Checkliste Phase 2
- [ ] Erstelle `create-incident-dialog.component.ts` für Störungsmeldungen
- [ ] Erstelle `create-maintenance-dialog.component.ts` für Wartungsfenster
- [ ] Implementiere Reactive Forms mit Validierung
- [ ] Erstelle `system-selector.component.ts` für Systemauswahl
- [ ] Implementiere Priority-Selector mit Material Design
- [ ] Erstelle DateTime-Picker für Wartungsfenster
- [ ] Implementiere GraphQL Mutations für Meldungserstellung
- [ ] Erstelle Bestätigungsdialoge und Success-Messages
- [ ] Implementiere Real-time Updates mit GraphQL Subscriptions

#### Akzeptanzkriterien Phase 2
- [ ] Störungsmeldung-Formular mit allen Pflichtfeldern funktional
- [ ] Wartungsfenster-Formular mit Zeitauswahl funktional
- [ ] Validierung aller Pflichtfelder aktiv
- [ ] Multi-Select-Liste für betroffene Anwendungen verfügbar
- [ ] Automatische Zeitstempel-Erstellung bei Speicherung
- [ ] Bestätigungsmeldungen nach erfolgreicher Erstellung
- [ ] Fehlerbehandlung bei API-Fehlern

### Phase 3: Meldungsbearbeitung und Status-Updates (F-INC-004, F-INC-005, F-INC-006)
**Kurzbeschreibung:** Implementierung der Bearbeitungsfunktionen und Status-Updates für bestehende Meldungen.

#### Checkliste Phase 3
- [ ] Erstelle `edit-incident-dialog.component.ts` für Meldungsbearbeitung
- [ ] Implementiere Status-Update-Buttons (Behoben, Abgeschlossen)
- [ ] Erstelle `resolve-incident-dialog.component.ts` für Lösungsbeschreibung
- [ ] Implementiere automatische Zeitstempel-Updates
- [ ] Erstelle GraphQL Mutations für Updates
- [ ] Implementiere Versionierung der Änderungen (optional)
- [ ] Erstelle Status-Workflow-Logik
- [ ] Implementiere Validierung für Status-Übergänge

#### Akzeptanzkriterien Phase 3
- [ ] Bearbeiten-Dialog mit vorausgefüllten Feldern funktional
- [ ] Status-Änderung von "nicht behoben" zu "behoben" möglich
- [ ] Incidents können über resolveIncident Mutation behoben werden
- [ ] Automatische Zeitstempel-Setzung bei Status-Änderungen
- [ ] Optionale Lösungs-/Abschlussbeschreibung erfassbar
- [ ] Bestätigungsmeldungen nach allen Änderungen

### Phase 4: Meldungslöschung und Archivierung (F-INC-007)
**Kurzbeschreibung:** Implementierung der sicheren Löschfunktion mit Bestätigung und Abhängigkeitsprüfung.

#### Checkliste Phase 4
- [ ] Erstelle `delete-incident-dialog.component.ts` mit Sicherheitsabfrage
- [ ] Implementiere Abhängigkeitsprüfung (Benachrichtigungen)
- [ ] Erstelle GraphQL Mutation für Löschung
- [ ] Implementiere Soft-Delete Option (optional)
- [ ] Erstelle Archivierungsfunktion für behobene Meldungen
- [ ] Implementiere Bulk-Aktionen für mehrere Meldungen
- [ ] Erstelle Wiederherstellungsfunktion (optional)

#### Akzeptanzkriterien Phase 4
- [ ] Löschen-Button mit Sicherheitsabfrage funktional
- [ ] Warnung vor unwiderruflichem Löschen angezeigt
- [ ] Prüfung auf Abhängigkeiten vor Löschung
- [ ] Bestätigungsmeldung nach erfolgreichem Löschen
- [ ] Automatische Aktualisierung der Meldungsliste
- [ ] Archivierung behobener Meldungen möglich

### Phase 5: Real-time Updates und Benachrichtigungen
**Kurzbeschreibung:** Implementierung von Echtzeit-Updates über GraphQL Subscriptions für Live-Benachrichtigungen.

#### Checkliste Phase 5
- [ ] Implementiere GraphQL Subscriptions für Incident-Updates
- [ ] Erstelle Real-time Service für WebSocket-Verbindung
- [ ] Implementiere automatische UI-Updates bei Änderungen
- [ ] Erstelle Toast-Benachrichtigungen für neue Incidents
- [ ] Implementiere Browser-Benachrichtigungen (optional)
- [ ] Erstelle Reconnection-Logic für unterbrochene Verbindungen

#### Akzeptanzkriterien Phase 5
- [ ] Neue Incidents erscheinen automatisch in der Liste
- [ ] Status-Änderungen werden sofort angezeigt
- [ ] Benutzer erhalten Benachrichtigungen für abonnierte Anwendungen
- [ ] Verbindungsabbrüche werden automatisch wiederhergestellt
- [ ] Performance bleibt auch bei vielen gleichzeitigen Updates stabil

## 3. Technische Anforderungen

### Entwicklungsumgebung Setup
- [ ] Angular 19 mit Standalone Components
- [ ] Angular Material für UI-Komponenten
- [ ] NgRx für State Management
- [ ] Apollo Client für GraphQL-Integration
- [ ] TypeScript Strict Mode aktiviert

### Code-Struktur
```
src/app/features/incident-management/
├── components/
│   ├── incident-list/
│   │   ├── incident-list.component.ts
│   │   ├── incident-list.component.html
│   │   └── incident-list.component.scss
│   ├── incident-filters/
│   │   ├── incident-filters.component.ts
│   │   ├── incident-filters.component.html
│   │   └── incident-filters.component.scss
│   └── application-selector/
│       ├── application-selector.component.ts
│       ├── application-selector.component.html
│       └── application-selector.component.scss
├── dialogs/
│   ├── create-incident-dialog/
│   ├── create-maintenance-dialog/
│   ├── edit-incident-dialog/
│   ├── resolve-incident-dialog/
│   └── delete-incident-dialog/
├── services/
│   ├── incident.service.ts
│   ├── incident-graphql.service.ts
│   └── incident-validation.service.ts
├── models/
│   ├── incident.model.ts
│   └── application.model.ts
├── store/
│   ├── incident.actions.ts
│   ├── incident.reducer.ts
│   ├── incident.effects.ts
│   └── incident.selectors.ts
└── incident-management.routes.ts
```

### Performance-Ziele
- [ ] Initiale Ladezeit der Meldungsliste < 2 Sekunden
- [ ] Filter-Response-Zeit < 500ms
- [ ] Formular-Validierung in Echtzeit < 100ms
- [ ] GraphQL Query-Optimierung mit Caching
- [ ] Lazy Loading für große Meldungslisten
- [ ] Virtual Scrolling bei > 100 Meldungen
- [ ] Real-time Updates über GraphQL Subscriptions
- [ ] Optimistic Updates für bessere User Experience

## 4. Abhängigkeiten & Risiken

### Abhängigkeiten
- [ ] GraphQL-Backend-API muss verfügbar sein
- [ ] Anwendungsverwaltung-Feature muss implementiert sein
- [ ] Benutzer-Authentication muss funktional sein
- [ ] Benachrichtigungssystem-Integration erforderlich

### Risiken & Lösungen
| Risiko | Lösung |
|--------|--------|
| GraphQL Schema-Änderungen | Code-First Approach mit TypeScript Generierung |
| Performance bei vielen Meldungen | Pagination und Virtual Scrolling implementieren |
| Komplexe Status-Workflows | State Machine Pattern mit NgRx |
| Zeitzone-Probleme | UTC-Speicherung mit lokaler Anzeige |
| Concurrent Updates | Optimistic Updates mit Rollback-Mechanismus |

## 5. API-Anpassungen und Wichtige Hinweise

### Wichtige Änderungen gegenüber ursprünglicher Planung
- **Feldnamen:** API verwendet `identifier` statt `id`, `applications` statt `system`
- **Incident-Typen:** `STOERUNG`, `WARTUNGSFENSTER`, `KEINE_STOERUNG` statt generischer Typen
- **Status-Handling:** Einfaches `isResolved` Boolean statt komplexer Status-Enums
- **Multi-Application Support:** Incidents können mehrere Anwendungen betreffen
- **Real-time Features:** GraphQL Subscriptions für Live-Updates verfügbar
- **User-Context:** Incidents werden benutzerspezifisch basierend auf Abonnements gefiltert

### Anpassungen in der Implementierung
- Verwende `incidentsForCurrentUser` Query statt allgemeine Incident-Liste
- Implementiere Application-Subscription-Management parallel
- Nutze `identifier` Feld konsistent in allen Komponenten
- Berücksichtige Multi-Application-Auswahl in Formularen

## 6. Nächste Schritte

1. **Sofort starten:** Erstelle Grundstruktur `src/app/features/incident-management/` und Basis-Komponenten
2. **Nach Phase 1:** Implementiere Meldungserstellung-Dialoge mit Multi-Application-Support
3. **Parallel möglich:** Integration mit Application-Management für Subscription-Features
4. **Phase 5:** Real-time Updates mit GraphQL Subscriptions
5. **Abschluss:** End-to-End Tests und Performance-Optimierung

## KI-Entwicklungshinweise

- Verwende Angular 19 Standalone Components für bessere Tree-Shaking
- Implementiere von Anfang an responsive Design mit Angular Flex Layout
- Nutze TypeScript Strict Mode für maximale Type Safety
- Befolge Angular Style Guide und Clean Code Prinzipien
- Erstelle wiederverwendbare Components für Anwendungs-Auswahl und Status-Anzeige
- Implementiere umfassendes Error Handling mit User-freundlichen Meldungen
- Verwende OnPush Change Detection für Performance-Optimierung
- Implementiere Accessibility (WCAG 2.1) von Anfang an
- Nutze Angular Material Theming für konsistentes Design
- Implementiere Unit Tests für alle Services und Components

## Datenmodell-Definitionen

### TypeScript Interfaces
```typescript
export interface Incident {
  identifier: string;
  title: string;
  description?: string;
  type: IncidentType;
  startTime: string;
  plannedEndTime?: string;
  actualEndTime?: string;
  isResolved: boolean;
  applications: Application[];
}

export interface Application {
  identifier: string;
  name: string;
  description?: string;
  isDeleted: boolean;
}

export interface User {
  identifier: string;
  email: string;
  displayName: string;
  role: UserRole;
}

export interface UserSubscription {
  identifier: string;
  userId: string;
  applicationId: string;
  application: Application;
  isActive: boolean;
}

export enum IncidentType {
  STOERUNG = 'STOERUNG',
  WARTUNGSFENSTER = 'WARTUNGSFENSTER',
  KEINE_STOERUNG = 'KEINE_STOERUNG'
}

export enum UserRole {
  USER = 'USER',
  ADMIN = 'ADMIN'
}

export interface CreateIncidentInput {
  title: string;
  type: IncidentType;
  description?: string;
  startTime: string;
  plannedEndTime?: string;
  alternatives?: string;
  applicationIds: string[];
}

export interface UpdateIncidentInput {
  identifier: string;
  title?: string;
  type?: IncidentType;
  description?: string;
  startTime?: string;
  plannedEndTime?: string;
  actualEndTime?: string;
  alternatives?: string;
  applicationIds?: string[];
}
```

### GraphQL Schema (Aktuell)
```graphql
enum IncidentType {
  STOERUNG
  WARTUNGSFENSTER
  KEINE_STOERUNG
}

enum UserRole {
  USER
  ADMIN
}

type Application {
  identifier: ID!
  name: String!
  description: String
  isDeleted: Boolean!
}

type Incident {
  identifier: ID!
  title: String!
  type: IncidentType!
  description: String
  startTime: String!
  plannedEndTime: String
  actualEndTime: String
  isResolved: Boolean!
  applications: [Application!]
}

type User {
  identifier: ID!
  email: String!
  displayName: String!
  role: UserRole!
}

type UserSubscription {
  identifier: ID!
  userId: ID!
  applicationId: ID!
  application: Application!
  isActive: Boolean!
}

type Query {
  # Applications
  allApplications: [Application!]
  
  # Incidents
  incidentsForCurrentUser(isResolved: Boolean): [Incident!]
  
  # User & Subscriptions
  currentUser: User
  currentUserSubscriptions: [UserSubscription!]
  currentUserSubscribedApplications: [Application!]
}

type Mutation {
  # Subscription Management
  subscribeToApplication(applicationId: ID!): UserSubscription
  unsubscribeFromApplication(applicationId: ID!): Boolean
  
  # Incident Management
  createIncident(input: CreateIncidentInput!): Incident!
  updateIncident(input: UpdateIncidentInput!): Incident!
  resolveIncident(identifier: ID!): Incident!
  deleteIncident(identifier: ID!): Boolean!
  
  # Application Management
  createApplication(input: CreateApplicationInput!): Application!
  updateApplication(identifier: ID!, name: String, description: String, isDeleted: Boolean): Application!
  deleteApplication(identifier: ID!): Application
}

# Input Types
input CreateIncidentInput {
  title: String!
  type: IncidentType!
  description: String
  startTime: String!
  plannedEndTime: String
  alternatives: String
  applicationIds: [ID!]!
}

input UpdateIncidentInput {
  identifier: ID!
  title: String
  type: IncidentType
  description: String
  startTime: String
  plannedEndTime: String
  actualEndTime: String
  alternatives: String
  applicationIds: [ID!]
}

input CreateApplicationInput {
  name: String!
  description: String
}

type Subscription {
  # Real-time Updates
  currentUserIncidentCreated: Incident
  currentUserIncidentUpdated: Incident
  currentUserIncidentResolved: Incident
}
```

### GraphQL Query Beispiele
```graphql
# Alle Incidents für den aktuellen Benutzer abrufen
query GetUserIncidents {
  incidentsForCurrentUser(isResolved: false) {
    identifier
    title
    type
    description
    startTime
    plannedEndTime
    isResolved
    applications {
      identifier
      name
    }
  }
}

# Neuen Incident erstellen
mutation CreateIncident {
  createIncident(input: {
    title: "Datenbankverbindungsprobleme"
    type: STOERUNG
    description: "Benutzer erleben Verbindungstimeouts"
    startTime: "2025-05-27T10:30:00Z"
    plannedEndTime: "2025-05-27T12:00:00Z"
    applicationIds: ["app-123", "app-456"]
  }) {
    identifier
    title
    type
    startTime
    applications {
      name
    }
  }
}

# Incident aktualisieren
mutation UpdateIncident {
  updateIncident(input: {
    identifier: "incident-123"
    description: "Problem behoben - Datenbankverbindung wiederhergestellt"
    actualEndTime: "2025-05-27T11:45:00Z"
  }) {
    identifier
    title
    description
    actualEndTime
    isResolved
  }
}

# Incident als behoben markieren
mutation ResolveIncident {
  resolveIncident(identifier: "incident-123") {
    identifier
    title
    isResolved
    actualEndTime
  }
}
```