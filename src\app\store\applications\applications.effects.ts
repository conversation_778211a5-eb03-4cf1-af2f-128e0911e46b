import { Injectable, inject } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { of } from 'rxjs';
import { catchError, map, mergeMap, switchMap } from 'rxjs/operators';
import { ApplicationService } from '../../core/services/application.service';
import * as ApplicationActions from './applications.actions';

@Injectable()
export class ApplicationsEffects {
  private actions$ = inject(Actions);
  private applicationService = inject(ApplicationService);

  loadApplications$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ApplicationActions.loadApplications),
      switchMap(({ filter }) =>
        this.applicationService.getAllApplications(filter).pipe(
          map((applications) =>
            ApplicationActions.loadApplicationsSuccess({ applications })
          ),
          catchError((error) =>
            of(ApplicationActions.loadApplicationsFailure({ error }))
          )
        )
      )
    )
  );

  createApplication$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ApplicationActions.createApplication),
      mergeMap(({ application }) =>
        this.applicationService.createApplication(application).pipe(
          map((newApplication) =>
            ApplicationActions.createApplicationSuccess({ application: newApplication })
          ),
          catchError((error) =>
            of(ApplicationActions.createApplicationFailure({ error }))
          )
        )
      )
    )
  );

  updateApplication$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ApplicationActions.updateApplication),
      mergeMap(({ application }) =>
        this.applicationService.updateApplication(application).pipe(
          map((updatedApplication) =>
            ApplicationActions.updateApplicationSuccess({ application: updatedApplication })
          ),
          catchError((error) =>
            of(ApplicationActions.updateApplicationFailure({ error }))
          )
        )
      )
    )
  );

  deleteApplication$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ApplicationActions.deleteApplication),
      mergeMap(({ identifier }) =>
        this.applicationService.deleteApplication(identifier).pipe(
          map(() =>
            ApplicationActions.deleteApplicationSuccess({ identifier })
          ),
          catchError((error) =>
            of(ApplicationActions.deleteApplicationFailure({ error }))
          )
        )
      )
    )
  );
}