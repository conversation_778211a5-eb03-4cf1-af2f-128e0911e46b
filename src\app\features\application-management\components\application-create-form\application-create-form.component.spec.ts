import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { ApplicationCreateFormComponent } from './application-create-form.component';
import { ApplicationValidators } from '../../shared/validators/application-validators';
import { of } from 'rxjs';

describe('ApplicationCreateFormComponent', () => {
  let component: ApplicationCreateFormComponent;
  let fixture: ComponentFixture<ApplicationCreateFormComponent>;
  let mockApplicationValidators: Partial<ApplicationValidators>;

  beforeEach(async () => {
    mockApplicationValidators = {
      nameUniquenessValidator: () => () => of(null),
    };

    await TestBed.configureTestingModule({
      imports: [
        ReactiveFormsModule,
        NoopAnimationsModule,
        ApplicationCreateFormComponent,
      ],
      providers: [
        { provide: ApplicationValidators, useValue: mockApplicationValidators },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ApplicationCreateFormComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize the form with empty values', () => {
    expect(component.applicationForm.value).toEqual({
      name: '',
      description: '',
    });
  });

  it('should emit formSubmit event on valid submission', () => {
    spyOn(component.formSubmit, 'emit');
    component.applicationForm.setValue({
      name: 'Test App',
      description: 'Test Description',
    });
    component.onSubmit();
    expect(component.formSubmit.emit).toHaveBeenCalledWith({
      name: 'Test App',
      description: 'Test Description',
    });
  });

  it('should not emit formSubmit event on invalid submission', () => {
    spyOn(component.formSubmit, 'emit');
    component.applicationForm.setValue({ name: '', description: '' });
    component.onSubmit();
    expect(component.formSubmit.emit).not.toHaveBeenCalled();
  });

  it('should emit formCancel event on cancel', () => {
    spyOn(component.formCancel, 'emit');
    component.onCancel();
    expect(component.formCancel.emit).toHaveBeenCalled();
  });
});