# Edit Dialog

Mode: Orchestrator

```
<PERSON><PERSON><PERSON> einen Edit-Dialog zum Editieren der Incidents auf Basis von @/src/app/features/incident-management !
```

# Create Dialog

Mode: Orchestrator
```
<PERSON><PERSON><PERSON> nun den entsprechenden Create-Dialog zur Anlage eines Incidents inkl. zugehöriger GraphQL-Anbindung.

Beachte die @/docs/feature-03-incident-management-crud/implementation-step-edit.md 

Ignoriere das Schreiben der Tests!
```

# Delete Dialog

Mode: Orchestrator
```
<PERSON><PERSON>elle nun die entsprechende Funktion zur Löschung eines Incidents inkl. zugehöriger GraphQL-Anbindung. Nutze einen einfachen Bestätigungsdialog zur Löschung eines Incidents!

Beachte die @/docs/feature-03-incident-management-crud/implementation-step-edit.md 

Ignoriere das Schreiben der Tests!
```

# Detail-Dialog

Mode: Orchestrator
```
Erstelle nun die Detail-Ansicht eines Incidents inkl. zugehöriger GraphQL-Anbindung für die Ansicht. Es geht nur um die "Details-Ansicht", die in genutzt wird: @/src/app/features/incident-management/components/incident-list/incident-list.component.html 

Ignoriere das Schreiben der Tests!
```