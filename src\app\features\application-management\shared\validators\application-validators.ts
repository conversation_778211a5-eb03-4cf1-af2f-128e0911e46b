import { Injectable } from '@angular/core';
import {
  AbstractControl,
  AsyncValidatorFn,
  ValidationErrors,
} from '@angular/forms';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

@Injectable({ providedIn: 'root' })
export class ApplicationValidators {
  // This would typically involve a service call to the backend
  // to check for name uniqueness. For now, we'll simulate it.
  nameUniquenessValidator(): AsyncValidatorFn {
    return (control: AbstractControl): Observable<ValidationErrors | null> => {
      // Simulate an API call
      return of(null).pipe(
        map(() => {
          // In a real app, you would check if the name exists
          // and return { uniqueName: true } if it does.
          return null;
        }),
        catchError(() => of(null))
      );
    };
  }
}