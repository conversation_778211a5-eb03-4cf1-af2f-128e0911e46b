import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { Subject, takeUntil, switchMap, Observable } from 'rxjs';

// Angular Material
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTabsModule } from '@angular/material/tabs';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialog } from '@angular/material/dialog';

// Models and Services
import { Application } from '../../../../core/models/application.model';
import { ApplicationsFacade } from '../../../../store/applications/applications.facade';
import { EntityType } from '../../../../core/enums/entity-type.enum';

// Components
import { ChangeHistoryComponent } from '../change-history/change-history.component';
import { ApplicationEditDialogComponent } from '../../dialogs/application-edit-dialog/application-edit-dialog.component';
import { DeleteConfirmationDialogComponent } from '../../../../shared/components/delete-confirmation-dialog/delete-confirmation-dialog.component';

@Component({
  selector: 'app-application-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatTabsModule,
    MatChipsModule,
    ChangeHistoryComponent
  ],
  templateUrl: './application-detail.component.html',
  styleUrls: ['./application-detail.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ApplicationDetailComponent implements OnInit, OnDestroy {
  application$: Observable<Application | null>;
  loading$: Observable<boolean>;
  error$: Observable<any>;
  entityType = EntityType.APPLICATION;

  private destroy$ = new Subject<void>();

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private applicationsFacade: ApplicationsFacade,
    private dialog: MatDialog
  ) {
    this.application$ = this.applicationsFacade.selectedApplication$;
    this.loading$ = this.applicationsFacade.loading$;
    this.error$ = this.applicationsFacade.error$;
  }

  ngOnInit(): void {
    this.route.paramMap.pipe(takeUntil(this.destroy$)).subscribe(params => {
      const id = params.get('id');
      if (id) {
        this.applicationsFacade.selectApplication(id);
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  goBack(): void {
    this.router.navigate(['/applications']);
  }

  getStatusChipColor(isDeleted: boolean): string {
    return isDeleted ? 'warn' : 'primary';
  }

  getStatusText(isDeleted: boolean): string {
    return isDeleted ? 'Gelöscht' : 'Aktiv';
  }

  formatDate(dateString: string): string {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('de-DE', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  openEditDialog(application: Application): void {
    const dialogRef = this.dialog.open(ApplicationEditDialogComponent, {
      width: '600px',
      data: application
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Application was updated, reload the data
        this.applicationsFacade.selectApplication(application.identifier);
      }
    });
  }

  openDeleteDialog(application: Application): void {
    const dialogRef = this.dialog.open(DeleteConfirmationDialogComponent, {
      width: '500px',
      data: {
        title: 'Applikation löschen',
        message: `Möchten Sie die Applikation "${application.name}" wirklich löschen?`,
        entityType: 'application',
        entityId: application.identifier,
        entityName: application.name,
        warningMessage: 'Diese Aktion kann nicht rückgängig gemacht werden.',
        allowForceDelete: true
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result?.confirmed) {
        this.applicationsFacade.deleteApplication(application.identifier);
        this.goBack();
      }
    });
  }
}