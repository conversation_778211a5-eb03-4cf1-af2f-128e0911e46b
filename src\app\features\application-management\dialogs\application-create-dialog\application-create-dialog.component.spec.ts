import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatDialogRef } from '@angular/material/dialog';
import { provideMockStore } from '@ngrx/store/testing';
import { ApplicationCreateDialogComponent } from './application-create-dialog.component';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { ApplicationsFacade } from '../../../../store/applications/applications.facade';
import { Actions } from '@ngrx/effects';
import { MatSnackBar } from '@angular/material/snack-bar';
import { of, Subject } from 'rxjs';
import * as ApplicationActions from '../../../../store/applications/applications.actions';
import { CreateApplicationInput } from '../../../../core/models/application.model';

describe('ApplicationCreateDialogComponent', () => {
  let component: ApplicationCreateDialogComponent;
  let fixture: ComponentFixture<ApplicationCreateDialogComponent>;
  let mockDialogRef: MatDialogRef<ApplicationCreateDialogComponent>;
  let mockApplicationsFacade: Partial<ApplicationsFacade>;
  let mockActions: Actions;
  let mockSnackBar: Partial<MatSnackBar>;
  let actions$: Subject<any>;

  beforeEach(async () => {
    mockDialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);
    mockApplicationsFacade = {
      loading$: of(false),
      error$: of(null),
      createApplication: jasmine.createSpy('createApplication'),
    };
    actions$ = new Subject();
    mockActions = new Actions(actions$);
    mockSnackBar = jasmine.createSpyObj('MatSnackBar', ['open']);

    await TestBed.configureTestingModule({
      imports: [ApplicationCreateDialogComponent, NoopAnimationsModule],
      providers: [
        { provide: MatDialogRef, useValue: mockDialogRef },
        { provide: ApplicationsFacade, useValue: mockApplicationsFacade },
        { provide: Actions, useValue: mockActions },
        { provide: MatSnackBar, useValue: mockSnackBar },
        provideMockStore({}),
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ApplicationCreateDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should close the dialog on cancel', () => {
    component.onCancel();
    expect(mockDialogRef.close).toHaveBeenCalledWith(false);
  });

  it('should dispatch createApplication action on submit', () => {
    const app: CreateApplicationInput = { name: 'Test', description: 'Test' };
    component.onSubmit(app);
    expect(mockApplicationsFacade.createApplication).toHaveBeenCalledWith(app);
  });

  it('should show snackbar and close dialog on createApplicationSuccess', (done) => {
    // Trigger the action after component initialization
    setTimeout(() => {
      actions$.next(ApplicationActions.createApplicationSuccess({ application: {} as any }));
      
      // Allow time for subscription to process
      setTimeout(() => {
        expect(mockSnackBar.open).toHaveBeenCalledWith('Application created successfully.', 'OK', { duration: 3000 });
        expect(mockDialogRef.close).toHaveBeenCalledWith(true);
        done();
      }, 10);
    }, 10);
  });

  it('should show snackbar on createApplicationFailure', (done) => {
    const error = { message: 'Error' };
    
    // Trigger the action after component initialization
    setTimeout(() => {
      actions$.next(ApplicationActions.createApplicationFailure({ error }));
      
      // Allow time for subscription to process
      setTimeout(() => {
        expect(mockSnackBar.open).toHaveBeenCalledWith(`Error creating application: ${error.message}`, 'OK', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        done();
      }, 10);
    }, 10);
  });
});