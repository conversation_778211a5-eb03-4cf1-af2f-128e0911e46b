import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup, NonNullableFormBuilder, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { Application } from '../../../../core/models/application.model';
import { ApplicationForm } from '../../shared/interfaces/application-form.interfaces';
import { ApplicationValidators } from '../../shared/validators/application-validators';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-application-edit-form',
  templateUrl: './application-edit-form.component.html',
  styleUrls: ['./application-edit-form.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
  ],
})
export class ApplicationEditFormComponent implements OnInit {
  @Input({ required: true }) application!: Application;
  @Input() loading = false;
  @Input() disabled = false;
  @Output() formSubmit = new EventEmitter<any>();
  @Output() formCancel = new EventEmitter<void>();

  applicationForm: FormGroup<ApplicationForm>;
  private initialFormValue: any;

  constructor(
    private fb: NonNullableFormBuilder,
    private applicationValidators: ApplicationValidators
  ) {
    this.applicationForm = this.fb.group({
      name: [
        '',
        [Validators.required],
        [this.applicationValidators.nameUniquenessValidator()],
      ],
      description: ['', [Validators.required]],
    });
  }

  ngOnInit(): void {
    this.applicationForm.patchValue(this.application);
    this.initialFormValue = this.applicationForm.getRawValue();
  }

  hasChanges(): boolean {
    return (
      JSON.stringify(this.initialFormValue) !==
      JSON.stringify(this.applicationForm.getRawValue())
    );
  }

  onSubmit(): void {
    if (this.applicationForm.valid) {
      this.formSubmit.emit(this.applicationForm.getRawValue());
    }
  }

  onCancel(): void {
    this.formCancel.emit();
  }
}