import { createAction, props } from '@ngrx/store';
import { Incident, IncidentFilter, UpdateIncidentInput, CreateIncidentInput } from '../../core/models/incident.model';

// Load All Incidents Actions
export const loadIncidents = createAction(
  '[Incidents] Load Incidents'
);

export const loadIncidentsSuccess = createAction(
  '[Incidents] Load Incidents Success',
  props<{ incidents: Incident[] }>()
);

export const loadIncidentsFailure = createAction(
  '[Incidents] Load Incidents Failure',
  props<{ error: string }>()
);

// Load My Incidents Actions
export const loadMyIncidents = createAction(
  '[Incidents] Load My Incidents',
  props<{ isResolved?: boolean }>()
);

export const loadMyIncidentsSuccess = createAction(
  '[Incidents] Load My Incidents Success',
  props<{ incidents: Incident[] }>()
);

export const loadMyIncidentsFailure = createAction(
  '[Incidents] Load My Incidents Failure',
  props<{ error: string }>()
);

// Update Incident Actions
export const updateIncident = createAction(
  '[Incidents] Update Incident',
  props<{ updateInput: UpdateIncidentInput }>()
);

export const updateIncidentSuccess = createAction(
  '[Incidents] Update Incident Success',
  props<{ incident: Incident }>()
);

export const updateIncidentFailure = createAction(
  '[Incidents] Update Incident Failure',
  props<{ error: string }>()
);

// Create Incident Actions
export const createIncident = createAction(
  '[Incidents] Create Incident',
  props<{ createInput: CreateIncidentInput }>()
);

export const createIncidentSuccess = createAction(
  '[Incidents] Create Incident Success',
  props<{ incident: Incident }>()
);

export const createIncidentFailure = createAction(
  '[Incidents] Create Incident Failure',
  props<{ error: string }>()
);

// Delete Incident Actions
export const deleteIncident = createAction(
  '[Incidents] Delete Incident',
  props<{ identifier: string }>()
);

export const deleteIncidentSuccess = createAction(
  '[Incidents] Delete Incident Success',
  props<{ identifier: string }>()
);

export const deleteIncidentFailure = createAction(
  '[Incidents] Delete Incident Failure',
  props<{ error: string }>()
);

// Load Single Incident Actions
export const loadIncident = createAction(
  '[Incidents] Load Incident',
  props<{ id: string }>()
);

export const loadIncidentSuccess = createAction(
  '[Incidents] Load Incident Success',
  props<{ incident: Incident }>()
);

export const loadIncidentFailure = createAction(
  '[Incidents] Load Incident Failure',
  props<{ error: string }>()
);

// Clear Incidents Error
export const clearIncidentsError = createAction(
  '[Incidents] Clear Error'
);