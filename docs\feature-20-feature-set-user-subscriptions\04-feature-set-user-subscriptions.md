# Feature-Set: <PERSON><PERSON>er-Subscriptions

## Übersicht

Dieses Feature-Set umfasst die Verwaltung von Benutzer-Abonnements für Systeme. Ben<PERSON><PERSON>, die sich über das Frontend angemeldet haben, werden automatisch allen Systemen zugeordnet und können ihre Subscriptions später anpassen.

## Fachliche Anforderungen

### F-SUB-001: Benutzerübersicht
**Als** Administrator  
**möchte ich** eine Übersicht aller registrierten Benutzer sehen  
**damit** ich weiß, wer das System nutzt

#### Akzeptanzkriterien
- [ ] Tabellarische Darstellung aller Benutzer
- [ ] Anzeige von Name, E-Mail, Registrierungsdatum und letzter Aktivität
- [ ] Anzeige der Anzahl abonnierter Systeme pro Benutzer
- [ ] Suchfunktion nach Benutzername oder E-Mail
- [ ] Sortierung nach verschiedenen Spalten
- [ ] Filterung nach Aktivitätsstatus (aktiv/inaktiv)

### F-SUB-002: Benutzer-System-Zuordnungen anzeigen
**Als** Administrator  
**möchte ich** sehen, welche Systeme ein Benutzer abonniert hat  
**damit** ich die Subscription-Verteilung verstehe

#### Akzeptanzkriterien
- [ ] Detail-Ansicht für jeden Benutzer
- [ ] Liste aller abonnierten Systeme
- [ ] Anzeige des Subscription-Datums
- [ ] Unterscheidung zwischen automatischen und manuellen Subscriptions
- [ ] Schnellübersicht über nicht-abonnierte Systeme
- [ ] Export-Funktion für Subscription-Listen (optional)

### F-SUB-003: System-Benutzer-Zuordnungen anzeigen
**Als** Administrator  
**möchte ich** sehen, welche Benutzer ein System abonniert haben  
**damit** ich die Reichweite von Meldungen einschätzen kann

#### Akzeptanzkriterien
- [ ] Detail-Ansicht für jedes System
- [ ] Liste aller Abonnenten
- [ ] Anzeige der Anzahl Abonnenten
- [ ] Filterung nach Subscription-Typ (automatisch/manuell)
- [ ] Suchfunktion nach Benutzername
- [ ] Export-Funktion für Abonnenten-Listen (optional)

### F-SUB-004: Subscription hinzufügen
**Als** Administrator  
**möchte ich** Benutzer zu Systemen hinzufügen können  
**damit** sie entsprechende Benachrichtigungen erhalten

#### Akzeptanzkriterien
- [ ] Auswahl-Dialog für Benutzer und System
- [ ] Dropdown-Listen für verfügbare Benutzer und Systeme
- [ ] Prüfung auf bereits bestehende Subscription
- [ ] Automatische Zeitstempel-Erstellung
- [ ] Markierung als "manuell hinzugefügt"
- [ ] Bestätigungsmeldung nach erfolgreichem Hinzufügen

### F-SUB-005: Subscription entfernen
**Als** Administrator  
**möchte ich** Benutzer-System-Zuordnungen entfernen können  
**damit** Benutzer keine ungewünschten Benachrichtigungen erhalten

#### Akzeptanzkriterien
- [ ] Entfernen-Button bei bestehenden Subscriptions
- [ ] Sicherheitsabfrage vor dem Entfernen
- [ ] Unterscheidung zwischen automatischen und manuellen Subscriptions
- [ ] Warnung bei Entfernung automatischer Subscriptions
- [ ] Bestätigungsmeldung nach erfolgreichem Entfernen
- [ ] Protokollierung der Änderung

### F-SUB-006: Bulk-Operationen
**Als** Administrator  
**möchte ich** mehrere Subscriptions gleichzeitig verwalten können  
**damit** ich effizient arbeiten kann

#### Akzeptanzkriterien
- [ ] Mehrfachauswahl von Benutzern oder Systemen
- [ ] Bulk-Hinzufügung von Subscriptions
- [ ] Bulk-Entfernung von Subscriptions
- [ ] Fortschrittsanzeige bei größeren Operationen
- [ ] Fehlerbehandlung bei teilweise fehlgeschlagenen Operationen
- [ ] Zusammenfassung der durchgeführten Änderungen

### F-SUB-007: Automatische Subscription bei Neuregistrierung
**Als** Administrator  
**möchte ich** dass neue Benutzer automatisch allen Systemen zugeordnet werden  
**damit** sie sofort alle relevanten Informationen erhalten

#### Akzeptanzkriterien
- [ ] Automatische Subscription bei Benutzerregistrierung
- [ ] Zuordnung zu allen aktiven Systemen
- [ ] Markierung als "automatisch zugeordnet"
- [ ] Protokollierung der automatischen Zuordnung
- [ ] Konfigurierbare Ausnahmen (optional)
- [ ] Benachrichtigung des Administrators (optional)

### F-SUB-008: Subscription-Statistiken
**Als** Administrator  
**möchte ich** Statistiken über Subscriptions sehen  
**damit** ich die Nutzung des Systems verstehe

#### Akzeptanzkriterien
- [ ] Dashboard mit Subscription-Übersicht
- [ ] Anzahl Subscriptions pro System
- [ ] Anzahl Subscriptions pro Benutzer
- [ ] Verhältnis automatische/manuelle Subscriptions
- [ ] Entwicklung der Subscription-Zahlen über Zeit
- [ ] Export-Funktion für Statistiken

## Subscription-Typen

| Typ | Beschreibung | Eigenschaften |
|-----|--------------|---------------|
| Automatisch | Bei Registrierung zugeordnet | Standard für alle neuen Benutzer |
| Manuell | Vom Administrator hinzugefügt | Gezielte Zuordnung |
| Benutzer-gewählt | Vom Benutzer selbst gewählt | Über Frontend-Interface |

## Benutzer-Status

| Status | Beschreibung | Auswirkung |
|--------|--------------|------------|
| Aktiv | Regelmäßige Nutzung | Erhält alle Benachrichtigungen |
| Inaktiv | Längere Zeit nicht angemeldet | Benachrichtigungen optional reduziert |
| Gesperrt | Administrativ deaktiviert | Keine Benachrichtigungen |

## Technische Hinweise

- Subscription-Änderungen in Echtzeit an Frontend übertragen
- Effiziente Datenbankabfragen für große Benutzerzahlen
- Caching für häufig abgerufene Subscription-Listen
- Audit-Log für alle Subscription-Änderungen

## Abhängigkeiten

- Benutzerverwaltung/Authentifizierung
- Systemverwaltung
- Benachrichtigungssystem
- Frontend-Benutzerinterface
- GraphQL-Backend-API

## Datenschutz-Hinweise

- Benutzer müssen über automatische Subscriptions informiert werden
- Opt-out-Möglichkeit für Benutzer bereitstellen
- DSGVO-konforme Datenverarbeitung
- Löschung von Subscription-Daten bei Benutzer-Löschung