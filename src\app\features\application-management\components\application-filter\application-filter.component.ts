import { Component, Output, EventEmitter, OnInit, OnDestroy, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';

// Angular Material
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';

// Models
import { ApplicationFilter } from '../../../../core/models/application.model';

@Component({
  selector: 'app-application-filter',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule
  ],
  templateUrl: './application-filter.component.html',
  styleUrls: ['./application-filter.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ApplicationFilterComponent implements OnInit, OnDestroy {
  @Output() filterChange = new EventEmitter<ApplicationFilter>();
  @Output() searchChange = new EventEmitter<string>();

  filterForm: FormGroup;
  private destroy$ = new Subject<void>();

  constructor(private fb: FormBuilder) {
    this.filterForm = this.fb.group({
      searchTerm: [''],
      statusFilter: ['active'] // 'active', 'deleted', 'all'
    });
    
    console.log('🏗️ Constructor - Initial form value:', this.filterForm.value);
  }

  ngOnInit(): void {
    console.log('🎛️ ApplicationFilter.ngOnInit - Initial form value:', this.filterForm.value);
    
    // Ensure form has correct initial values
    this.filterForm.patchValue({
      searchTerm: '',
      statusFilter: 'active'
    });
    
    console.log('🎛️ ApplicationFilter.ngOnInit - After patch form value:', this.filterForm.value);
    
    this.setupFormSubscriptions();
    
    // Use setTimeout to ensure patch is applied before emitting
    setTimeout(() => {
      console.log('🎛️ ApplicationFilter.ngOnInit - Delayed emit, form value:', this.filterForm.value);
      this.emitFilter();
    }, 0);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setupFormSubscriptions(): void {
    // Search term with debounce
    this.filterForm.get('searchTerm')?.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      takeUntil(this.destroy$)
    ).subscribe(searchTerm => {
      this.searchChange.emit(searchTerm || '');
    });

    // Status filter changes (immediate)
    this.filterForm.get('statusFilter')?.valueChanges.pipe(
      takeUntil(this.destroy$)
    ).subscribe(() => {
      this.emitFilter();
    });
  }

  private emitFilter(): void {
    const formValue = this.filterForm.value;
    const filter: ApplicationFilter = {};
    
    console.log('🎛️ ApplicationFilter.emitFilter called with form value:', formValue);
    console.log('🎛️ Raw statusFilter value:', formValue.statusFilter);
    console.log('🎛️ Type of statusFilter:', typeof formValue.statusFilter);
    
    // Handle status filter with explicit logging
    const statusValue = formValue.statusFilter;
    if (statusValue === 'active') {
      filter.isDeleted = false;
      console.log('🔍 MATCH: statusFilter === "active" -> Setting isDeleted = false');
    } else if (statusValue === 'deleted') {
      filter.isDeleted = true;
      console.log('🔍 MATCH: statusFilter === "deleted" -> Setting isDeleted = true');
    } else if (statusValue === 'all') {
      console.log('🔍 MATCH: statusFilter === "all" -> No isDeleted filter');
    } else {
      console.log('🚨 NO MATCH: statusFilter value not recognized:', statusValue);
    }
    
    console.log('📤 Emitting filter:', filter);
    this.filterChange.emit(filter);
  }

  clearFilters(): void {
    console.log('🧹 ApplicationFilter.clearFilters called');
    console.log('🧹 Form value before clear:', this.filterForm.value);
    
    this.filterForm.patchValue({
      searchTerm: '',
      statusFilter: 'active'
    });
    
    console.log('🧹 Form value after clear:', this.filterForm.value);
    
    // Manually emit events since patchValue might not trigger valueChanges
    this.searchChange.emit('');
    this.emitFilter();
  }

  hasActiveFilters(): boolean {
    const formValue = this.filterForm.value;
    return !!(formValue.searchTerm || formValue.statusFilter !== 'active');
  }

  clearSearch(): void {
    this.filterForm.patchValue({ searchTerm: '' });
    this.searchChange.emit('');
  }

  onStatusFilterChange(event: any): void {
    console.log('🎛️ onStatusFilterChange called with event:', event);
    console.log('🎛️ Selected value:', event.value);
    console.log('🎛️ Current form value after selection:', this.filterForm.value);
    
    // Manually emit filter after selection change
    this.emitFilter();
  }
}