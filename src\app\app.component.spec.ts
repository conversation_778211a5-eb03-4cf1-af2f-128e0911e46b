import { TestBed } from '@angular/core/testing';
import { AppComponent } from './app.component';
import { provideMockStore } from '@ngrx/store/testing';
import { AuthFacade } from './store/auth/auth.facade';
import { of } from 'rxjs';

describe('AppComponent', () => {
  let mockAuthFacade: Partial<AuthFacade>;

  beforeEach(async () => {
    mockAuthFacade = {
      isAuthenticated$: of(false),
      currentUser$: of(null),
    };

    await TestBed.configureTestingModule({
      imports: [AppComponent],
      providers: [
        provideMockStore({}),
        { provide: AuthFacade, useValue: mockAuthFacade },
      ],
    }).compileComponents();
  });

  it('should create the app', () => {
    const fixture = TestBed.createComponent(AppComponent);
    const app = fixture.componentInstance;
    expect(app).toBeTruthy();
  });

  it(`should have the 'stoerungsbuddy-frontend' title`, () => {
    const fixture = TestBed.createComponent(AppComponent);
    const app = fixture.componentInstance;
    expect(app.title).toEqual('stoerungsbuddy-frontend');
  });

  it('should not render title when not logged in', () => {
    const fixture = TestBed.createComponent(AppComponent);
    fixture.detectChanges();
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.querySelector('h1')).toBeNull();
  });
});
