import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet, RouterModule } from '@angular/router';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { Store } from '@ngrx/store';
import { Observable, map, shareReplay } from 'rxjs';

import { HeaderComponent } from '../header/header.component';
import { selectIsAuthenticated } from '../../../../store/auth/auth.selectors';
import * as AuthActions from '../../../../store/auth/auth.actions';

@Component({
  selector: 'app-main-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    RouterModule,
    MatSidenavModule,
    MatToolbarModule,
    MatIconModule,
    HeaderComponent
  ],
  template: `
    <!-- Temporarily bypass authentication for development -->
    <div class="layout-container">
      <mat-sidenav-container class="sidenav-container">
        <!-- Sidebar Navigation -->
        <mat-sidenav
          #drawer
          [class]="'sidenav' + (isSidebarExpanded ? ' expanded' : '')"
          fixedInViewport
          [attr.role]="(isHandset$ | async) ? 'dialog' : 'navigation'"
          [mode]="(isHandset$ | async) ? 'over' : 'side'"
          [opened]="(isHandset$ | async) === false"
        >
          <div class="sidenav-content">
            <!-- Navigation Menu -->
            <nav class="navigation-menu">
              <div class="nav-header">
                <h3>{{ isSidebarExpanded ? 'StörungsBuddy' : 'SB' }}</h3>
              </div>
              
              <div class="nav-items">
                <a routerLink="/dashboard"
                   routerLinkActive="active"
                   class="nav-item"
                   title="Dashboard"
                   (click)="onNavItemClick()">
                  <mat-icon>dashboard</mat-icon>
                  <span>Dashboard</span>
                </a>
                
                <a routerLink="/applications"
                   routerLinkActive="active"
                   class="nav-item"
                   title="Applikationen"
                   (click)="onNavItemClick()">
                  <mat-icon>apps</mat-icon>
                  <span>Applikationen</span>
                </a>
                
                <a routerLink="/incidents"
                   routerLinkActive="active"
                   class="nav-item"
                   title="Störungen"
                   (click)="onNavItemClick()">
                  <mat-icon>warning</mat-icon>
                  <span>Störungen</span>
                </a>
                
                <a routerLink="/reports"
                   routerLinkActive="active"
                   class="nav-item"
                   title="Berichte"
                   (click)="onNavItemClick()">
                  <mat-icon>assessment</mat-icon>
                  <span>Berichte</span>
                </a>
              </div>
            </nav>
          </div>
        </mat-sidenav>

        <!-- Main Content Area -->
        <mat-sidenav-content class="main-content">
          <!-- Header -->
          <app-header></app-header>
          
          <!-- Page Content -->
          <main class="page-content">
            <router-outlet></router-outlet>
          </main>
        </mat-sidenav-content>
      </mat-sidenav-container>
    </div>
  `,
  styles: [`
    .layout-container {
      height: 100vh;
      display: flex;
      flex-direction: column;
    }

    .sidenav-container {
      flex: 1;
      display: flex;
    }

    .sidenav {
      width: 64px;
      background: var(--funk-light-gray);
      border-right: 1px solid var(--border-color);
      box-shadow: none;
      z-index: 100;
      transition: width 0.3s ease;
    }

    .sidenav.expanded {
      width: 280px;
    }

    .sidenav-content {
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    .navigation-menu {
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    .nav-header {
      padding: 0;
      background: var(--funk-blue);
      color: var(--funk-white);
      position: relative;
      overflow: hidden;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 64px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .nav-header h3 {
      margin: 0;
      padding: 0;
      font-size: 1.2rem;
      font-weight: 700;
      position: relative;
      z-index: 1;
      text-shadow: none;
      color: var(--funk-white);
      line-height: 1;
    }

    .nav-items {
      flex: 1;
      padding: 1rem 0;
      display: flex;
      flex-direction: column;
      gap: 8px;
      overflow: hidden;
      align-items: center;
    }

    .nav-item {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 12px;
      margin: 0;
      text-decoration: none;
      color: var(--funk-gray);
      transition: all 0.2s ease;
      border: none;
      background: none;
      width: 48px;
      height: 48px;
      cursor: pointer;
      border-radius: var(--border-radius-sm);
      position: relative;
      font-weight: 500;
      flex-shrink: 0;
    }

    .nav-item:hover {
      background: var(--funk-light-blue);
      color: var(--funk-white);
      transform: none;
      box-shadow: none;
    }

    .nav-item.active {
      background: var(--funk-blue);
      color: var(--funk-white);
      transform: none;
      box-shadow: none;
    }

    .nav-item mat-icon {
      margin: 0;
      font-size: 1.5rem;
      width: 1.5rem;
      height: 1.5rem;
      transition: none;
    }

    .nav-item:hover mat-icon,
    .nav-item.active mat-icon {
      transform: none;
    }

    .nav-item span {
      display: none;
    }

    .sidenav.expanded .nav-items {
      align-items: stretch;
    }

    .sidenav.expanded .nav-item {
      justify-content: flex-start;
      padding: 1rem 1.5rem;
      width: calc(100% - 16px);
      height: auto;
      margin: 0 8px;
    }

    .sidenav.expanded .nav-item mat-icon {
      margin-right: 1rem;
    }

    .sidenav.expanded .nav-item span {
      display: inline;
      font-size: 0.95rem;
      font-weight: 600;
      letter-spacing: 0;
    }

    .main-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      background-color: var(--funk-white);
    }

    .page-content {
      flex: 1;
      padding: 2rem;
      overflow-y: auto;
      background-color: var(--funk-white);
      position: relative;
    }

    .auth-layout {
      height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--background-color);
    }

    /* Enhanced responsive adjustments */
    @media (max-width: 768px) {
      .page-content {
        padding: 1.5rem;
      }

      .nav-header {
        padding: 1.5rem 1.25rem;
      }

      .nav-header h3 {
        font-size: 1.2rem;
      }

      .nav-items {
        padding: 1rem 0;
      }

      .nav-item {
        padding: 0.875rem 1.25rem;
        margin: 0 8px;
        width: calc(100% - 16px);
      }
    }

    @media (max-width: 480px) {
      .page-content {
        padding: 1rem;
      }

      .nav-header {
        padding: 1.25rem 1rem;
      }

      .nav-header h3 {
        font-size: 1.1rem;
      }

      .nav-item {
        padding: 0.75rem 1rem;
        margin: 0 6px;
        width: calc(100% - 12px);
      }

      .nav-item mat-icon {
        margin-right: 0.75rem;
        font-size: 1.125rem;
        width: 1.125rem;
        height: 1.125rem;
      }

      .nav-item span {
        font-size: 0.875rem;
      }
    }

    /* Enhanced sidenav animations */
    .sidenav {
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Enhanced scrollbar for main content */
    .page-content::-webkit-scrollbar {
      width: 8px;
    }

    .page-content::-webkit-scrollbar-track {
      background: transparent;
      border-radius: 4px;
    }

    .page-content::-webkit-scrollbar-thumb {
      background: linear-gradient(180deg, var(--text-muted) 0%, var(--text-secondary) 100%);
      border-radius: 4px;
      transition: background 0.3s ease;
    }

    .page-content::-webkit-scrollbar-thumb:hover {
      background: linear-gradient(180deg, var(--text-secondary) 0%, var(--text-primary) 100%);
    }

    /* Backdrop blur effect for mobile overlay */
    @media (max-width: 768px) {
      .sidenav-container .mat-drawer-backdrop {
        backdrop-filter: blur(8px);
        background-color: rgba(0, 0, 0, 0.4);
      }
    }
  `]
})
export class MainLayoutComponent implements OnInit {
  private store = inject(Store);
  private breakpointObserver = inject(BreakpointObserver);

  isAuthenticated$ = this.store.select(selectIsAuthenticated);
  isSidebarExpanded = false;

  isHandset$: Observable<boolean> = this.breakpointObserver.observe(Breakpoints.Handset)
    .pipe(
      map(result => result.matches),
      shareReplay()
    );

  ngOnInit() {
    // Initialize auth state on app startup
    this.store.dispatch(AuthActions.initializeAuth());
    
    // Listen for sidebar toggle events
    window.addEventListener('toggleSidebar', () => {
      this.toggleSidebar();
    });
  }

  toggleSidebar() {
    this.isSidebarExpanded = !this.isSidebarExpanded;
  }

  onNavItemClick() {
    // Auto-collapse sidebar after navigation item selection
    if (this.isSidebarExpanded) {
      this.isSidebarExpanded = false;
    }
  }
}