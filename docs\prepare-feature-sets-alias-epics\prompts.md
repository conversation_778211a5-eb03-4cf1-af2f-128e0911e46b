# Init-Punkt definieren

```
Initialisiere die technologischen Frontend-Komponenten im Rootverzeichnis. Nutze die spezifischen Framework-Initialisierungs-Routinen für den Start und generiere nicht selbst Code.

Schreibe zusätzlich unter \.roo\rules\technical-constaints.md eine Basis-Beschreibung des Technologie-Stacks als allgemeingültige Orientierung für alle nachfolgenden Prompts und Befehle.

Weitere Technologie-Vorgaben:

- **Framework**: Angular 19 
- **State Management**: NgRx
- **UI-Framework**: Angular Material
- **API-Integration**: Apollo Client für GraphQL

```


# EPICs definieren


## EPIC 01

```
Du bist ein erfahrener Software-Architekt und schreibst ein detailliertes EPIC für die Softwareentwicklung. Verwende die folgende Struktur und Checkliste:

@/docs/prepare-feature-sets-alias-epics/template-epics.md 


Schreibe das EPIC neu unter Verwendung dieser optimierten Struktur für maximale KI-Verständlichkeit und Umsetzbarkeit.

@/docs/01-feature-set-admin-ui-foundation.md 
```

## EPIC 02

```
Du bist ein erfahrener Software-Architekt und schreibst ein detailliertes EPIC für die Softwareentwicklung. Verwende die folgende Struktur und Checkliste:

@/docs/prepare-feature-sets-alias-epics/template-epics.md 


Schreibe das EPIC neu unter Verwendung dieser optimierten Struktur für maximale KI-Verständlichkeit und Umsetzbarkeit.

@/docs/02-feature-set-system-management.md 
```

## EPIC 03

```
Du bist ein erfahrener Software-Architekt und schreibst ein detailliertes EPIC für die Softwareentwicklung. Verwende die folgende Struktur und Checkliste:

@/docs/prepare-feature-sets-alias-epics/template-epics.md 


Schreibe das EPIC neu unter Verwendung dieser optimierten Struktur für maximale KI-Verständlichkeit und Umsetzbarkeit.

@/docs/03-feature-set-incident-management.md
```