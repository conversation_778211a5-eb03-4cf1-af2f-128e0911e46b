import { Component, EventEmitter, OnInit, OnD<PERSON>roy, Output, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, FormControl, Validators, ReactiveFormsModule, AbstractControl, ValidationErrors } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';

// Angular Material
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

// Models and Components
import { CreateIncidentInput, IncidentType } from '../../../../core/models/incident.model';
import { ApplicationSelectorComponent } from '../application-selector/application-selector.component';
import { DateTimePickerComponent } from '../../../../shared/components/datetime-picker/datetime-picker.component';

// Utils
import { toISOString } from '../../../../core/utils/datetime.utils';

interface IncidentCreateForm {
  title: FormControl<string>;
  type: FormControl<IncidentType>;
  description: FormControl<string>;
  startTime: FormControl<Date | null>;
  plannedEndTime: FormControl<Date | null>;
  alternatives: FormControl<string>;
  applicationIds: FormControl<string[]>;
}

@Component({
  selector: 'app-incident-create-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    MatButtonModule,
    MatIconModule,
    ApplicationSelectorComponent,
    DateTimePickerComponent
  ],
  templateUrl: './incident-create-form.component.html',
  styleUrls: ['./incident-create-form.component.scss']
})
export class IncidentCreateFormComponent implements OnInit, OnDestroy {
  @Output() formSubmit = new EventEmitter<CreateIncidentInput>();
  @Output() formCancel = new EventEmitter<void>();

  form!: FormGroup<IncidentCreateForm>;
  incidentTypes = Object.values(IncidentType);
  
  private destroy$ = new Subject<void>();
  private fb = inject(FormBuilder);

  ngOnInit(): void {
    this.initializeForm();
    this.setupFormValidation();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForm(): void {
    const now = new Date();
    
    this.form = this.fb.group<IncidentCreateForm>({
      title: this.fb.control('', {
        nonNullable: true,
        validators: [
          Validators.required,
          Validators.minLength(3),
          Validators.maxLength(200)
        ]
      }),
      type: this.fb.control(IncidentType.STOERUNG, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      description: this.fb.control('', {
        nonNullable: true,
        validators: [Validators.maxLength(1000)]
      }),
      startTime: this.fb.control<Date | null>(now, {
        validators: [Validators.required]
      }),
      plannedEndTime: this.fb.control<Date | null>(null, {
        validators: []
      }),
      alternatives: this.fb.control('', {
        nonNullable: true,
        validators: [Validators.maxLength(500)]
      }),
      applicationIds: this.fb.control<string[]>([], {
        nonNullable: true,
        validators: [this.applicationSelectionValidator.bind(this)]
      })
    });
  }

  private setupFormValidation(): void {
    // Watch for changes in start time to validate planned end time
    this.form.get('startTime')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.form.get('plannedEndTime')?.updateValueAndValidity();
      });

    // Custom validation for planned end time
    this.form.get('plannedEndTime')?.addValidators(this.plannedEndTimeValidator.bind(this));
  }

  private applicationSelectionValidator(control: AbstractControl): ValidationErrors | null {
    if (!control.value || control.value.length === 0) {
      return { required: true };
    }
    return null;
  }

  private plannedEndTimeValidator(control: AbstractControl): ValidationErrors | null {
    if (!control.value) {
      return null; // Optional field
    }

    const startTime = this.form?.get('startTime')?.value;
    if (startTime && control.value <= startTime) {
      return { plannedEndTimeBeforeStart: true };
    }

    return null;
  }

  onSubmit(): void {
    if (this.form.valid) {
      const formValue = this.form.value;
      
      const createInput: CreateIncidentInput = {
        title: formValue.title!,
        type: formValue.type!,
        description: formValue.description || undefined,
        startTime: formValue.startTime ? toISOString(formValue.startTime) : new Date().toISOString(),
        plannedEndTime: formValue.plannedEndTime ? toISOString(formValue.plannedEndTime) : undefined,
        alternatives: formValue.alternatives || undefined,
        applicationIds: formValue.applicationIds!
      };

      this.formSubmit.emit(createInput);
    } else {
      this.markFormGroupTouched();
    }
  }

  onCancel(): void {
    this.formCancel.emit();
  }

  private markFormGroupTouched(): void {
    Object.keys(this.form.controls).forEach(key => {
      const control = this.form.get(key);
      control?.markAsTouched();
    });
  }

  // Getter methods for template
  get titleControl() { return this.form.get('title'); }
  get typeControl() { return this.form.get('type'); }
  get descriptionControl() { return this.form.get('description'); }
  get startTimeControl() { return this.form.get('startTime'); }
  get plannedEndTimeControl() { return this.form.get('plannedEndTime'); }
  get alternativesControl() { return this.form.get('alternatives'); }
  get applicationIdsControl() { return this.form.get('applicationIds'); }

  // Error message methods
  getTitleErrorMessage(): string {
    const control = this.titleControl;
    if (control?.hasError('required')) {
      return 'Titel ist erforderlich';
    }
    if (control?.hasError('minlength')) {
      return 'Titel muss mindestens 3 Zeichen lang sein';
    }
    if (control?.hasError('maxlength')) {
      return 'Titel darf maximal 200 Zeichen lang sein';
    }
    return '';
  }

  getTypeErrorMessage(): string {
    const control = this.typeControl;
    if (control?.hasError('required')) {
      return 'Typ ist erforderlich';
    }
    return '';
  }

  getDescriptionErrorMessage(): string {
    const control = this.descriptionControl;
    if (control?.hasError('maxlength')) {
      return 'Beschreibung darf maximal 1000 Zeichen lang sein';
    }
    return '';
  }

  getStartTimeErrorMessage(): string {
    const control = this.startTimeControl;
    if (control?.hasError('required')) {
      return 'Startzeit ist erforderlich';
    }
    return '';
  }

  getPlannedEndTimeErrorMessage(): string {
    const control = this.plannedEndTimeControl;
    if (control?.hasError('plannedEndTimeBeforeStart')) {
      return 'Geplante Endzeit muss nach der Startzeit liegen';
    }
    return '';
  }

  getAlternativesErrorMessage(): string {
    const control = this.alternativesControl;
    if (control?.hasError('maxlength')) {
      return 'Alternativen dürfen maximal 500 Zeichen lang sein';
    }
    return '';
  }

  getApplicationsErrorMessage(): string {
    const control = this.applicationIdsControl;
    if (control?.hasError('required')) {
      return 'Mindestens eine Anwendung muss ausgewählt werden';
    }
    return '';
  }


  getIncidentTypeDisplayName(type: IncidentType): string {
    switch (type) {
      case IncidentType.STOERUNG:
        return 'Störung';
      case IncidentType.WARTUNGSFENSTER:
        return 'Wartungsfenster';
      case IncidentType.KEINE_STOERUNG:
        return 'Keine Störung';
      default:
        return type;
    }
  }
}