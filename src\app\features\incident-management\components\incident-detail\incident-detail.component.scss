.incident-detail-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  .detail-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    gap: 16px;

    h2 {
      margin: 0;
      color: #1976d2;
      font-weight: 500;
      flex: 1;
    }

    .header-actions {
      display: flex;
      gap: 12px;

      button {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  }

  .error-container {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px;
    background-color: #ffebee;
    border: 1px solid #f44336;
    border-radius: 8px;
    margin-bottom: 24px;
    color: #c62828;

    mat-icon {
      font-size: 20px;
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px;
    gap: 16px;

    p {
      margin: 0;
      color: #666;
    }
  }

  .incident-content {
    display: flex;
    flex-direction: column;
    gap: 24px;

    .info-card {
      .title-section {
        display: flex;
        align-items: center;
        gap: 16px;
        flex-wrap: wrap;

        .incident-title {
          font-size: 1.5rem;
          font-weight: 500;
          color: #1976d2;
        }
      }

      .status-section {
        display: flex;
        align-items: center;
        gap: 16px;
        margin-top: 8px;

        .incident-id {
          color: #666;
          font-size: 0.9rem;
          font-family: 'Courier New', monospace;
        }

        mat-chip {
          mat-icon {
            font-size: 16px;
            width: 16px;
            height: 16px;
          }
        }
      }

      .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 24px;
        margin-top: 16px;

        .info-item {
          display: flex;
          flex-direction: column;
          gap: 8px;

          &.full-width {
            grid-column: 1 / -1;
          }

          .info-label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            color: #1976d2;
            font-size: 0.9rem;

            mat-icon {
              font-size: 18px;
              width: 18px;
              height: 18px;
            }
          }

          .info-value {
            padding-left: 26px;
            color: #333;
            line-height: 1.5;

            &.description-text,
            &.alternatives-text {
              background-color: #f8f9fa;
              padding: 12px 16px;
              border-radius: 4px;
              border-left: 4px solid #1976d2;
              margin-left: 10px;
              white-space: pre-wrap;
            }
          }
        }
      }
    }

    .applications-card {
      mat-card-title {
        display: flex;
        align-items: center;
        gap: 8px;

        .app-count {
          color: #666;
          font-weight: normal;
          font-size: 0.9rem;
        }
      }

      .applications-grid {
        margin-top: 16px;

        mat-chip-set {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;

          mat-chip {
            mat-icon[matChipAvatar] {
              background-color: #1976d2;
              color: white;
              font-weight: bold;
            }
          }
        }
      }

      .no-applications {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #666;
        font-style: italic;
        padding: 16px;
        text-align: center;
        justify-content: center;

        mat-icon {
          color: #ccc;
        }
      }
    }

    .timestamps-card {
      mat-card-title {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .timestamps-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-top: 16px;

        .timestamp-item {
          display: flex;
          flex-direction: column;
          gap: 8px;

          .timestamp-label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            color: #1976d2;
            font-size: 0.9rem;

            mat-icon {
              font-size: 16px;
              width: 16px;
              height: 16px;
            }
          }

          .timestamp-value {
            padding-left: 24px;
            color: #333;
            font-size: 0.95rem;
          }
        }
      }
    }

    .action-section {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
      justify-content: center;
      padding: 24px 0;
      border-top: 1px solid #e0e0e0;

      button {
        display: flex;
        align-items: center;
        gap: 8px;
        min-width: 140px;
      }
    }
  }

  .no-incident-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px 24px;
    text-align: center;
    color: #666;

    mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: 16px;
      color: #ccc;
    }

    h3 {
      margin: 0 0 8px 0;
      font-weight: 500;
      color: #333;
    }

    p {
      margin: 0 0 24px 0;
      max-width: 400px;
    }

    button {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .incident-detail-container {
    padding: 16px;

    .detail-header {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;

      h2 {
        text-align: center;
      }

      .header-actions {
        justify-content: center;
      }
    }

    .incident-content {
      .info-card {
        .title-section {
          flex-direction: column;
          align-items: flex-start;
          gap: 12px;

          .incident-title {
            font-size: 1.3rem;
          }
        }

        .status-section {
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;
        }

        .info-grid {
          grid-template-columns: 1fr;
          gap: 16px;

          .info-item {
            .info-value {
              padding-left: 0;

              &.description-text,
              &.alternatives-text {
                margin-left: 0;
              }
            }
          }
        }
      }

      .timestamps-card {
        .timestamps-grid {
          grid-template-columns: 1fr;
          gap: 16px;

          .timestamp-item {
            .timestamp-value {
              padding-left: 0;
            }
          }
        }
      }

      .action-section {
        flex-direction: column;
        align-items: stretch;

        button {
          justify-content: center;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .incident-detail-container {
    padding: 12px;

    .incident-content {
      gap: 16px;

      .applications-card {
        .applications-grid {
          mat-chip-set {
            flex-direction: column;
            align-items: stretch;

            mat-chip {
              justify-content: flex-start;
            }
          }
        }
      }
    }
  }
}

// Global Snackbar Styles
::ng-deep {
  .success-snackbar {
    background-color: #4caf50 !important;
    color: white !important;
  }

  .error-snackbar {
    background-color: #f44336 !important;
    color: white !important;
  }

  .info-snackbar {
    background-color: #2196f3 !important;
    color: white !important;
  }
}