.incident-create-dialog {
  display: flex;
  flex-direction: column;
  min-height: 0;
  max-height: 90vh;

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 1.5rem 1rem 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    margin: 0;

    .title-content {
      display: flex;
      align-items: center;
      gap: 0.75rem;

      .title-icon {
        color: #1976d2;
        font-size: 1.5rem;
        width: 1.5rem;
        height: 1.5rem;
      }

      h2 {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.87);
      }
    }

    .close-button {
      color: rgba(0, 0, 0, 0.54);
      
      &:hover:not(:disabled) {
        background-color: rgba(0, 0, 0, 0.04);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }

  .dialog-content {
    flex: 1;
    overflow-y: auto;
    padding: 0;
    position: relative;
    min-height: 0;

    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.8);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 1rem;
      z-index: 10;

      p {
        margin: 0;
        color: rgba(0, 0, 0, 0.6);
        font-size: 0.875rem;
      }
    }

    .error-banner {
      display: flex;
      align-items: flex-start;
      gap: 0.75rem;
      padding: 1rem 1.5rem;
      background-color: #ffebee;
      border-left: 4px solid #f44336;
      margin: 1rem 1.5rem;
      border-radius: 4px;

      .error-icon {
        color: #f44336;
        font-size: 1.25rem;
        width: 1.25rem;
        height: 1.25rem;
        margin-top: 0.125rem;
      }

      .error-content {
        flex: 1;

        h4 {
          margin: 0 0 0.25rem 0;
          font-size: 0.875rem;
          font-weight: 500;
          color: #d32f2f;
        }

        p {
          margin: 0;
          font-size: 0.8125rem;
          color: #c62828;
          line-height: 1.4;
        }
      }
    }

    .form-disabled {
      pointer-events: none;
      opacity: 0.6;
    }
  }
}

// Dialog container customizations
::ng-deep {
  .incident-create-dialog-container {
    .mat-mdc-dialog-container {
      padding: 0;
      overflow: hidden;
    }

    .mat-mdc-dialog-surface {
      border-radius: 8px;
      box-shadow: 0 11px 15px -7px rgba(0, 0, 0, 0.2),
                  0 24px 38px 3px rgba(0, 0, 0, 0.14),
                  0 9px 46px 8px rgba(0, 0, 0, 0.12);
    }
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .incident-create-dialog {
    .dialog-header {
      border-bottom-color: rgba(255, 255, 255, 0.12);

      .title-content {
        .title-icon {
          color: #90caf9;
        }

        h2 {
          color: rgba(255, 255, 255, 0.87);
        }
      }

      .close-button {
        color: rgba(255, 255, 255, 0.54);
        
        &:hover:not(:disabled) {
          background-color: rgba(255, 255, 255, 0.04);
        }
      }
    }

    .dialog-content {
      .loading-overlay {
        background-color: rgba(0, 0, 0, 0.8);

        p {
          color: rgba(255, 255, 255, 0.6);
        }
      }

      .error-banner {
        background-color: #3e2723;
        border-left-color: #f44336;

        .error-content {
          h4 {
            color: #ef5350;
          }

          p {
            color: #ffcdd2;
          }
        }
      }
    }
  }
}

// Mobile responsive design
@media (max-width: 768px) {
  .incident-create-dialog {
    .dialog-header {
      padding: 1rem;

      .title-content {
        h2 {
          font-size: 1.125rem;
        }
      }
    }

    .dialog-content {
      .error-banner {
        margin: 0.75rem 1rem;
        padding: 0.75rem;
      }
    }
  }

  ::ng-deep {
    .incident-create-dialog-container {
      .mat-mdc-dialog-container {
        margin: 1rem;
        max-height: calc(100vh - 2rem);
        max-width: calc(100vw - 2rem);
      }
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .incident-create-dialog {
    .dialog-header {
      border-bottom: 2px solid;
    }

    .error-banner {
      border-left-width: 6px;
      border: 2px solid #f44336;
    }
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .incident-create-dialog {
    * {
      transition: none !important;
      animation: none !important;
    }
  }

  ::ng-deep {
    .incident-create-dialog-container {
      .mat-mdc-dialog-surface {
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }
    }
  }
}

// Focus management
.incident-create-dialog {
  .dialog-header {
    .close-button:focus {
      outline: 2px solid #1976d2;
      outline-offset: 2px;
    }
  }
}

// Loading state animations
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.incident-create-dialog {
  .dialog-content {
    .loading-overlay {
      p {
        animation: pulse 2s ease-in-out infinite;
      }
    }
  }
}

@media (prefers-reduced-motion: reduce) {
  .incident-create-dialog {
    .dialog-content {
      .loading-overlay {
        p {
          animation: none;
        }
      }
    }
  }
}