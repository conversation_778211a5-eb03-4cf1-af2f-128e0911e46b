# Update EPIC

```
Aktualisiere die @/docs/03-EPIC-incident-management.md mit den letzten Informationen aus der GraphQL-API @/docs/api/graphql-api.md 
```

# Init-Prompt

```
<PERSON>rstelle unter docs\03-EPIC-incident-management-plan.md einen Implementierungsplan mit Checklisten zur Umsetzung von @/docs/03-EPIC-incident-management.md  . Stoppe dann zunächst!

Beachte die Erkenntnisse aus @/docs/learnings/graphql-learnings.md 
```

# Phase I

```
Setze den @/docs/03-EPIC-incident-management-plan.md schrittweise um! Gehe Phasenweise um und stoppe nach jeder Phase!
```

# Update

```
Aktualisiere die Checkliste gegenüber dem tatsächliche Implementierungsstand in @/docs/03-EPIC-incident-management-plan.md 
```

# Phase II

```
Setze den Plan schrittweise weiter um für die noch offenen Punkte  
@/docs/03-EPIC-incident-management-plan.md

Aktualisiere anschließend die Checkliste!
```