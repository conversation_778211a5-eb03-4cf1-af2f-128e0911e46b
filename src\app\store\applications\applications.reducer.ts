import { createReducer, on } from '@ngrx/store';
import { initialApplicationsState } from './applications.state';
import * as ApplicationActions from './applications.actions';

export const applicationsReducer = createReducer(
  initialApplicationsState,
  on(ApplicationActions.loadApplications, (state) => ({
    ...state,
    loading: true,
    error: null,
  })),
  on(ApplicationActions.loadApplicationsSuccess, (state, { applications }) => ({
    ...state,
    applications,
    loading: false,
  })),
  on(ApplicationActions.loadApplicationsFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error,
  })),
  on(ApplicationActions.selectApplication, (state, { identifier }) => ({
    ...state,
    selectedApplication: state.applications.find(app => app.identifier === identifier) || null,
  })),
  on(ApplicationActions.createApplication, (state) => ({
    ...state,
    loading: true,
  })),
  on(ApplicationActions.createApplicationSuccess, (state, { application }) => ({
    ...state,
    applications: [...state.applications, application],
    loading: false,
  })),
  on(ApplicationActions.createApplicationFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error,
  })),
  on(ApplicationActions.updateApplication, (state) => ({
    ...state,
    loading: true,
  })),
  on(ApplicationActions.updateApplicationSuccess, (state, { application }) => ({
    ...state,
    applications: state.applications.map((app) =>
      app.identifier === application.identifier ? application : app
    ),
    loading: false,
  })),
  on(ApplicationActions.updateApplicationFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error,
  })),
  on(ApplicationActions.deleteApplication, (state) => ({
    ...state,
    loading: true,
  })),
  on(ApplicationActions.deleteApplicationSuccess, (state, { identifier }) => ({
    ...state,
    applications: state.applications.filter(app => app.identifier !== identifier),
    loading: false,
  })),
  on(ApplicationActions.deleteApplicationFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error,
  }))
);