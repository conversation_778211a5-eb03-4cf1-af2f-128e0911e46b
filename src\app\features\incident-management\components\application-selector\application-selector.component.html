<div class="application-selector">
  <!-- Selected Applications Chips -->
  <div class="selected-applications" *ngIf="selectedApplications.length > 0">
    <mat-chip-set>
      <mat-chip 
        *ngFor="let application of selectedApplications" 
        [removable]="!disabled"
        (removed)="onApplicationRemoved(application)"
        [attr.aria-label]="'Ausgewählte Anwendung: ' + application.name">
        {{ application.name }}
        <mat-icon matChipRemove *ngIf="!disabled">cancel</mat-icon>
      </mat-chip>
    </mat-chip-set>
  </div>

  <!-- Search Input with Autocomplete -->
  <mat-form-field appearance="outline" class="search-field">
    <mat-label>{{ placeholder }}</mat-label>
    <input
      matInput
      [formControl]="searchControl"
      [matAutocomplete]="auto"
      [placeholder]="placeholder"
      [attr.aria-label]="placeholder"
      [attr.aria-required]="required"
      [attr.aria-invalid]="hasError">
    
    <!-- Loading Spinner -->
    <mat-spinner 
      *ngIf="loading" 
      matSuffix 
      diameter="20"
      aria-label="Anwendungen werden geladen">
    </mat-spinner>
    
    <!-- Search Icon -->
    <mat-icon *ngIf="!loading" matSuffix>search</mat-icon>
    
    <!-- Error Message -->
    <mat-error *ngIf="hasError">
      {{ errorMessage }}
    </mat-error>
    
    <!-- Autocomplete Panel -->
    <mat-autocomplete 
      #auto="matAutocomplete" 
      [displayWith]="displayFn"
      (optionSelected)="onApplicationSelected($event.option.value)"
      [attr.aria-label]="'Verfügbare Anwendungen'">
      
      <mat-option 
        *ngFor="let application of filteredApplications" 
        [value]="application"
        [attr.aria-label]="'Anwendung auswählen: ' + application.name">
        <div class="application-option">
          <div class="application-name">{{ application.name }}</div>
          <div class="application-description" *ngIf="application.description">
            {{ application.description }}
          </div>
        </div>
      </mat-option>
      
      <!-- No Results Message -->
      <mat-option 
        *ngIf="filteredApplications.length === 0 && !loading && searchControl.value" 
        disabled
        aria-label="Keine Anwendungen gefunden">
        <div class="no-results">
          <mat-icon>search_off</mat-icon>
          <span>Keine Anwendungen gefunden</span>
        </div>
      </mat-option>
    </mat-autocomplete>
  </mat-form-field>
</div>