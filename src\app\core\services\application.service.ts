import { Injectable } from '@angular/core';
import { Apollo } from 'apollo-angular';
import { Observable, map, catchError, throwError, of, delay } from 'rxjs';
import { FetchPolicy } from '@apollo/client/core';

import { Application, CreateApplicationInput, UpdateApplicationInput, ApplicationFilter } from '../models/application.model';
import {
  GET_ALL_APPLICATIONS,
  GET_APPLICATION_BY_ID,
  SEARCH_APPLICATIONS_BY_NAME,
  CHECK_APPLICATION_DEPENDENCIES
} from '../graphql/application.queries';
import {
  CREATE_APPLICATION,
  UPDATE_APPLICATION,
  DELETE_APPLICATION
} from '../graphql/application.mutations';

export interface ApplicationDependencies {
  // Dependencies can be extended in the future for other entity types
  canDelete: boolean;
  warningMessage?: string;
}

@Injectable({
  providedIn: 'root'
})
export class ApplicationService {
  private useMockData = false; // Set to false when backend is available
  
  // Mock data for development - using proper UUID format
  private mockApplications: Application[] = [
    {
      identifier: '550e8400-e29b-41d4-a716-************',
      name: 'Customer Portal',
      description: 'Hauptportal für Kundenzugriff',
      isDeleted: false,
      createdAt: '2024-01-15T10:30:00Z',
      updatedAt: '2024-01-20T14:45:00Z'
    },
    {
      identifier: '550e8400-e29b-41d4-a716-************',
      name: 'Admin Dashboard',
      description: 'Verwaltungsbereich für Administratoren',
      isDeleted: false,
      createdAt: '2024-01-10T09:15:00Z',
      updatedAt: '2024-01-18T16:20:00Z'
    },
    {
      identifier: '550e8400-e29b-41d4-a716-************',
      name: 'Payment Gateway',
      description: 'Zahlungsabwicklung und Transaktionsverarbeitung',
      isDeleted: false,
      createdAt: '2024-01-05T11:00:00Z',
      updatedAt: '2024-01-22T13:30:00Z'
    },
    {
      identifier: '550e8400-e29b-41d4-a716-************',
      name: 'Legacy System',
      description: 'Altes System - wird nicht mehr verwendet',
      isDeleted: true,
      createdAt: '2023-12-01T08:00:00Z',
      updatedAt: '2024-01-25T10:00:00Z'
    }
  ];

  constructor(private apollo: Apollo) {}

  /**
   * Get all applications with optional filtering
   */
  getAllApplications(filter: ApplicationFilter = {}): Observable<Application[]> {
    console.log('🔍 ApplicationService.getAllApplications called with filter:', filter);
    console.log('📊 useMockData flag:', this.useMockData);
    
    if (this.useMockData) {
      console.log('🎭 Using mock data for getAllApplications');
      let filteredApps = this.mockApplications;
      
      // Apply status filter if isDeleted is specified
      if (filter.isDeleted !== undefined) {
        filteredApps = filteredApps.filter(app => app.isDeleted === filter.isDeleted);
        console.log('🔍 Applied status filter for isDeleted:', filter.isDeleted);
      }
      
      // Apply search filter if searchTerm is provided
      if (filter.searchTerm && filter.searchTerm.trim()) {
        const searchTerm = filter.searchTerm.trim().toLowerCase();
        filteredApps = filteredApps.filter(app =>
          app.name.toLowerCase().includes(searchTerm) ||
          (app.description && app.description.toLowerCase().includes(searchTerm))
        );
        console.log('🔍 Applied search filter for term:', searchTerm);
      }
      
      console.log('🎭 Mock filtered applications:', filteredApps);
      return of(filteredApps).pipe(delay(300)); // Simulate network delay
    }

    console.log('🚀 Loading all applications from backend and filtering in frontend');
    
    // Always load all applications from backend (without any filter)
    return this.apollo.query<{ allApplications: Application[] }>({
      query: GET_ALL_APPLICATIONS,
      // Don't pass any variables - load all applications
      fetchPolicy: 'cache-first' // Use cache to improve performance
    }).pipe(
      map(result => {
        console.log('✅ GraphQL query successful, raw result:', result);
        console.log('📋 All applications data:', result.data.allApplications);
        
        let filteredApps = result.data.allApplications;
        
        // Apply status filter in frontend
        if (filter.isDeleted !== undefined) {
          console.log('🔍 Before status filter - total apps:', filteredApps.length);
          console.log('🔍 Filtering for isDeleted:', filter.isDeleted);
          console.log('🔍 Sample app isDeleted values:', filteredApps.slice(0, 3).map(app => ({ name: app.name, isDeleted: app.isDeleted })));
          
          filteredApps = filteredApps.filter(app => app.isDeleted === filter.isDeleted);
          
          console.log('🔍 After status filter - remaining apps:', filteredApps.length);
          console.log('🔍 Applied frontend status filter for isDeleted:', filter.isDeleted);
        }
        
        // Apply search filter in frontend
        if (filter.searchTerm && filter.searchTerm.trim()) {
          const searchTerm = filter.searchTerm.trim().toLowerCase();
          filteredApps = filteredApps.filter(app =>
            app.name.toLowerCase().includes(searchTerm) ||
            (app.description && app.description.toLowerCase().includes(searchTerm))
          );
          console.log('🔍 Applied frontend search filter for term:', searchTerm);
        }
        
        console.log('📋 Final filtered applications:', filteredApps);
        return filteredApps;
      }),
      catchError(error => {
        console.error('❌ GraphQL query failed in getAllApplications:', error);
        return this.handleError(error);
      })
    );
  }

  /**
   * Get a specific application by identifier
   */
  getApplicationById(identifier: string): Observable<Application> {
    if (this.useMockData) {
      const app = this.mockApplications.find(a => a.identifier === identifier);
      if (app) {
        return of(app).pipe(delay(200));
      } else {
        return throwError(() => new Error('Applikation nicht gefunden'));
      }
    }

    return this.apollo.query<{ application: Application }>({
      query: GET_APPLICATION_BY_ID,
      variables: { identifier }
    }).pipe(
      map(result => result.data.application),
      catchError(this.handleError)
    );
  }

  /**
   * Search applications by name - DEPRECATED: Use getAllApplications with filter instead
   * This method is kept for backward compatibility but redirects to getAllApplications
   */
  searchApplicationsByName(name: string): Observable<Application[]> {
    console.log('🔍 ApplicationService.searchApplicationsByName called with name:', name);
    console.log('⚠️ DEPRECATED: Redirecting to getAllApplications with search filter');
    
    return this.getAllApplications({ searchTerm: name, isDeleted: false });
  }

  /**
   * Create a new application
   */
  createApplication(input: CreateApplicationInput): Observable<Application> {
    if (this.useMockData) {
      const newApp: Application = {
        identifier: this.generateUUID(),
        name: input.name,
        description: input.description,
        isDeleted: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      this.mockApplications.push(newApp);
      return of(newApp).pipe(delay(500));
    }

    return this.apollo.mutate<{ createApplication: Application }>({
      mutation: CREATE_APPLICATION,
      variables: { input },
      update: (cache, { data }) => {
        if (data?.createApplication) {
          // Update the cache with the new application
          const existingApplications = cache.readQuery<{ allApplications: Application[] }>({
            query: GET_ALL_APPLICATIONS
          });

          if (existingApplications) {
            cache.writeQuery({
              query: GET_ALL_APPLICATIONS,
              data: {
                allApplications: [...existingApplications.allApplications, data.createApplication]
              }
            });
          }
        }
      }
    }).pipe(
      map(result => result.data!.createApplication),
      catchError(this.handleError)
    );
  }

  /**
   * Update an existing application
   */
  updateApplication(input: UpdateApplicationInput): Observable<Application> {
    if (this.useMockData) {
      const appIndex = this.mockApplications.findIndex(a => a.identifier === input.identifier);
      if (appIndex === -1) {
        return throwError(() => new Error('Applikation nicht gefunden'));
      }
      
      const updatedApp: Application = {
        ...this.mockApplications[appIndex],
        ...(input.name && { name: input.name }),
        ...(input.description !== undefined && { description: input.description }),
        ...(input.isDeleted !== undefined && { isDeleted: input.isDeleted }),
        updatedAt: new Date().toISOString()
      };
      
      this.mockApplications[appIndex] = updatedApp;
      return of(updatedApp).pipe(delay(500));
    }

    // Build variables object with only defined values
    const variables: any = {
      identifier: input.identifier
    };
    
    if (input.name !== undefined) {
      variables.name = input.name;
    }
    
    if (input.description !== undefined) {
      variables.description = input.description;
    }
    
    if (input.isDeleted !== undefined) {
      variables.isDeleted = input.isDeleted;
    }

    return this.apollo.mutate<{ updateApplication: Application }>({
      mutation: UPDATE_APPLICATION,
      variables,
      optimisticResponse: {
        updateApplication: {
          identifier: input.identifier,
          name: input.name || '',
          description: input.description || '',
          isDeleted: input.isDeleted || false,
          createdAt: '',
          updatedAt: new Date().toISOString()
        }
      }
    }).pipe(
      map(result => result.data!.updateApplication),
      catchError(this.handleError)
    );
  }

  /**
   * Soft delete an application
   */
  deleteApplication(identifier: string): Observable<Application> {
    if (this.useMockData) {
      const appIndex = this.mockApplications.findIndex(a => a.identifier === identifier);
      if (appIndex === -1) {
        return throwError(() => new Error('Applikation nicht gefunden'));
      }
      
      const deletedApp: Application = {
        ...this.mockApplications[appIndex],
        isDeleted: true,
        updatedAt: new Date().toISOString()
      };
      
      this.mockApplications[appIndex] = deletedApp;
      return of(deletedApp).pipe(delay(500));
    }

    return this.apollo.mutate<{ deleteApplication: Application }>({
      mutation: DELETE_APPLICATION,
      variables: { identifier },
      update: (cache) => {
        // Remove from active applications cache
        const existingApplications = cache.readQuery<{ allApplications: Application[] }>({
          query: GET_ALL_APPLICATIONS
        });

        if (existingApplications) {
          cache.writeQuery({
            query: GET_ALL_APPLICATIONS,
            data: {
              allApplications: existingApplications.allApplications.filter(
                app => app.identifier !== identifier
              )
            }
          });
        }
      }
    }).pipe(
      map(result => result.data!.deleteApplication),
      catchError(this.handleError)
    );
  }

  /**
   * Check if application has dependencies (incidents)
   */
  checkDependencies(identifier: string): Observable<ApplicationDependencies> {
    if (this.useMockData) {
      const mockDependencies: ApplicationDependencies = {
        canDelete: true,
        warningMessage: undefined
      };
      return of(mockDependencies).pipe(delay(200));
    }

    return this.apollo.query<{ applicationDependencies: ApplicationDependencies }>({
      query: CHECK_APPLICATION_DEPENDENCIES,
      variables: { identifier },
      fetchPolicy: 'network-only' as FetchPolicy
    }).pipe(
      map(result => result.data.applicationDependencies),
      catchError(this.handleError)
    );
  }

  /**
   * Generate a UUID v4 for mock data
   */
  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Handle GraphQL errors
   */
  private handleError(error: any): Observable<never> {
    console.error('🚨 ApplicationService Error Details:', error);
    console.error('🔍 Error type:', typeof error);
    console.error('🔍 Error constructor:', error.constructor.name);
    
    if (error.graphQLErrors) {
      console.error('📋 GraphQL Errors:', error.graphQLErrors);
    }
    
    if (error.networkError) {
      console.error('🌐 Network Error:', error.networkError);
    }
    
    if (error.message) {
      console.error('💬 Error Message:', error.message);
    }
    
    let errorMessage = 'Ein unbekannter Fehler ist aufgetreten.';
    
    if (error.graphQLErrors?.length > 0) {
      errorMessage = error.graphQLErrors[0].message;
      console.log('🎯 Using GraphQL error message:', errorMessage);
    } else if (error.networkError) {
      errorMessage = 'Netzwerkfehler. Bitte versuchen Sie es später erneut.';
      console.log('🎯 Using network error message:', errorMessage);
    }
    
    console.error('🎯 Final error message to user:', errorMessage);
    return throwError(() => new Error(errorMessage));
  }
}