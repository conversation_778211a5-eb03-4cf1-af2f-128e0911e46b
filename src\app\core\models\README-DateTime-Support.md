# DateTime Support Implementation

## Overview

This document describes the implementation of full DateTime support for the Incident data model in the StoerungsBuddy Frontend v2 project.

## Changes Made

### 1. Enhanced Incident Data Model (`incident.model.ts`)

- **Updated Time Fields**: All time-related fields now support both `Date` objects and ISO strings:
  - `startTime: Date | string`
  - `plannedEndTime?: Date | string`
  - `actualEndTime?: Date | string`
  - `createdAt: Date | string`
  - `updatedAt: Date | string`

- **Type Safety**: Maintained strict TypeScript typing while allowing flexibility for both frontend Date objects and GraphQL ISO strings

- **Documentation**: Added comprehensive JSDoc comments explaining the dual-type support

### 2. GraphQL Types Enhancement (`graphql/types.ts`)

- **Consistent Documentation**: Added clear comments explaining that GraphQL fields receive/send ISO strings
- **Type Clarity**: Maintained string types for GraphQL operations while documenting the conversion process

### 3. DateTime Utilities (`utils/datetime.utils.ts`)

Created comprehensive utility functions for DateTime handling:

#### Core Conversion Functions
- `toISOString(dateTime: Date | string): string` - Converts to ISO string for GraphQL
- `toDate(isoString: string): Date` - Converts ISO string to Date object
- `ensureDate(dateTime: Date | string): Date` - Ensures result is a Date object

#### Validation Functions
- `isValidISOString(dateString: string): boolean` - Validates ISO date strings
- `isPastDateTime(dateTime: Date | string): boolean` - Checks if date is in the past
- `isFutureDateTime(dateTime: Date | string): boolean` - Checks if date is in the future

#### Formatting Functions
- `formatDateTime()` - Locale-aware date/time formatting (default: German)
- `formatDateForInput()` - Format for HTML date inputs (YYYY-MM-DD)
- `formatDateTimeForInput()` - Format for datetime-local inputs (YYYY-MM-DDTHH:mm)

#### Calculation Functions
- `calculateDuration()` - Calculate duration between two dates
- `formatDuration()` - Human-readable duration formatting
- `createDateFromInputs()` - Create Date from separate date/time inputs

### 4. Incident Adapter Service (`services/incident-adapter.service.ts`)

Created a service to handle conversion between frontend models and GraphQL types:

#### Key Features
- **Bidirectional Conversion**: Frontend ↔ GraphQL type conversion
- **DateTime Handling**: Automatic conversion between Date objects and ISO strings
- **Validation**: Built-in DateTime field validation
- **Type Safety**: Maintains TypeScript safety throughout the conversion process

#### Main Methods
- `fromGraphQLResponse()` - Converts GraphQL response to frontend Incident model
- `toGraphQLCreateInput()` - Converts frontend input to GraphQL create format
- `toGraphQLUpdateInput()` - Converts frontend input to GraphQL update format
- `validateDateTimeFields()` - Validates DateTime fields and relationships

### 5. Comprehensive Testing

#### Unit Tests
- **Model Tests** (`incident.model.spec.ts`): Tests for all interface variations and type safety
- **Utility Tests** (`datetime.utils.spec.ts`): Comprehensive testing of all utility functions
- **Adapter Tests** (`incident-adapter.service.spec.ts`): Tests for GraphQL conversion and validation

#### Integration Tests
- **GraphQL Compatibility** (`incident.service.integration.spec.ts`): Tests for end-to-end DateTime handling
- **Round-trip Conversion**: Ensures data integrity through GraphQL ↔ Frontend conversions
- **Validation Integration**: Tests for DateTime validation in real-world scenarios

## GraphQL Schema Compatibility

### Backend Requirements
The implementation assumes the GraphQL backend uses:
- `DateTime` scalar type for time fields
- ISO 8601 string format for DateTime transmission
- Optional time fields (plannedEndTime, actualEndTime)

### Current GraphQL Operations
All existing GraphQL queries and mutations are compatible:
- `GET_ALL_INCIDENTS` - Returns ISO strings, converted to Date objects
- `GET_MY_INCIDENTS` - Returns ISO strings, converted to Date objects  
- `CREATE_INCIDENT` - Accepts Date objects, converts to ISO strings
- `UPDATE_INCIDENT` - Accepts Date objects, converts to ISO strings
- `RESOLVE_INCIDENT` - Accepts Date objects, converts to ISO strings

## Usage Examples

### Creating an Incident with Date Objects
```typescript
const createInput: CreateIncidentInput = {
  title: 'System Outage',
  type: IncidentType.STOERUNG,
  startTime: new Date('2024-01-15T09:00:00.000Z'),
  plannedEndTime: new Date('2024-01-15T17:00:00.000Z'),
  applicationIds: ['app-123']
};

// The adapter automatically converts Date objects to ISO strings for GraphQL
const graphqlInput = adapterService.toGraphQLCreateInput(createInput);
```

### Working with GraphQL Responses
```typescript
// GraphQL returns ISO strings
const graphqlResponse: IncidentResponse = {
  startTime: '2024-01-15T09:00:00.000Z',
  // ... other fields
};

// Adapter converts to frontend model with Date objects
const incident = adapterService.fromGraphQLResponse(graphqlResponse);
console.log(incident.startTime instanceof Date); // true
```

### Using DateTime Utilities
```typescript
import { formatDateTime, calculateDuration, isPastDateTime } from '../utils/datetime.utils';

// Format for display
const displayTime = formatDateTime(incident.startTime, 'de-DE');

// Calculate incident duration
const duration = calculateDuration(incident.startTime, incident.actualEndTime);

// Check if incident is overdue
const isOverdue = incident.plannedEndTime && isPastDateTime(incident.plannedEndTime);
```

## Migration Notes

### Existing Code Compatibility
- **Backward Compatible**: Existing code using string time fields continues to work
- **Gradual Migration**: Components can be updated incrementally to use Date objects
- **Type Safety**: TypeScript will help identify areas that need updates

### Recommended Migration Path
1. Update services to use the new IncidentAdapterService
2. Update components to work with Date objects for better UX
3. Use DateTime utilities for formatting and calculations
4. Add validation using the adapter's validation methods

## Next Steps

### Dependent Tasks
1. **UI Components**: Update incident forms to use datetime-local inputs
2. **Date Pickers**: Implement Angular Material date/time pickers
3. **Validation**: Add client-side DateTime validation to forms
4. **Formatting**: Update display components to use new formatting utilities
5. **Testing**: Add E2E tests for DateTime functionality

### Performance Considerations
- DateTime conversions are lightweight and cached where possible
- Validation is performed only when needed
- Utilities are tree-shakeable for optimal bundle size

## Technical Constraints Compliance

✅ **Angular 19**: Uses standalone components and modern Angular patterns
✅ **TypeScript Strict**: Full type safety with strict TypeScript configuration  
✅ **Apollo Client**: Compatible with GraphQL DateTime scalar types
✅ **NgRx**: Ready for integration with state management
✅ **Angular Material**: Utilities support Material date/time components

## Security & Validation

- **Input Sanitization**: All DateTime inputs are validated before processing
- **Type Safety**: TypeScript prevents invalid DateTime assignments
- **GraphQL Compatibility**: Ensures only valid ISO strings are sent to backend
- **Error Handling**: Graceful handling of invalid DateTime formats