import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { Router } from '@angular/router';
import { Apollo } from 'apollo-angular';
import {
  User,
  AuthUser,
  LoginCredentials,
  LoginResponse
} from '../models/user.model';
import { GET_CURRENT_USER } from '../graphql/auth.queries';

interface BackendLoginResponse {
  token: string;
  user: {
    id: string;
    email: string;
    displayName: string;
    role: string;
  };
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private http = inject(HttpClient);
  private router = inject(Router);
  private apollo = inject(Apollo);
  
  private readonly API_BASE_URL = 'http://localhost:5079';
  private readonly TOKEN_KEY = 'auth_token';
  private readonly REFRESH_TOKEN_KEY = 'refresh_token';
  private readonly USER_KEY = 'current_user';

  private currentUserSubject = new BehaviorSubject<User | null>(null);
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);

  public currentUser$ = this.currentUserSubject.asObservable();
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();

  constructor() {
    this.initializeAuth();
  }

  private initializeAuth(): void {
    const token = this.getStoredToken();
    const user = this.getStoredUser();
    
    if (token && user && this.isTokenValid(token)) {
      this.currentUserSubject.next(user);
      this.isAuthenticatedSubject.next(true);
    } else {
      this.clearStoredAuth();
    }
  }

  login(credentials: LoginCredentials): Observable<LoginResponse> {
    return this.http.post<BackendLoginResponse>(`${this.API_BASE_URL}/api/auth/login`, {
      email: credentials.email,
      password: credentials.password
    }).pipe(
      map(backendResponse => this.mapBackendLoginResponse(backendResponse)),
      tap(response => {
        this.setAuthData(response);
      }),
      catchError((error: HttpErrorResponse) => {
        console.error('Login failed:', error);
        const errorMessage = error.error?.message || 'Login failed';
        return throwError(() => new Error(errorMessage));
      })
    );
  }

  logout(): void {
    this.clearStoredAuth();
    this.currentUserSubject.next(null);
    this.isAuthenticatedSubject.next(false);
    this.router.navigate(['/login']);
  }

  refreshToken(): Observable<string> {
    const refreshToken = this.getStoredRefreshToken();
    if (!refreshToken) {
      return throwError(() => new Error('No refresh token available'));
    }

    // TODO: Implement when backend supports refresh tokens
    // For now, use mock implementation
    return this.mockRefreshToken(refreshToken).pipe(
      tap(newToken => {
        localStorage.setItem(this.TOKEN_KEY, newToken);
      }),
      catchError(error => {
        this.logout();
        return throwError(() => error);
      })
    );
  }

  // Future implementation for refresh token endpoint
  private refreshTokenFromAPI(refreshToken: string): Observable<string> {
    return this.http.post<{ token: string }>(`${this.API_BASE_URL}/api/auth/refresh`, {
      refreshToken
    }).pipe(
      map(response => response.token),
      catchError((error: HttpErrorResponse) => {
        console.error('Token refresh failed:', error);
        return throwError(() => new Error('Token refresh failed'));
      })
    );
  }

  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  isAuthenticated(): boolean {
    return this.isAuthenticatedSubject.value;
  }

  getToken(): string | null {
    return this.getStoredToken();
  }

  getCurrentUserFromAPI(): Observable<User | null> {
    return this.apollo.query<{ currentUser: any }>({
      query: GET_CURRENT_USER,
      errorPolicy: 'all'
    }).pipe(
      map(result => {
        if (result.data?.currentUser) {
          const userData = result.data.currentUser;
          const user: User = {
            id: userData.identifier,
            email: userData.email,
            firstName: userData.displayName.split(' ')[0] || 'User',
            lastName: userData.displayName.split(' ').slice(1).join(' ') || '',
            role: userData.role.toLowerCase() as any,
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
          };
          return user;
        }
        return null;
      }),
      catchError(error => {
        console.error('Failed to fetch current user:', error);
        return of(null);
      })
    );
  }

  private setAuthData(response: LoginResponse): void {
    const { user, token, refreshToken } = response;
    
    localStorage.setItem(this.TOKEN_KEY, token);
    localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
    localStorage.setItem(this.USER_KEY, JSON.stringify(user));
    
    this.currentUserSubject.next(user);
    this.isAuthenticatedSubject.next(true);
  }

  private clearStoredAuth(): void {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    localStorage.removeItem(this.USER_KEY);
  }

  private getStoredToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  private getStoredRefreshToken(): string | null {
    return localStorage.getItem(this.REFRESH_TOKEN_KEY);
  }

  private getStoredUser(): User | null {
    const userJson = localStorage.getItem(this.USER_KEY);
    if (userJson) {
      try {
        return JSON.parse(userJson);
      } catch {
        return null;
      }
    }
    return null;
  }

  private isTokenValid(token: string): boolean {
    // JWT token validation
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp * 1000 > Date.now();
    } catch {
      return false;
    }
  }

  isTokenExpired(token?: string): boolean {
    const tokenToCheck = token || this.getStoredToken();
    if (!tokenToCheck) return true;
    
    try {
      const payload = JSON.parse(atob(tokenToCheck.split('.')[1]));
      return payload.exp * 1000 <= Date.now();
    } catch {
      return true;
    }
  }

  getTokenExpirationDate(token?: string): Date | null {
    const tokenToCheck = token || this.getStoredToken();
    if (!tokenToCheck) return null;
    
    try {
      const payload = JSON.parse(atob(tokenToCheck.split('.')[1]));
      return new Date(payload.exp * 1000);
    } catch {
      return null;
    }
  }

  // Check if token expires within the next 5 minutes
  shouldRefreshToken(token?: string): boolean {
    const tokenToCheck = token || this.getStoredToken();
    if (!tokenToCheck) return false;
    
    try {
      const payload = JSON.parse(atob(tokenToCheck.split('.')[1]));
      const expirationTime = payload.exp * 1000;
      const fiveMinutesFromNow = Date.now() + (5 * 60 * 1000);
      return expirationTime <= fiveMinutesFromNow;
    } catch {
      return false;
    }
  }

  private mapBackendLoginResponse(backendResponse: BackendLoginResponse): LoginResponse {
    // Map backend response to frontend LoginResponse format
    const user: User = {
      id: backendResponse.user.id,
      email: backendResponse.user.email,
      firstName: backendResponse.user.displayName.split(' ')[0] || 'User',
      lastName: backendResponse.user.displayName.split(' ').slice(1).join(' ') || '',
      role: backendResponse.user.role.toLowerCase() as any,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    return {
      user,
      token: backendResponse.token,
      refreshToken: '', // Backend doesn't provide refresh token yet
      expiresIn: 86400 // 24 hours as per API docs
    };
  }

  // Mock implementations - replace with actual API calls
  private mockLogin(credentials: LoginCredentials): Observable<LoginResponse> {
    // Simulate API delay
    return new Observable(observer => {
      setTimeout(() => {
        if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {
          const mockUser: User = {
            id: '1',
            email: credentials.email,
            firstName: 'Admin',
            lastName: 'User',
            role: 'admin' as any,
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
          };

          const response: LoginResponse = {
            user: mockUser,
            token: this.generateMockToken(mockUser),
            refreshToken: 'mock-refresh-token',
            expiresIn: 3600
          };

          observer.next(response);
          observer.complete();
        } else {
          observer.error({ message: 'Invalid credentials' });
        }
      }, 1000);
    });
  }

  private mockRefreshToken(refreshToken: string): Observable<string> {
    return new Observable(observer => {
      setTimeout(() => {
        const user = this.getCurrentUser();
        if (user) {
          observer.next(this.generateMockToken(user));
          observer.complete();
        } else {
          observer.error({ message: 'Invalid refresh token' });
        }
      }, 500);
    });
  }

  private generateMockToken(user: User): string {
    const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
    const payload = btoa(JSON.stringify({
      sub: user.id,
      email: user.email,
      role: user.role,
      exp: Math.floor(Date.now() / 1000) + 3600
    }));
    const signature = btoa('mock-signature');
    
    return `${header}.${payload}.${signature}`;
  }
}