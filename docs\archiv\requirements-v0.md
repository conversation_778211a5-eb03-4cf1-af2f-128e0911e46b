# Anforderungen: Backend - Administrations-UI und Backend-API

## Übersicht

Das Backend des "Störungs-Buddy"-Projekts besteht aus einer Administrations-UI und einer Backend-API. Es richtet sich an Administratoren und dient zur Stammdatenpflege, Verwal<PERSON><PERSON> von Störungsmeldungen und Wartungsfenstern und stellt Schnittstellen für das Frontend bereit. Die Authentifizierung soll über oAuth2 erfolgen für den MVP mit einer einfachen Absicherung über eine Loginmaske.

## Zielgruppe

- Administratoren, die für die Verwaltung der Systeme und Störungsmeldungen verantwortlich sind
- Indirekt: <PERSON><PERSON><PERSON><PERSON>, die über das Frontend auf die Daten zugreifen

## Funktionale Anforderungen

### Kernfunktionen (1. Iteration und MVP-Scope)

#### F1: Störungs- und Wartungsmeldungen verwalten
- **Beschreibung:** Erstellen, Bearbeiten und Erledigen von Störungs- und Wartungsmeldungen
- **Priorität:** Hoch
- **Akzeptanzkriterien:** Administratoren können Störungsmeldungen und Wartungsfenster vollständig verwalten

#### F2: Stammdatenpflege
- **Beschreibung:** Zentrale Verwaltung von Applikationen in einer nicht hierarchischen Struktur
- **Priorität:** Hoch
- **Akzeptanzkriterien:** Administratoren können Systeme hinzufügen, bearbeiten und entfernen

#### F3: Benutzer-Subscriptions
- **Beschreibung:** Verwaltung der Benutzer-Abonnements für Systeme und zugehörigen Nutzern. Wir nutzen die User, die sich einmalig über das Frontend angemeldet haben und so in einer Anmeldeliste stehen. Initial werden jedem User automatisch alle Applikationen zugewiesen.
- **Priorität:** Mittel
- **Akzeptanzkriterien:** Administratoren können einsehen und verwalten, welche Benutzer welche Systeme abonniert haben

#### F4: Priorisierung von Meldungen
- **Beschreibung:** Kennzeichnung von Meldungen nach Wichtigkeit (High-Critical und Normal)
- **Priorität:** Mittel
- **Akzeptanzkriterien:** Administratoren können Meldungen priorisieren

#### F5: API-Bereitstellung
- **Beschreibung:** Bereitstellung von Schnittstellen für das Frontend:
   - Endpunkt: Aktuelle Liste der Subscriptions für Meldungen für den angemeldeten Nutzer
   - Endpunkt: Alle verfügbaren Systeme
   - Endpunkt: Aktuelle Systemmeldungen für vorhandene Subscriptions für Störungen und Wartungsfenster

- **Priorität:** Hoch
- **Akzeptanzkriterien:** API stellt alle notwendigen Endpunkte für das Frontend bereit

### Erweiterte Funktionen (2. Iteration)

#### EF1: Historische Störungen
- **Beschreibung:** Anzeige vergangener Störungen mit Statusverlauf
- **Priorität:** Niedrig
- **Akzeptanzkriterien:** Vergangene Störungen können eingesehen werden

#### EF3: AD-Authentifikation
- **Beschreibung:** Integration mit Azure-AD für Benutzerauthentifizierung und -autorisierung zur Anmeldung der Administratoren
- **Priorität:** Niedrig
- **Akzeptanzkriterien:** Benutzer können sich über Azure-AD authentifizieren

## Nicht-funktionale Anforderungen

### Leistung

#### NF1: Ladezeit
- **Beschreibung:** Die Administrations-UI soll innerhalb von 3 Sekunden laden
- **Messbarkeit:** Zeit bis zur vollständigen Funktionsbereitschaft < 3s

#### NF2: API-Reaktionszeit
- **Beschreibung:** API-Anfragen sollen innerhalb von 500ms beantwortet werden
- **Messbarkeit:** Antwortzeit für API-Anfragen < 500ms

#### NF3: Ressourcenverbrauch
- **Beschreibung:** Das Backend soll effizient mit Serverressourcen umgehen
- **Messbarkeit:** CPU-Auslastung < 30%, RAM-Verbrauch angemessen für die Anwendungsgröße

### Sicherheit

#### NF4: Authentifizierung
- **Beschreibung:** Zugriff auf die Administrations-UI und API nur für autorisierte Administratoren
- **Messbarkeit:** Erfolgreiche Authentifizierung und Autorisierung erforderlich

#### NF5: Datenschutz
- **Beschreibung:** Sichere Speicherung und Verarbeitung von Daten
- **Messbarkeit:** Einhaltung der Datenschutzrichtlinien, verschlüsselte Datenübertragung

### Benutzerfreundlichkeit (für Administrations-UI)

#### NF7: Intuitivität
- **Beschreibung:** Die Benutzeroberfläche soll ohne umfangreiche Schulung bedienbar sein
- **Messbarkeit:** Erfolgreiche Durchführung von Administrationsaufgaben ohne detaillierte Anleitung

#### NF8: Responsivität
- **Beschreibung:** Die Anwendung soll auf Benutzereingaben ohne wahrnehmbare Verzögerung reagieren
- **Messbarkeit:** Reaktionszeit auf Benutzereingaben < 200ms

### Zuverlässigkeit

#### NF10: Verfügbarkeit
- **Beschreibung:** Das Backend soll zu 99,9% verfügbar sein
- **Messbarkeit:** Ausfallzeit < 0,1%

#### NF11: Fehlertoleranz
- **Beschreibung:** Das Backend soll robust gegenüber Fehlern sein und diese angemessen behandeln
- **Messbarkeit:** Korrekte Fehlerbehandlung, keine Systemabstürze bei Fehlereingaben

### Wartbarkeit

#### NF13: Modularität
- **Beschreibung:** Das Backend soll modular aufgebaut sein
- **Messbarkeit:** Komponenten können unabhängig voneinander getestet und ausgetauscht werden

#### NF14: Dokumentation
- **Beschreibung:** Der Code und die API sollen gut dokumentiert sein
- **Messbarkeit:** Vollständige Dokumentation aller Klassen, Methoden und API-Endpunkte

## Schnittstellen

### API-Endpunkte (Beispiele)

- `/api/systems` - Verwaltung der Systeme
- `/api/notifications` - Verwaltung der Störungs- und Wartungsmeldungen
- `/api/subscriptions` - Verwaltung der Benutzer-Abonnements
- `/api/users` - Benutzerverwaltung (in Verbindung mit AD-Integration)

### Datenbank

- Speicherung von Systeminformationen, Störungsmeldungen, Benutzerabonnements und weiteren relevanten Daten
- Sicherstellung der Datenintegrität und -konsistenz

## Technische Anforderungen

- Das Backend soll als Webapplikation mit moderner Architektur implementiert werden
- Die API soll RESTful sein und JSON als Datenformat verwenden
- Sichere Authentifizierung und Autorisierung über Azure AD
- Skalierbarkeit für zukünftige Erweiterungen