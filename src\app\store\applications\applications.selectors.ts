import { createFeatureSelector, createSelector } from '@ngrx/store';
import { ApplicationsState } from './applications.state';

export const selectApplicationsState = createFeatureSelector<ApplicationsState>('applications');

export const selectAllApplications = createSelector(
  selectApplicationsState,
  (state) => state.applications
);

export const selectSelectedApplication = createSelector(
  selectApplicationsState,
  (state) => state.selectedApplication
);

export const selectApplicationsLoading = createSelector(
  selectApplicationsState,
  (state) => state.loading
);

export const selectApplicationsError = createSelector(
  selectApplicationsState,
  (state) => state.error
);