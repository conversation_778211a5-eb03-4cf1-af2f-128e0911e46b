{"metadata": {"version": "1.0.0", "createdAt": "2025-06-27T14:27:00.000Z", "updatedAt": "2025-06-27T14:27:00.000Z", "projectName": "StoerungsBuddy Frontend - Uhrzeit-Erfassung", "description": "Tasks für die Implementierung der Uhrzeit-Erfassung in Incident-Dialogen"}, "tags": {"master": {"name": "master", "description": "Hauptentwicklungszweig für Uhrzeit-Erfassung Features", "createdAt": "2025-06-27T14:27:00.000Z", "tasks": [{"id": 1, "title": "Datenmodell für DateTime-Unterstützung erweitern", "description": "Erweiter<PERSON> das Incident-Datenmodell um vollständige DateTime-Unterstützung für präzise Zeiterfassung", "status": "done", "priority": "high", "dependencies": [], "details": "Aktualisiere das Incident.model.ts um sicherzustellen, dass alle Zeitfelder (startTime, plannedEndTime, actualEndTime) vollständige DateTime-Objekte unterstützen. Prüfe GraphQL-Schema-Kompatibilität und Backend-Integration.", "testStrategy": "Unit-Tests für Datenmodell-Validierung, Integration-Tests für GraphQL-Queries mit DateTime-Feldern", "subtasks": []}, {"id": 2, "title": "DateTime-Picker Komponente implementieren", "description": "<PERSON><PERSON><PERSON> eine wiederverwendbare DateTime-Picker Komponente basierend auf Angular Material", "status": "done", "priority": "high", "dependencies": [1], "details": "Implementiere eine Angular-Komponente die sowohl Datum als auch Uhrzeit erfassen kann. Verwende Angular Material DatePicker kombiniert mit TimePicker oder einen integrierten DateTimePicker. Stelle deutsche Lokalisierung und Accessibility sicher.", "testStrategy": "Komponenten-Tests für DateTime-Eingabe, Validierung und Formatierung. E2E-Tests für Benutzerinteraktion.", "subtasks": []}, {"id": 3, "title": "Incident Create Form um Uhrzeit-Eingabe erweitern", "description": "Erweitere IncidentCreateFormComponent um Uhrzeit-Erfassung für Start- und geplante Endzeit", "status": "pending", "priority": "high", "dependencies": [2], "details": "Integriere die DateTime-Picker Komponente in das Incident-Erstellungsformular. Implementiere Validierung für sinnvolle Zeitangaben (plannedEndTime nach startTime). Aktualisiere FormControls und Validatoren.", "testStrategy": "Formular-Validierung testen, DateTime-Eingabe und -Ausgabe prüfen, Fehlerbehandlung validieren", "subtasks": []}, {"id": 4, "title": "Incident Edit Form um Uhrzeit-Bearbeitung erweitern", "description": "Erweitere IncidentEditFormComponent um vollständige DateTime-Bearbeitung für alle Zeitfelder", "status": "pending", "priority": "high", "dependencies": [2], "details": "Integriere DateTime-Picker für startTime, plannedEndTime und actualEndTime in das Bearbeitungsformular. <PERSON><PERSON> sic<PERSON>, dass bestehende Uhrzeiten korrekt geladen und angezeigt werden. Implementiere Fallback für Incidents ohne Uhrzeiten.", "testStrategy": "Laden bestehender DateTime-Werte testen, Bearbeitung und Speicherung validieren, Fallback-Verhalten prüfen", "subtasks": []}, {"id": 5, "title": "Incident Create <PERSON><PERSON> aktualisieren", "description": "Aktualisiere IncidentCreateDialogComponent um die erweiterte DateTime-Funktionalität zu unterstützen", "status": "pending", "priority": "medium", "dependencies": [3], "details": "Passe den Dialog an die erweiterten Formular-Funktionen an. <PERSON><PERSON>, dass DateTime-Daten korrekt an das Backend übertragen werden. Aktualisiere Dialog-Größe falls notwendig.", "testStrategy": "Dialog-Integration testen, Datenübertragung validieren, UI/UX auf verschiedenen Bildschirmgrößen prüfen", "subtasks": []}, {"id": 6, "title": "Incident Edit Dialog aktualisieren", "description": "Aktualisiere IncidentEditDialogComponent um die erweiterte DateTime-Bearbeitung zu unterstützen", "status": "pending", "priority": "medium", "dependencies": [4], "details": "Integriere die erweiterten Bearbeitungsfunktionen in den Dialog. <PERSON><PERSON> sicher, dass DateTime-Updates korrekt verarbeitet und gespeichert werden. Behandle Edge-Cases für bestehende Incidents ohne Uhrzeiten.", "testStrategy": "Dialog-Funktionalität testen, Update-Operationen validieren, Backward-Kompatibilität sicherstellen", "subtasks": []}, {"id": 7, "title": "Incident Detail View um DateTime-Anzeige erweitern", "description": "Erweitere IncidentDetailComponent um vollständige DateTime-Informationen anzuzeigen", "status": "pending", "priority": "medium", "dependencies": [1], "details": "Aktualisiere die Detail-Ansicht um Datum und Uhrzeit für alle Zeitfelder anzuzeigen. Implementiere benutzerfreundliche Formatierung (z.B. '27.06.2025, 16:30'). Aktualisiere Dauer-Berechnung um Uhrzeiten zu berücksichtigen.", "testStrategy": "DateTime-An<PERSON>ige testen, Formatierung validieren, Dauer-Berechnung mit Uhrzeiten prüfen", "subtasks": []}, {"id": 8, "title": "Incident List View um DateTime-Anzeige erweitern", "description": "Erweitere IncidentListComponent um Uhrzeiten in der Tabellen-Ansicht anzuzeigen", "status": "pending", "priority": "medium", "dependencies": [1], "details": "Aktualisiere die Listen-Ansicht um kompakte DateTime-Darstellung zu zeigen (z.B. '27.06. 16:30'). Aktualisiere Sortierung um vollständige DateTime-Information zu nutzen. Stelle responsive Darstellung sicher.", "testStrategy": "Tabellen-An<PERSON>ige testen, Sortierung validieren, responsive Verhalten auf mobilen Geräten prüfen", "subtasks": []}, {"id": 9, "title": "Dashboard DateTime-Anzeige aktualisieren", "description": "Aktualisiere ActiveIncidentsListComponent und Dashboard um Uhrzeiten anzuzeigen", "status": "pending", "priority": "low", "dependencies": [1], "details": "Erweitere die Dashboard-Komponenten um Uhrzeiten für aktuelle Incidents anzuzeigen. Implementiere kompakte Darstellung für Dashboard-Übersicht. Stelle Konsistenz mit anderen Ansichten sicher.", "testStrategy": "Dashboard-<PERSON><PERSON><PERSON> test<PERSON>, Konsistenz der DateTime-Formatierung prüfen, Performance bei vielen Incidents validieren", "subtasks": []}, {"id": 10, "title": "NgRx State Management für DateTime aktualisieren", "description": "Aktualisiere Actions, Reducers und Effects für vollständige DateTime-Unterstützung", "status": "pending", "priority": "medium", "dependencies": [1], "details": "Prüfe und aktualisiere Incident-Actions für DateTime-Handling. Validiere Selectors für korrekte DateTime-Verarbeitung. Aktualisiere Effects für API-Calls mit DateTime-Daten. Stelle Typsicherheit sicher.", "testStrategy": "State-Management testen, Action-Dispatching validieren, Selector-Funktionalität prüfen", "subtasks": []}, {"id": 11, "title": "Validierung und Fehlerbehandlung für DateTime implementieren", "description": "Implementiere umfassende Validierung und Fehlerbehandlung für DateTime-Eingaben", "status": "pending", "priority": "medium", "dependencies": [2, 3, 4], "details": "<PERSON>rstelle Validatoren für DateTime-Kombinationen (z.B. Endzeit nach Startzeit). Implementiere Fehlerbehandlung für ungültige Eingaben. Stelle benutzerfreundliche Fehlermeldungen bereit. Behandle Timezone-Edge-Cases.", "testStrategy": "Validierungs-<PERSON><PERSON><PERSON> testen, Fehlerbehandlung validieren, Edge-Cases für DateTime-Eingaben prüfen", "subtasks": []}, {"id": 12, "title": "Mobile Responsiveness und Accessibility sicherstellen", "description": "<PERSON><PERSON> sic<PERSON>, dass alle DateTime-Funktionen auf mobilen Geräten funktionieren und accessibility-konform sind", "status": "pending", "priority": "medium", "dependencies": [2, 3, 4, 7, 8], "details": "Teste und optimiere DateTime-Picker für mobile Geräte. Implementiere Accessibility-Features (ARIA-Labels, Keyboard-Navigation). <PERSON><PERSON>, dass Touch-Interaktionen funktionieren. Validiere Responsive Design.", "testStrategy": "Mobile Testing auf verschiedenen Geräten, Accessibility-Tests mit Screen-Readern, Touch-Interaktion validieren", "subtasks": []}]}}, "currentTag": "master"}