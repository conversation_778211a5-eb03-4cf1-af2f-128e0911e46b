.modern-delete-dialog {
  // Subtle header similar to normal dialogs
  .dialog-header {
    &.warning-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24px 24px 16px 24px;
      border-bottom: 1px solid var(--mat-divider-color);
      background-color: var(--mat-surface);

      .header-content {
        display: flex;
        align-items: center;
        gap: 12px;
        flex: 1;

        .header-icon {
          &.warning-icon {
            color: var(--mat-warn);
            font-size: 24px;
            width: 24px;
            height: 24px;

            mat-icon {
              font-size: 24px;
              width: 24px;
              height: 24px;
            }
          }
        }

        .header-text {
          flex: 1;

          h2 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 500;
            color: var(--mat-text-primary);
            line-height: 1.2;
          }

          .header-subtitle {
            margin: 4px 0 0 0;
            font-size: 0.875rem;
            color: var(--mat-text-secondary);
            font-weight: 400;
          }
        }
      }
    }
  }

  // Subtle dialog content similar to normal dialogs
  .dialog-content {
    flex: 1;
    overflow-y: auto;
    padding: 0;
    max-height: calc(90vh - 120px);

    .delete-content {
      display: flex;
      flex-direction: column;
      gap: 16px;
      padding: 24px;

      // Main confirmation section - more subtle
      .confirmation-section {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 16px;
        background-color: var(--mat-surface-variant);
        border: 1px solid var(--mat-divider-color);
        border-radius: 8px;

        .confirmation-icon {
          color: var(--mat-warn);
          font-size: 20px;
          width: 20px;
          height: 20px;
          margin-top: 2px;
          flex-shrink: 0;

          mat-icon {
            font-size: 20px;
            width: 20px;
            height: 20px;
          }
        }

        .confirmation-message {
          margin: 0;
          font-size: 0.875rem;
          color: var(--mat-text-primary);
          line-height: 1.5;
          font-weight: 500;
        }
      }

      // Warning alert - more subtle
      .warning-alert {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 12px 16px;
        background-color: var(--mat-warn-container);
        border-left: 4px solid var(--mat-warn);
        border-radius: 4px;

        mat-icon {
          font-size: 18px;
          width: 18px;
          height: 18px;
          margin-top: 2px;
          color: var(--mat-warn);
        }

        .warning-content {
          display: flex;
          flex-direction: column;
          gap: 4px;

          .warning-title {
            font-weight: 600;
            font-size: 0.8125rem;
            color: var(--mat-on-warn-container);
          }

          .warning-message {
            font-size: 0.75rem;
            color: var(--mat-on-warn-container);
            opacity: 0.8;
          }
        }
      }

      // Dependency section
      .dependency-section {
        .dependency-loading {
          .loading-content {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 16px;
            background-color: var(--mat-surface-variant);
            border: 1px solid var(--mat-divider-color);
            border-radius: 8px;

            mat-spinner {
              flex-shrink: 0;
            }

            .loading-text {
              display: flex;
              flex-direction: column;
              gap: 4px;

              .loading-title {
                font-weight: 500;
                font-size: 0.8125rem;
                color: var(--mat-text-primary);
              }

              .loading-subtitle {
                font-size: 0.75rem;
                color: var(--mat-text-secondary);
              }
            }
          }
        }

        .dependency-results {
          display: flex;
          flex-direction: column;
          gap: 16px;

          .dependency-summary {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            border-radius: 4px;
            border-left: 4px solid;

            &.dependency-safe {
              background-color: var(--mat-success-container);
              border-color: var(--mat-success);
              color: var(--mat-on-success-container);
            }

            &.dependency-warning {
              background-color: var(--mat-warn-container);
              border-color: var(--mat-warn);
              color: var(--mat-on-warn-container);
            }

            &.dependency-blocking {
              background-color: var(--mat-error-container);
              border-color: var(--mat-error);
              color: var(--mat-on-error-container);
            }

            mat-icon {
              font-size: 18px;
              width: 18px;
              height: 18px;
            }

            .summary-content {
              display: flex;
              flex-direction: column;
              gap: 4px;

              .summary-title {
                font-weight: 500;
                font-size: 0.8125rem;
              }

              .summary-message {
                font-size: 0.75rem;
                opacity: 0.8;
              }
            }
          }

          // Subtle expansion panels
          .modern-expansion-panel {
            border-radius: 4px;
            border: 1px solid var(--mat-divider-color);

            &.warnings-panel {
              .mat-expansion-panel-header {
                background-color: var(--mat-warn-container);
              }
            }

            &.blocking-panel {
              .mat-expansion-panel-header {
                background-color: var(--mat-error-container);
              }
            }

            .mat-expansion-panel-header {
              padding: 12px 16px;

              .panel-title-content {
                display: flex;
                align-items: center;
                gap: 8px;
                font-weight: 500;

                mat-icon {
                  font-size: 16px;
                  width: 16px;
                  height: 16px;
                }
              }
            }

            .panel-content {
              padding: 12px 16px;

              .warning-list,
              .blocking-list {
                display: flex;
                flex-direction: column;
                gap: 8px;

                .warning-item,
                .blocking-item {
                  display: flex;
                  align-items: center;
                  gap: 8px;
                  padding: 6px 8px;
                  background-color: var(--mat-surface-variant);
                  border-radius: 4px;

                  mat-icon {
                    font-size: 14px;
                    width: 14px;
                    height: 14px;
                  }

                  span {
                    font-size: 0.8125rem;
                  }
                }
              }
        
              .close-button {
                color: var(--mat-text-secondary);
                
                &:hover:not(:disabled) {
                  color: var(--mat-text-primary);
                  background-color: var(--mat-action-hover);
                }
        
                &:disabled {
                  opacity: 0.5;
                  cursor: not-allowed;
                }
              }
            }
          }

          // Force delete section
          .force-delete-section {
            display: flex;
            flex-direction: column;
            gap: 16px;

            .force-delete-checkbox {
              padding: 12px 16px;
              background-color: var(--mat-error-container);
              border-left: 4px solid var(--mat-error);
              border-radius: 4px;

              .checkbox-label {
                font-weight: 500;
                color: var(--mat-on-error-container);
              }
            }

            .force-delete-warning {
              .danger-alert {
                padding: 16px;
                background-color: var(--mat-error-container);
                border-left: 4px solid var(--mat-error);
                border-radius: 4px;

                mat-icon {
                  font-size: 20px;
                  width: 20px;
                  height: 20px;
                  color: var(--mat-error);
                  margin-bottom: 8px;
                }

                .danger-content {
                  display: flex;
                  flex-direction: column;
                  gap: 12px;

                  .danger-title {
                    font-weight: 600;
                    font-size: 0.875rem;
                    color: var(--mat-on-error-container);
                  }

                  .danger-message {
                    margin: 0;
                    font-size: 0.8125rem;
                    color: var(--mat-on-error-container);
                    line-height: 1.4;
                  }

                  .confirmation-checkbox {
                    margin-top: 8px;

                    .confirm-label {
                      font-weight: 500;
                      color: var(--mat-on-error-container);
                    }
                  }
                }
              }
            }
          }
        }
      }

      // Dependencies warning - more subtle
      .dependencies-warning {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 12px 16px;
        background-color: var(--mat-error-container);
        border-left: 4px solid var(--mat-error);
        border-radius: 4px;

        mat-icon {
          font-size: 18px;
          width: 18px;
          height: 18px;
          margin-top: 2px;
          color: var(--mat-error);
        }

        .dependencies-content {
          display: flex;
          flex-direction: column;
          gap: 4px;

          .dependencies-title {
            font-weight: 500;
            font-size: 0.8125rem;
            color: var(--mat-on-error-container);
          }

          .dependencies-message {
            font-size: 0.75rem;
            color: var(--mat-on-error-container);
            opacity: 0.8;
          }
        }
      }
    }
  }

  // Subtle dialog actions similar to normal dialogs
  .dialog-actions {
    padding: 16px 24px;
    border-top: 1px solid var(--mat-divider-color);
    background-color: var(--mat-surface);

    .actions-content {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      align-items: center;

      .cancel-button {
        min-width: 100px;
        height: 36px;
        font-weight: 500;
        color: var(--mat-text-secondary);

        &:hover:not(:disabled) {
          background-color: var(--mat-action-hover);
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        mat-icon {
          font-size: 18px;
          width: 18px;
          height: 18px;
        }
      }

      .delete-button {
        min-width: 120px;
        height: 36px;
        font-weight: 500;

        &:disabled {
          opacity: 0.5;
        }

        .button-content {
          display: flex;
          align-items: center;
          gap: 8px;

          mat-spinner {
            margin: 0;
          }

          mat-icon {
            font-size: 18px;
            width: 18px;
            height: 18px;
          }

          span {
            font-size: 0.875rem;
          }
        }
      }
    }
  }
}

// Removed dramatic animations for subtlety

// Responsive design
@media (max-width: 768px) {
  .modern-delete-dialog {
    .dialog-header.warning-header {
      padding: 20px 24px;
      margin: -24px -24px 0 -24px;

      .header-content {
        gap: 12px;

        .header-icon.warning-icon {
          width: 40px;
          height: 40px;

          mat-icon {
            font-size: 24px;
            width: 24px;
            height: 24px;
          }
        }

        .header-text {
          h2 {
            font-size: 1.25rem;
          }

          .header-subtitle {
            font-size: 0.8rem;
          }
        }
      }
    }

    .dialog-content {
      padding: 24px 24px 20px 24px;
      min-width: 320px;
      max-width: 100%;

      .delete-content {
        gap: 20px;

        .confirmation-section {
          padding: 16px;
          gap: 12px;

          .confirmation-icon {
            width: 32px;
            height: 32px;

            mat-icon {
              font-size: 16px;
              width: 16px;
              height: 16px;
            }
          }

          .confirmation-message {
            font-size: 0.875rem;
            margin-top: 4px;
          }
        }
      }
    }

    .dialog-actions {
      padding: 0 24px 24px 24px;

      .actions-content {
        flex-direction: column-reverse;
        gap: 12px;

        .cancel-button,
        .delete-button {
          width: 100%;
          justify-content: center;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .modern-delete-dialog {
    .dialog-content {
      padding: 20px 20px 16px 20px;
    }

    .dialog-actions {
      padding: 0 20px 20px 20px;
    }
  }
}