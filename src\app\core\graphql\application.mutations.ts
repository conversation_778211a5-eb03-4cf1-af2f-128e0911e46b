import { gql } from 'apollo-angular';

export const CREATE_APPLICATION = gql`
  mutation CreateApplication($input: CreateApplicationInput!) {
    createApplication(input: $input) {
      identifier
      name
      description
      isDeleted
      createdAt
      updatedAt
    }
  }
`;

export const UPDATE_APPLICATION = gql`
  mutation UpdateApplication($identifier: UUID!, $name: String, $description: String, $isDeleted: Boolean) {
    updateApplication(identifier: $identifier, name: $name, description: $description, isDeleted: $isDeleted) {
      identifier
      name
      description
      isDeleted
      createdAt
      updatedAt
    }
  }
`;

export const DELETE_APPLICATION = gql`
  mutation DeleteApplication($identifier: UUID!) {
    deleteApplication(identifier: $identifier) {
      identifier
      name
      description
      isDeleted
      createdAt
      updatedAt
    }
  }
`;