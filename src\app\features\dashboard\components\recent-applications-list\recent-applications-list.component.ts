import { Component, Input, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { Application } from '../../../../core/models/application.model';

@Component({
  selector: 'app-recent-applications-list',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatListModule, MatIconModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <mat-card class="applications-card">
      <mat-card-header>
        <mat-card-title>Kürzlich hinzugefügte Anwendungen</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div *ngIf="applications.length === 0" class="empty-state">
          <mat-icon>apps</mat-icon>
          <p>Keine <PERSON>wendungen registriert</p>
        </div>
        <mat-list *ngIf="applications.length > 0">
          <mat-list-item *ngFor="let app of applications; trackBy: trackByApplicationId">
            <mat-icon matListItemIcon class="app-icon">apps</mat-icon>
            <div matListItemTitle>{{ app.name }}</div>
            <div matListItemLine>
              {{ app.description || 'Keine Beschreibung' }}
            </div>
            <div matListItemLine class="app-date">
              Hinzugefügt: {{ app.createdAt | date:'short' }}
            </div>
          </mat-list-item>
        </mat-list>
      </mat-card-content>
    </mat-card>
  `,
  styles: [`
    .applications-card {
      border: 1px solid var(--border-color);
      box-shadow: none;
      border-radius: var(--border-radius-md);
      background-color: var(--funk-white);
    }

    .applications-card mat-card-title {
      font-size: 1rem;
      font-weight: 600;
      color: var(--funk-blue);
      margin: 0;
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 2rem;
      color: var(--text-secondary);
      text-align: center;
    }

    .empty-state mat-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
      margin-bottom: 1rem;
      opacity: 0.5;
      color: var(--info-color);
    }

    .empty-state p {
      margin: 0;
      font-size: 0.875rem;
    }

    mat-list {
      padding: 0;
    }

    mat-list-item {
      border-bottom: 1px solid var(--border-color);
    }

    mat-list-item:last-child {
      border-bottom: none;
    }

    .app-icon {
      font-size: 1.25rem;
      width: 1.25rem;
      height: 1.25rem;
      color: var(--funk-blue);
    }

    .app-date {
      font-size: 0.75rem;
      color: var(--text-secondary);
      margin-top: 0.25rem;
    }

    [matListItemTitle] {
      font-weight: 500;
      color: var(--text-primary);
    }

    [matListItemLine] {
      color: var(--text-secondary);
      font-size: 0.875rem;
    }

    [matListItemLine].app-date {
      font-size: 0.75rem;
    }
  `]
})
export class RecentApplicationsListComponent {
  @Input() applications: Application[] = [];

  trackByApplicationId(index: number, application: Application): string {
    return application.identifier;
  }
}