import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatBadgeModule } from '@angular/material/badge';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDividerModule } from '@angular/material/divider';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { Store } from '@ngrx/store';
import { Observable, of, startWith, map, switchMap } from 'rxjs';

import { User } from '../../../../core/models/user.model';
import { NotificationBadge } from '../../../../core/models/notification.model';
import { SearchResult, SearchService } from '../../../../core/services/search.service';
import { NotificationService } from '../../../../core/services/notification.service';
import { AuthFacade } from '../../../../store/auth/auth.facade';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatToolbarModule,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    MatBadgeModule,
    MatAutocompleteModule,
    MatInputModule,
    MatFormFieldModule,
    MatDividerModule
  ],
  template: `
    <!-- Simplified header for development -->
    <mat-toolbar class="header-toolbar">
      <!-- Logo/Brand Section -->
      <div class="brand-section">
        <button mat-icon-button (click)="toggleSidebar()" class="menu-button">
          <mat-icon>menu</mat-icon>
        </button>
        <div class="logo-placeholder">SB</div>
        <span class="brand-text">StörungsBuddy</span>
      </div>

      <div class="spacer"></div>

      <!-- User Section -->
      <div class="user-section" *ngIf="isAuthenticated$ | async">
        <button mat-button [matMenuTriggerFor]="userMenu" class="user-button">
          <div class="user-avatar">
            <div class="avatar-initials">{{ (userInitials$ | async) || 'U' }}</div>
          </div>
          <div class="user-info">
            <div class="user-name">{{ (userFullName$ | async) || 'User' }}</div>
            <div class="user-role">{{ (currentUser$ | async)?.role || 'User' }}</div>
          </div>
          <mat-icon>arrow_drop_down</mat-icon>
        </button>
        
        <mat-menu #userMenu="matMenu">
          <button mat-menu-item>
            <mat-icon>person</mat-icon>
            <span>Profile</span>
          </button>
          <button mat-menu-item>
            <mat-icon>settings</mat-icon>
            <span>Settings</span>
          </button>
          <mat-divider></mat-divider>
          <button mat-menu-item (click)="logout()">
            <mat-icon>logout</mat-icon>
            <span>Logout</span>
          </button>
        </mat-menu>
      </div>
    </mat-toolbar>
  `,
  styles: [`
    .header-toolbar {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 0 1.5rem;
      background-color: var(--funk-blue) !important;
      color: var(--funk-white) !important;
      border-bottom: none;
      box-shadow: none;
      z-index: 1000;
      height: 64px;
    }

    .brand-section {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      min-width: 200px;
    }

    .menu-button {
      color: var(--funk-white) !important;
    }

    .logo-placeholder {
      height: 32px;
      width: 32px;
      background-color: var(--funk-white);
      border-radius: var(--border-radius-sm);
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 0.875rem;
      color: var(--funk-blue);
    }

    .brand-text {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--funk-white);
      letter-spacing: -0.025em;
    }

    .spacer {
      flex: 1;
    }

    .user-section {
      display: flex;
      align-items: center;
    }

    .user-button {
      display: flex !important;
      align-items: center;
      gap: 0.75rem;
      color: var(--funk-white) !important;
      padding: 0.5rem 0.75rem !important;
      border-radius: var(--border-radius-sm) !important;
      transition: background-color 0.15s ease-in-out;
    }

    .user-button:hover {
      background-color: var(--funk-light-blue) !important;
    }

    .user-avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--funk-white);
    }

    .avatar-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .avatar-initials {
      font-size: 0.875rem;
      font-weight: 500;
      color: var(--funk-blue);
    }

    .user-info {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      text-align: left;
    }

    .user-name {
      font-size: 0.875rem;
      font-weight: 500;
      line-height: 1.2;
      color: var(--funk-white);
    }

    .user-role {
      font-size: 0.75rem;
      color: var(--funk-light-blue);
      line-height: 1.2;
    }

    @media (max-width: 768px) {
      .header-toolbar {
        padding: 0 1rem;
      }

      .user-info {
        display: none;
      }

      .brand-text {
        display: none;
      }
    }
  `]
})
export class HeaderComponent implements OnInit {
  private authFacade = inject(AuthFacade);
  private searchService = inject(SearchService);
  private notificationService = inject(NotificationService);

  searchControl = new FormControl('');
  
  currentUser$ = this.authFacade.currentUser$;
  userFullName$ = this.authFacade.userFullName$;
  userInitials$ = this.authFacade.userInitials$;
  isAuthenticated$ = this.authFacade.isAuthenticated$;
  
  notificationBadge$ = this.notificationService.badge$;
  recentNotifications$ = this.notificationService.getNotifications({ isArchived: false });
  
  filteredSearchResults$: Observable<SearchResult[]> = new Observable();

  ngOnInit() {
    this.filteredSearchResults$ = this.searchControl.valueChanges.pipe(
      startWith(''),
      map(value => value || ''),
      switchMap((query: string) => query.length >= 2 ? this.searchService.search(query) : of([]))
    );
  }

  toggleSidebar() {
    // Emit event to parent component to toggle sidebar
    // This will be handled by the main layout component
    const event = new CustomEvent('toggleSidebar');
    window.dispatchEvent(event);
  }

  onSearchResultSelected(event: any) {
    // Navigation will be handled by routerLink in template
    this.searchControl.setValue('');
  }

  getSearchResultIcon(type: string): string {
    const icons: Record<string, string> = {
      system: 'computer',
      user: 'person',
      maintenance: 'build',
      documentation: 'description',
      setting: 'settings'
    };
    return icons[type] || 'search';
  }

  getNotificationIcon(type: string): string {
    const icons: Record<string, string> = {
      error: 'error',
      warning: 'warning',
      success: 'check_circle',
      info: 'info',
      system: 'computer',
      maintenance: 'build'
    };
    return icons[type] || 'notifications';
  }

  markAllNotificationsAsRead() {
    this.notificationService.markAllAsRead().subscribe();
  }

  formatTime(date: Date): string {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `vor ${days} Tag${days > 1 ? 'en' : ''}`;
    if (hours > 0) return `vor ${hours} Stunde${hours > 1 ? 'n' : ''}`;
    if (minutes > 0) return `vor ${minutes} Minute${minutes > 1 ? 'n' : ''}`;
    return 'gerade eben';
  }

  logout() {
    this.authFacade.logout();
  }
}