// Modern Button Styles for StoerungsBuddy Frontend
// Enhanced Material Design buttons with contemporary styling

// Modern Button Variables
:root {
  // Button specific colors
  --button-primary-bg: linear-gradient(135deg, var(--funk-blue) 0%, #1565c0 100%);
  --button-primary-hover: linear-gradient(135deg, #1565c0 0%, var(--funk-blue) 100%);
  --button-primary-active: var(--funk-gray);
  
  --button-secondary-bg: linear-gradient(135deg, var(--funk-light-blue) 0%, #7ba3c0 100%);
  --button-secondary-hover: linear-gradient(135deg, #7ba3c0 0%, var(--funk-light-blue) 100%);
  
  --button-success-bg: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  --button-success-hover: linear-gradient(135deg, #20c997 0%, #28a745 100%);
  
  --button-warning-bg: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
  --button-warning-hover: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%);
  
  --button-danger-bg: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
  --button-danger-hover: linear-gradient(135deg, #e74c3c 0%, #dc3545 100%);
  
  // Modern shadows
  --button-shadow: 0 2px 8px rgba(var(--funk-blue-rgb), 0.15);
  --button-shadow-hover: 0 4px 16px rgba(var(--funk-blue-rgb), 0.25);
  --button-shadow-active: 0 1px 4px rgba(var(--funk-blue-rgb), 0.2);
  
  // Modern transitions
  --button-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --button-transform-hover: translateY(-1px);
  --button-transform-active: translateY(0px);
}

// Base Modern Button Styles
.modern-button-base {
  position: relative;
  overflow: hidden;
  border-radius: 8px !important;
  font-weight: 600 !important;
  text-transform: none !important;
  letter-spacing: 0.025em !important;
  transition: var(--button-transition) !important;
  box-shadow: var(--button-shadow) !important;
  border: none !important;
  
  // Modern ripple effect
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
    pointer-events: none;
  }
  
  &:hover {
    transform: var(--button-transform-hover) !important;
    box-shadow: var(--button-shadow-hover) !important;
    
    &::before {
      transform: translateX(100%);
    }
  }
  
  &:active {
    transform: var(--button-transform-active) !important;
    box-shadow: var(--button-shadow-active) !important;
  }
  
  &:disabled {
    transform: none !important;
    box-shadow: none !important;
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    
    &::before {
      display: none;
    }
  }
  
  // Icon spacing
  mat-icon {
    margin-right: 8px !important;
    font-size: 18px !important;
    width: 18px !important;
    height: 18px !important;
  }
  
  &.icon-only mat-icon {
    margin: 0 !important;
  }
}

// Primary Button (Raised/Flat)
.mat-mdc-raised-button,
.mat-mdc-flat-button {
  @extend .modern-button-base;
  
  &.mat-primary {
    background: var(--button-primary-bg) !important;
    color: white !important;
    
    &:hover:not(:disabled) {
      background: var(--button-primary-hover) !important;
    }
    
    &:active:not(:disabled) {
      background: var(--button-primary-active) !important;
    }
  }
  
  &.mat-accent {
    background: var(--button-secondary-bg) !important;
    color: white !important;
    
    &:hover:not(:disabled) {
      background: var(--button-secondary-hover) !important;
    }
  }
  
  &.mat-warn {
    background: var(--button-danger-bg) !important;
    color: white !important;
    
    &:hover:not(:disabled) {
      background: var(--button-danger-hover) !important;
    }
  }
  
  // Success variant
  &.success {
    background: var(--button-success-bg) !important;
    color: white !important;
    
    &:hover:not(:disabled) {
      background: var(--button-success-hover) !important;
    }
  }
  
  // Warning variant
  &.warning {
    background: var(--button-warning-bg) !important;
    color: var(--funk-blue) !important;
    
    &:hover:not(:disabled) {
      background: var(--button-warning-hover) !important;
    }
  }
}

// Outlined/Stroked Button
.mat-mdc-outlined-button,
.mat-mdc-stroked-button {
  @extend .modern-button-base;
  border: 2px solid var(--funk-blue) !important;
  background: transparent !important;
  color: var(--funk-blue) !important;
  box-shadow: none !important;
  
  &:hover:not(:disabled) {
    background: var(--funk-blue) !important;
    color: white !important;
    border-color: var(--funk-blue) !important;
    box-shadow: var(--button-shadow-hover) !important;
  }
  
  &.mat-accent {
    border-color: var(--funk-light-blue) !important;
    color: var(--funk-light-blue) !important;
    
    &:hover:not(:disabled) {
      background: var(--funk-light-blue) !important;
      color: white !important;
    }
  }
  
  &.mat-warn {
    border-color: #dc3545 !important;
    color: #dc3545 !important;
    
    &:hover:not(:disabled) {
      background: #dc3545 !important;
      color: white !important;
    }
  }
}

// Text Button
.mat-mdc-button {
  @extend .modern-button-base;
  background: transparent !important;
  box-shadow: none !important;
  color: var(--funk-blue) !important;
  border-radius: 6px !important;
  
  &:hover:not(:disabled) {
    background: rgba(var(--funk-blue-rgb), 0.08) !important;
    transform: none !important;
    box-shadow: none !important;
  }
  
  &:active:not(:disabled) {
    background: rgba(var(--funk-blue-rgb), 0.12) !important;
  }
  
  &.mat-accent {
    color: var(--funk-light-blue) !important;
    
    &:hover:not(:disabled) {
      background: rgba(var(--funk-light-blue-rgb), 0.08) !important;
    }
  }
  
  &.mat-warn {
    color: #dc3545 !important;
    
    &:hover:not(:disabled) {
      background: rgba(220, 53, 69, 0.08) !important;
    }
  }
}

// Icon Button
.mat-mdc-icon-button {
  @extend .modern-button-base;
  border-radius: 50% !important;
  width: 40px !important;
  height: 40px !important;
  background: transparent !important;
  box-shadow: none !important;
  
  &:hover:not(:disabled) {
    background: rgba(var(--funk-blue-rgb), 0.08) !important;
    transform: scale(1.05) !important;
    box-shadow: var(--button-shadow) !important;
  }
  
  &:active:not(:disabled) {
    transform: scale(0.95) !important;
  }
  
  mat-icon {
    margin: 0 !important;
    font-size: 20px !important;
    width: 20px !important;
    height: 20px !important;
  }
  
  &.mat-primary {
    color: var(--funk-blue) !important;
    
    &:hover:not(:disabled) {
      background: rgba(var(--funk-blue-rgb), 0.1) !important;
    }
  }
  
  &.mat-warn {
    color: #dc3545 !important;
    
    &:hover:not(:disabled) {
      background: rgba(220, 53, 69, 0.1) !important;
    }
  }
}

// FAB (Floating Action Button)
.mat-mdc-fab,
.mat-mdc-mini-fab {
  @extend .modern-button-base;
  background: var(--button-primary-bg) !important;
  color: white !important;
  box-shadow: 0 4px 16px rgba(var(--funk-blue-rgb), 0.3) !important;
  
  &:hover:not(:disabled) {
    background: var(--button-primary-hover) !important;
    box-shadow: 0 6px 24px rgba(var(--funk-blue-rgb), 0.4) !important;
    transform: translateY(-2px) scale(1.05) !important;
  }
  
  &:active:not(:disabled) {
    transform: translateY(0px) scale(1) !important;
  }
  
  mat-icon {
    margin: 0 !important;
  }
}

// Button Groups
.button-group {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
  
  &.vertical {
    flex-direction: column;
    align-items: stretch;
  }
  
  &.compact {
    gap: 8px;
  }
}

// Special Button Variants
.modern-button-variants {
  // Glass morphism effect
  &.glass {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: var(--funk-blue) !important;
    
    &:hover:not(:disabled) {
      background: rgba(255, 255, 255, 0.2) !important;
    }
  }
  
  // Gradient border effect
  &.gradient-border {
    position: relative;
    background: white !important;
    color: var(--funk-blue) !important;
    
    &::after {
      content: '';
      position: absolute;
      inset: 0;
      padding: 2px;
      background: var(--button-primary-bg);
      border-radius: inherit;
      mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
      mask-composite: xor;
      -webkit-mask-composite: xor;
    }
    
    &:hover:not(:disabled) {
      color: white !important;
      background: var(--button-primary-bg) !important;
      
      &::after {
        display: none;
      }
    }
  }
  
  // Pulse animation for important actions
  &.pulse {
    animation: pulse 2s infinite;
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--funk-blue-rgb), 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(var(--funk-blue-rgb), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--funk-blue-rgb), 0);
  }
}

// Loading state
.button-loading {
  position: relative;
  pointer-events: none;
  
  .button-content {
    opacity: 0.6;
  }
  
  mat-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

// Size variants
.button-size-small {
  height: 32px !important;
  padding: 0 16px !important;
  font-size: 0.8125rem !important;
  
  mat-icon {
    font-size: 16px !important;
    width: 16px !important;
    height: 16px !important;
  }
}

.button-size-large {
  height: 48px !important;
  padding: 0 32px !important;
  font-size: 1rem !important;
  
  mat-icon {
    font-size: 20px !important;
    width: 20px !important;
    height: 20px !important;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .modern-button-base {
    min-height: 44px !important; // Better touch targets
    
    &:hover {
      transform: none !important; // Disable hover effects on touch devices
    }
  }
  
  .button-group {
    &:not(.vertical) {
      flex-direction: column;
      align-items: stretch;
    }
  }
}

// Dark mode support (if needed in future)
@media (prefers-color-scheme: dark) {
  :root {
    --button-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    --button-shadow-hover: 0 4px 16px rgba(0, 0, 0, 0.4);
  }
}