import { Component, Inject, OnInit, OnD<PERSON>roy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil } from 'rxjs';

// Angular Material
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

// Models and Components
import { CreateIncidentInput } from '../../../../core/models/incident.model';
import { IncidentsFacade } from '../../../../store/incidents/incidents.facade';
import { IncidentCreateFormComponent } from '../incident-create-form/incident-create-form.component';

export interface IncidentCreateDialogData {
  title?: string;
}

@Component({
  selector: 'app-incident-create-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    IncidentCreateFormComponent
  ],
  templateUrl: './incident-create-dialog.component.html',
  styleUrls: ['./incident-create-dialog.component.scss']
})
export class IncidentCreateDialogComponent implements OnInit, OnDestroy {
  loading = false;
  error: string | null = null;
  private wasCreating = false;
  
  private destroy$ = new Subject<void>();
  private incidentsFacade = inject(IncidentsFacade);
  private snackBar = inject(MatSnackBar);

  constructor(
    public dialogRef: MatDialogRef<IncidentCreateDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: IncidentCreateDialogData
  ) {
    // Configure dialog
    this.dialogRef.disableClose = true;
  }

  ngOnInit(): void {
    // Subscribe to loading state
    this.incidentsFacade.isCreating$
      .pipe(takeUntil(this.destroy$))
      .subscribe(isCreating => {
        if (this.loading && !isCreating) {
          // We were creating and now we're not - mark for success check
          this.wasCreating = true;
        }
        this.loading = isCreating;
      });

    // Subscribe to create error
    this.incidentsFacade.createError$
      .pipe(takeUntil(this.destroy$))
      .subscribe(error => {
        this.error = error;
        if (error) {
          this.snackBar.open(
            `Fehler beim Erstellen: ${error}`,
            'Schließen',
            { 
              duration: 5000,
              panelClass: ['error-snackbar']
            }
          );
        }
      });

    // Subscribe to successful creation
    this.incidentsFacade.allIncidents$
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        // Check if we just finished creating (was loading, now not loading, no error)
        if (!this.loading && !this.error && this.wasCreating) {
          this.onCreateSuccess();
          this.wasCreating = false;
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onFormSubmit(incidentData: CreateIncidentInput): void {
    if (this.loading) {
      return;
    }

    this.error = null;
    this.incidentsFacade.createIncident(incidentData);
  }

  onCancel(): void {
    if (this.loading) {
      return;
    }
    
    this.dialogRef.close({ success: false });
  }

  private onCreateSuccess(): void {
    this.snackBar.open(
      'Vorfall erfolgreich erstellt',
      'Schließen',
      { 
        duration: 3000,
        panelClass: ['success-snackbar']
      }
    );

    this.dialogRef.close({ success: true });
  }

  get dialogTitle(): string {
    return this.data?.title || 'Neuen Vorfall erstellen';
  }
}