<mat-card class="filter-card">
  <mat-card-content>
    <form [formGroup]="filterForm" class="filter-form">
      <div class="filter-row">
        <!-- Search Field -->
        <mat-form-field appearance="outline" class="search-field">
          <mat-label>Suche nach Name oder Beschreibung</mat-label>
          <input matInput
                 formControlName="searchTerm"
                 placeholder="Suchbegriff eingeben..."
                 autocomplete="off"
                 (keydown.escape)="clearSearch()"
                 #searchInput>
          <button matSuffix
                  mat-icon-button
                  *ngIf="filterForm.get('searchTerm')?.value; else searchIcon"
                  (click)="clearSearch()"
                  type="button"
                  matTooltip="Suche löschen">
            <mat-icon>clear</mat-icon>
          </button>
          <ng-template #searchIcon>
            <mat-icon matSuffix>search</mat-icon>
          </ng-template>
          <mat-hint>Suche in Name und Beschreibung</mat-hint>
        </mat-form-field>

        <!-- Status Filter -->
        <mat-form-field appearance="outline" class="status-field">
          <mat-label>Status Filter</mat-label>
          <mat-select formControlName="statusFilter" (selectionChange)="onStatusFilterChange($event)">
            <mat-option value="active">
              <mat-icon>check_circle</mat-icon>
              Nur aktive Applikationen
            </mat-option>
            <mat-option value="deleted">
              <mat-icon>delete</mat-icon>
              Nur gelöschte Applikationen
            </mat-option>
            <mat-option value="all">
              <mat-icon>list</mat-icon>
              Alle Applikationen
            </mat-option>
          </mat-select>
          <mat-hint>Aktuell: {{ filterForm.get('statusFilter')?.value }}</mat-hint>
        </mat-form-field>

        <!-- Clear Button -->
        <button mat-stroked-button 
                type="button"
                (click)="clearFilters()"
                [disabled]="!hasActiveFilters()"
                class="clear-button">
          <mat-icon>clear</mat-icon>
          Filter zurücksetzen
        </button>
      </div>
    </form>
  </mat-card-content>
</mat-card>