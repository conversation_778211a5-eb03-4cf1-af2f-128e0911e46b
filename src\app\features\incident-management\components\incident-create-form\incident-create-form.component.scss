.incident-create-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
  max-width: 100%;

  .form-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .full-width {
    width: 100%;
  }

  .date-time-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }

    .datetime-field {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;

      .field-label {
        font-size: 0.875rem;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.87);
        margin-bottom: 0.25rem;

        &::after {
          content: ' *';
          color: #f44336;
        }

        &.optional::after {
          content: '';
        }
      }

      .error-message {
        font-size: 0.75rem;
        color: #f44336;
        margin-top: 0.25rem;
        margin-left: 0.75rem;
        line-height: 1.2;
      }

      app-datetime-picker {
        width: 100%;
      }
    }

    .date-field {
      width: 100%;
    }
  }

  .applications-section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    .section-label {
      font-size: 0.875rem;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.87);
      margin-bottom: 0.25rem;

      .mat-mdc-form-field-subscript-wrapper {
        display: none;
      }
    }

    .error-message {
      font-size: 0.75rem;
      color: #f44336;
      margin-top: 0.25rem;
      margin-left: 1rem;
    }
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(0, 0, 0, 0.12);

    @media (max-width: 768px) {
      flex-direction: column-reverse;
      gap: 0.75rem;

      .cancel-button,
      .submit-button {
        width: 100%;
      }
    }

    .cancel-button {
      color: rgba(0, 0, 0, 0.54);

      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }
    }

    .submit-button {
      min-width: 140px;

      &:disabled {
        background-color: rgba(0, 0, 0, 0.12);
        color: rgba(0, 0, 0, 0.26);
      }
    }
  }

  // Material Design form field customizations
  ::ng-deep {
    .mat-mdc-form-field {
      .mat-mdc-form-field-subscript-wrapper {
        .mat-mdc-form-field-hint-wrapper,
        .mat-mdc-form-field-error-wrapper {
          padding: 0 1rem;
        }
      }

      &.mat-form-field-invalid {
        .mat-mdc-form-field-outline-thick {
          color: #f44336;
        }
      }
    }

    .mat-mdc-select-panel {
      max-height: 300px;
    }

    .mat-datepicker-popup {
      .mat-calendar {
        width: 320px;
      }
    }
  }

  // Dark theme support
  @media (prefers-color-scheme: dark) {
    .section-label {
      color: rgba(255, 255, 255, 0.87);
    }

    .form-actions {
      border-top-color: rgba(255, 255, 255, 0.12);
    }

    .cancel-button {
      color: rgba(255, 255, 255, 0.54);

      &:hover {
        background-color: rgba(255, 255, 255, 0.04);
      }
    }
  }

  // Focus management
  .mat-mdc-form-field-focus-overlay {
    background-color: transparent;
  }

  // Accessibility improvements
  .mat-mdc-form-field-error {
    font-size: 0.75rem;
    line-height: 1.2;
  }

  // Loading state (for future use)
  &.loading {
    pointer-events: none;
    opacity: 0.6;

    .form-actions button {
      cursor: not-allowed;
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .incident-create-form {
    .form-actions {
      border-top: 2px solid;
    }

    .error-message {
      font-weight: bold;
    }
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .incident-create-form {
    * {
      transition: none !important;
      animation: none !important;
    }
  }
}