import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { of } from 'rxjs';

import { DashboardComponent } from './dashboard.component';
import { IncidentsFacade } from '../../store/incidents/incidents.facade';
import { ApplicationsFacade } from '../../store/applications/applications.facade';
import { IncidentType } from '../../core/models/incident.model';

describe('DashboardComponent', () => {
  let component: DashboardComponent;
  let fixture: ComponentFixture<DashboardComponent>;
  let mockIncidentsFacade: jasmine.SpyObj<IncidentsFacade>;
  let mockApplicationsFacade: jasmine.SpyObj<ApplicationsFacade>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockDialog: jasmine.SpyObj<MatDialog>;
  let mockSnackBar: jasmine.SpyObj<MatSnackBar>;

  const mockIncidents = [
    {
      identifier: '1',
      title: 'Test Incident',
      type: IncidentType.STOERUNG,
      description: 'Test description',
      startTime: '2024-01-01T10:00:00Z',
      isResolved: false,
      createdAt: '2024-01-01T10:00:00Z',
      updatedAt: '2024-01-01T10:00:00Z',
      applications: []
    }
  ];

  const mockApplications = [
    {
      identifier: '1',
      name: 'Test App',
      description: 'Test description',
      isDeleted: false,
      createdAt: '2024-01-01T10:00:00Z',
      updatedAt: '2024-01-01T10:00:00Z'
    }
  ];

  beforeEach(async () => {
    const incidentsFacadeSpy = jasmine.createSpyObj('IncidentsFacade', [
      'loadIncidents',
      'clearIncidentsError'
    ], {
      allIncidents$: of(mockIncidents),
      isLoading$: of(false),
      error$: of(null)
    });

    const applicationsFacadeSpy = jasmine.createSpyObj('ApplicationsFacade', [
      'loadApplications'
    ], {
      applications$: of(mockApplications),
      loading$: of(false),
      error$: of(null)
    });

    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const dialogSpy = jasmine.createSpyObj('MatDialog', ['open']);
    const snackBarSpy = jasmine.createSpyObj('MatSnackBar', ['open']);

    // Mock dialog return value
    const mockDialogRef = {
      afterClosed: () => of({ success: true })
    };
    dialogSpy.open.and.returnValue(mockDialogRef);

    await TestBed.configureTestingModule({
      imports: [DashboardComponent, NoopAnimationsModule],
      providers: [
        { provide: IncidentsFacade, useValue: incidentsFacadeSpy },
        { provide: ApplicationsFacade, useValue: applicationsFacadeSpy },
        { provide: Router, useValue: routerSpy },
        { provide: MatDialog, useValue: dialogSpy },
        { provide: MatSnackBar, useValue: snackBarSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(DashboardComponent);
    component = fixture.componentInstance;
    mockIncidentsFacade = TestBed.inject(IncidentsFacade) as jasmine.SpyObj<IncidentsFacade>;
    mockApplicationsFacade = TestBed.inject(ApplicationsFacade) as jasmine.SpyObj<ApplicationsFacade>;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockDialog = TestBed.inject(MatDialog) as jasmine.SpyObj<MatDialog>;
    mockSnackBar = TestBed.inject(MatSnackBar) as jasmine.SpyObj<MatSnackBar>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load data on init', () => {
    component.ngOnInit();
    
    expect(mockIncidentsFacade.loadIncidents).toHaveBeenCalled();
    expect(mockApplicationsFacade.loadApplications).toHaveBeenCalledWith({ isDeleted: false });
  });

  it('should refresh data when refreshData is called', () => {
    component.refreshData();
    
    expect(mockIncidentsFacade.clearIncidentsError).toHaveBeenCalled();
    expect(mockIncidentsFacade.loadIncidents).toHaveBeenCalled();
    expect(mockApplicationsFacade.loadApplications).toHaveBeenCalledWith({ isDeleted: false });
  });

  it('should have createIncident method that calls dialog service', () => {
    expect(component.createIncident).toBeDefined();
    expect(typeof component.createIncident).toBe('function');
  });

  it('should have createApplication method that calls dialog service', () => {
    expect(component.createApplication).toBeDefined();
    expect(typeof component.createApplication).toBe('function');
  });

  it('should navigate to incidents list when viewIncidents is called', () => {
    component.viewIncidents();
    
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/incidents']);
  });
});