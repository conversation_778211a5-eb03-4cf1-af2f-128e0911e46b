import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { provideMockStore } from '@ngrx/store/testing';
import { ApplicationEditDialogComponent } from './application-edit-dialog.component';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { ApplicationsFacade } from '../../../../store/applications/applications.facade';
import { Actions } from '@ngrx/effects';
import { MatSnackBar } from '@angular/material/snack-bar';
import { of, Subject } from 'rxjs';
import * as ApplicationActions from '../../../../store/applications/applications.actions';
import { Application, UpdateApplicationInput } from '../../../../core/models/application.model';

describe('ApplicationEditDialogComponent', () => {
  let component: ApplicationEditDialogComponent;
  let fixture: ComponentFixture<ApplicationEditDialogComponent>;
  let mockDialogRef: MatDialogRef<ApplicationEditDialogComponent>;
  let mockApplicationsFacade: Partial<ApplicationsFacade>;
  let mockActions: Actions;
  let mockSnackBar: Partial<MatSnackBar>;
  let actions$: Subject<any>;
  const mockApplication: Application = {
    identifier: 'test-app',
    name: 'Test App',
    description: 'Test Description',
    isDeleted: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  beforeEach(async () => {
    mockDialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);
    mockApplicationsFacade = {
      loading$: of(false),
      error$: of(null),
      updateApplication: jasmine.createSpy('updateApplication'),
    };
    actions$ = new Subject();
    mockActions = new Actions(actions$);
    mockSnackBar = jasmine.createSpyObj('MatSnackBar', ['open']);

    await TestBed.configureTestingModule({
      imports: [ApplicationEditDialogComponent, NoopAnimationsModule],
      providers: [
        { provide: MatDialogRef, useValue: mockDialogRef },
        { provide: ApplicationsFacade, useValue: mockApplicationsFacade },
        { provide: Actions, useValue: mockActions },
        { provide: MatSnackBar, useValue: mockSnackBar },
        { provide: MAT_DIALOG_DATA, useValue: mockApplication },
        provideMockStore({}),
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ApplicationEditDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should close the dialog on cancel', () => {
    component.onCancel();
    expect(mockDialogRef.close).toHaveBeenCalledWith(false);
  });

  it('should dispatch updateApplication action on submit', () => {
    const app: Partial<UpdateApplicationInput> = { name: 'New Name' };
    component.onSubmit(app);
    expect(mockApplicationsFacade.updateApplication).toHaveBeenCalledWith({
      ...app,
      identifier: mockApplication.identifier,
    });
  });

  it('should show snackbar and close dialog on updateApplicationSuccess', (done) => {
    // Trigger the action after component initialization
    setTimeout(() => {
      actions$.next(ApplicationActions.updateApplicationSuccess({ application: {} as any }));
      
      // Allow time for subscription to process
      setTimeout(() => {
        expect(mockSnackBar.open).toHaveBeenCalledWith('Application updated successfully.', 'OK', { duration: 3000 });
        expect(mockDialogRef.close).toHaveBeenCalledWith(true);
        done();
      }, 10);
    }, 10);
  });

  it('should show snackbar on updateApplicationFailure', (done) => {
    const error = { message: 'Error' };
    
    // Trigger the action after component initialization
    setTimeout(() => {
      actions$.next(ApplicationActions.updateApplicationFailure({ error }));
      
      // Allow time for subscription to process
      setTimeout(() => {
        expect(mockSnackBar.open).toHaveBeenCalledWith(`Error updating application: ${error.message}`, 'OK', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        done();
      }, 10);
    }, 10);
  });
});