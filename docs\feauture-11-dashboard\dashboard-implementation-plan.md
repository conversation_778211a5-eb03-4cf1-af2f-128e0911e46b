# Dashboard Implementation Plan - StoerungsBuddy Frontend

## Overview
This plan outlines how to transform the current static dashboard into a dynamic dashboard that displays real data from Incidents and Applications using the existing GraphQL queries and NgRx state management.

## Current State Analysis

### Existing Infrastructure ✅
- **GraphQL Queries**: `GET_ALL_INCIDENTS`, `GET_MY_INCIDENTS`, `GET_ALL_APPLICATIONS`
- **NgRx State Management**: Complete setup with `IncidentsFacade` and `ApplicationsFacade`
- **Services**: `IncidentService` and `ApplicationService` with GraphQL integration
- **Data Models**: `Incident` and `Application` interfaces

### Current Dashboard Limitations ❌
- Static placeholder data (hardcoded numbers)
- No real data integration
- No state management integration
- No loading states or error handling

## Implementation Strategy

### Phase 1: Dashboard Component Enhancement

#### 1.1 State Management Integration
```mermaid
graph TD
    A[Dashboard Component] --> B[IncidentsFacade]
    A --> C[ApplicationsFacade]
    B --> D[Incidents Store]
    C --> E[Applications Store]
    D --> F[IncidentService]
    E --> G[ApplicationService]
    F --> H[GraphQL Incidents API]
    G --> I[GraphQL Applications API]
```

#### 1.2 Data Loading Strategy
- **OnInit**: Load both incidents and applications data
- **Manual Refresh**: Refresh button to reload data
- **Loading States**: Show loading indicators during data fetch
- **Error Handling**: Display user-friendly error messages

#### 1.3 Dashboard Metrics Calculation
```typescript
interface DashboardMetrics {
  totalIncidents: number;
  activeIncidents: number;
  resolvedIncidents: number;
  plannedMaintenance: number;
  totalApplications: number;
  activeApplications: number;
}
```

### Phase 2: Dashboard Layout & Components

#### 2.1 Statistics Cards
- **Active Incidents**: Count of unresolved incidents
- **Planned Maintenance**: Count of `WARTUNGSFENSTER` type incidents
- **Managed Applications**: Count of active (non-deleted) applications
- **Recent Activity**: Count of incidents created in last 7 days

#### 2.2 Data Lists
- **Current Active Incidents**: List of top 5 unresolved incidents
- **Recent Applications**: List of 5 most recently added applications

#### 2.3 Quick Actions
- **Manual Refresh**: Button to reload all dashboard data
- **Create New Incident**: Navigate to incident creation
- **Add Application**: Navigate to application creation

### Phase 3: Component Architecture

```mermaid
graph TD
    A[DashboardComponent] --> B[DashboardStatsComponent]
    A --> C[ActiveIncidentsListComponent]
    A --> D[RecentApplicationsListComponent]
    A --> E[QuickActionsComponent]
    
    B --> F[StatCardComponent]
    C --> G[IncidentItemComponent]
    D --> H[ApplicationItemComponent]
```

#### 3.1 Smart Component Pattern
- **DashboardComponent**: Smart component managing state and data loading
- **Child Components**: Presentational components receiving data via `@Input()`

#### 3.2 Reactive Data Flow
```typescript
// Dashboard Component Data Flow
ngOnInit() {
  // Load initial data
  this.incidentsFacade.loadIncidents();
  this.applicationsFacade.loadApplications({ isDeleted: false });
  
  // Subscribe to data streams
  this.dashboardData$ = combineLatest([
    this.incidentsFacade.allIncidents$,
    this.applicationsFacade.applications$,
    this.incidentsFacade.isLoading$,
    this.applicationsFacade.loading$
  ]).pipe(
    map(([incidents, applications, incidentsLoading, appsLoading]) => ({
      incidents,
      applications,
      isLoading: incidentsLoading || appsLoading,
      metrics: this.calculateMetrics(incidents, applications)
    }))
  );
}
```

### Phase 4: Implementation Details

#### 4.1 Dashboard Component Structure
```typescript
@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    // Custom components
    DashboardStatsComponent,
    ActiveIncidentsListComponent,
    RecentApplicationsListComponent
  ],
  template: `
    <div class="dashboard-container">
      <div class="dashboard-header">
        <h1>Dashboard</h1>
        <button mat-icon-button (click)="refreshData()" [disabled]="isLoading">
          <mat-icon>refresh</mat-icon>
        </button>
      </div>
      
      <div *ngIf="isLoading" class="loading-container">
        <mat-spinner></mat-spinner>
      </div>
      
      <div *ngIf="!isLoading" class="dashboard-content">
        <app-dashboard-stats [metrics]="metrics"></app-dashboard-stats>
        <app-active-incidents-list [incidents]="activeIncidents"></app-active-incidents-list>
        <app-recent-applications-list [applications]="recentApplications"></app-recent-applications-list>
      </div>
    </div>
  `
})
export class DashboardComponent implements OnInit {
  // Component implementation
}
```

#### 4.2 Data Processing Logic
```typescript
private calculateMetrics(incidents: Incident[], applications: Application[]): DashboardMetrics {
  const activeIncidents = incidents.filter(i => !i.isResolved);
  const plannedMaintenance = incidents.filter(i => 
    i.type === IncidentType.WARTUNGSFENSTER && !i.isResolved
  );
  const activeApplications = applications.filter(a => !a.isDeleted);
  
  return {
    totalIncidents: incidents.length,
    activeIncidents: activeIncidents.length,
    resolvedIncidents: incidents.filter(i => i.isResolved).length,
    plannedMaintenance: plannedMaintenance.length,
    totalApplications: applications.length,
    activeApplications: activeApplications.length
  };
}
```

#### 4.3 Error Handling Strategy
```typescript
// Error handling with user-friendly messages
errorHandling$ = combineLatest([
  this.incidentsFacade.error$,
  this.applicationsFacade.error$
]).pipe(
  map(([incidentsError, appsError]) => {
    if (incidentsError || appsError) {
      return {
        hasError: true,
        message: 'Fehler beim Laden der Dashboard-Daten. Bitte versuchen Sie es erneut.'
      };
    }
    return { hasError: false, message: null };
  })
);
```

## Technical Implementation Details

### 1. NgRx Integration Pattern

#### Store Subscription Management
```typescript
export class DashboardComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  // Reactive data streams
  dashboardData$ = combineLatest([
    this.incidentsFacade.allIncidents$,
    this.applicationsFacade.applications$,
    this.incidentsFacade.isLoading$,
    this.applicationsFacade.loading$,
    this.incidentsFacade.error$,
    this.applicationsFacade.error$
  ]).pipe(
    takeUntil(this.destroy$),
    map(([incidents, applications, incidentsLoading, appsLoading, incidentsError, appsError]) => {
      return {
        incidents,
        applications,
        isLoading: incidentsLoading || appsLoading,
        hasError: !!(incidentsError || appsError),
        errorMessage: incidentsError || appsError,
        metrics: this.calculateMetrics(incidents, applications),
        activeIncidents: this.getActiveIncidents(incidents),
        recentApplications: this.getRecentApplications(applications)
      };
    })
  );
  
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
```

#### Data Transformation Methods
```typescript
private getActiveIncidents(incidents: Incident[]): Incident[] {
  return incidents
    .filter(incident => !incident.isResolved)
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, 5); // Top 5 most recent active incidents
}

private getRecentApplications(applications: Application[]): Application[] {
  return applications
    .filter(app => !app.isDeleted)
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, 5); // Top 5 most recently added applications
}

private calculateMetrics(incidents: Incident[], applications: Application[]): DashboardMetrics {
  const now = new Date();
  const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  
  const activeIncidents = incidents.filter(i => !i.isResolved);
  const resolvedIncidents = incidents.filter(i => i.isResolved);
  const plannedMaintenance = incidents.filter(i => 
    i.type === IncidentType.WARTUNGSFENSTER && !i.isResolved
  );
  const recentIncidents = incidents.filter(i => 
    new Date(i.createdAt) >= sevenDaysAgo
  );
  const activeApplications = applications.filter(a => !a.isDeleted);
  
  return {
    totalIncidents: incidents.length,
    activeIncidents: activeIncidents.length,
    resolvedIncidents: resolvedIncidents.length,
    plannedMaintenance: plannedMaintenance.length,
    totalApplications: applications.length,
    activeApplications: activeApplications.length,
    recentActivity: recentIncidents.length
  };
}
```

### 2. Component Architecture Details

#### Dashboard Stats Component
```typescript
@Component({
  selector: 'app-dashboard-stats',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatIconModule],
  template: `
    <div class="stats-grid">
      <mat-card class="stats-card" *ngFor="let stat of statsCards">
        <mat-card-header>
          <mat-card-title>{{ stat.title }}</mat-card-title>
          <mat-icon [class]="stat.iconClass">{{ stat.icon }}</mat-icon>
        </mat-card-header>
        <mat-card-content>
          <div class="stats-number">{{ stat.value }}</div>
          <div class="stats-label">{{ stat.label }}</div>
        </mat-card-content>
      </mat-card>
    </div>
  `
})
export class DashboardStatsComponent {
  @Input() metrics!: DashboardMetrics;
  
  get statsCards() {
    return [
      {
        title: 'Aktive Vorfälle',
        value: this.metrics.activeIncidents,
        label: 'Ungelöst',
        icon: 'warning',
        iconClass: 'stats-icon warning'
      },
      {
        title: 'Geplante Wartungen',
        value: this.metrics.plannedMaintenance,
        label: 'Diese Woche',
        icon: 'build',
        iconClass: 'stats-icon info'
      },
      {
        title: 'Verwaltete Anwendungen',
        value: this.metrics.activeApplications,
        label: 'Aktiv',
        icon: 'apps',
        iconClass: 'stats-icon success'
      },
      {
        title: 'Kürzliche Aktivität',
        value: this.metrics.recentActivity,
        label: 'Letzte 7 Tage',
        icon: 'trending_up',
        iconClass: 'stats-icon info'
      }
    ];
  }
}
```

#### Active Incidents List Component
```typescript
@Component({
  selector: 'app-active-incidents-list',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatListModule, MatIconModule, MatChipsModule],
  template: `
    <mat-card class="incidents-card">
      <mat-card-header>
        <mat-card-title>Aktive Vorfälle</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div *ngIf="incidents.length === 0" class="empty-state">
          <mat-icon>check_circle</mat-icon>
          <p>Keine aktiven Vorfälle</p>
        </div>
        <mat-list *ngIf="incidents.length > 0">
          <mat-list-item *ngFor="let incident of incidents">
            <mat-icon matListItemIcon [class]="getIncidentIconClass(incident.type)">
              {{ getIncidentIcon(incident.type) }}
            </mat-icon>
            <div matListItemTitle>{{ incident.title }}</div>
            <div matListItemLine>
              <mat-chip [class]="getIncidentTypeClass(incident.type)">
                {{ getIncidentTypeLabel(incident.type) }}
              </mat-chip>
              <span class="incident-time">{{ incident.startTime | date:'short' }}</span>
            </div>
          </mat-list-item>
        </mat-list>
      </mat-card-content>
    </mat-card>
  `
})
export class ActiveIncidentsListComponent {
  @Input() incidents: Incident[] = [];
  
  getIncidentIcon(type: IncidentType): string {
    switch (type) {
      case IncidentType.STOERUNG: return 'error';
      case IncidentType.WARTUNGSFENSTER: return 'build';
      default: return 'info';
    }
  }
  
  getIncidentIconClass(type: IncidentType): string {
    switch (type) {
      case IncidentType.STOERUNG: return 'incident-icon error';
      case IncidentType.WARTUNGSFENSTER: return 'incident-icon warning';
      default: return 'incident-icon info';
    }
  }
  
  getIncidentTypeClass(type: IncidentType): string {
    switch (type) {
      case IncidentType.STOERUNG: return 'incident-chip error';
      case IncidentType.WARTUNGSFENSTER: return 'incident-chip warning';
      default: return 'incident-chip info';
    }
  }
  
  getIncidentTypeLabel(type: IncidentType): string {
    switch (type) {
      case IncidentType.STOERUNG: return 'Störung';
      case IncidentType.WARTUNGSFENSTER: return 'Wartung';
      default: return 'Info';
    }
  }
}
```

#### Recent Applications List Component
```typescript
@Component({
  selector: 'app-recent-applications-list',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatListModule, MatIconModule],
  template: `
    <mat-card class="applications-card">
      <mat-card-header>
        <mat-card-title>Kürzlich hinzugefügte Anwendungen</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div *ngIf="applications.length === 0" class="empty-state">
          <mat-icon>apps</mat-icon>
          <p>Keine Anwendungen registriert</p>
        </div>
        <mat-list *ngIf="applications.length > 0">
          <mat-list-item *ngFor="let app of applications">
            <mat-icon matListItemIcon class="app-icon">apps</mat-icon>
            <div matListItemTitle>{{ app.name }}</div>
            <div matListItemLine>
              {{ app.description || 'Keine Beschreibung' }}
            </div>
            <div matListItemLine class="app-date">
              Hinzugefügt: {{ app.createdAt | date:'short' }}
            </div>
          </mat-list-item>
        </mat-list>
      </mat-card-content>
    </mat-card>
  `
})
export class RecentApplicationsListComponent {
  @Input() applications: Application[] = [];
}
```

### 3. Error Handling & Loading States

#### Loading State Management
```typescript
// In DashboardComponent template
<div class="dashboard-container">
  <div class="dashboard-header">
    <h1>Dashboard</h1>
    <button 
      mat-icon-button 
      (click)="refreshData()" 
      [disabled]="(dashboardData$ | async)?.isLoading"
      matTooltip="Daten aktualisieren">
      <mat-icon [class.spinning]="(dashboardData$ | async)?.isLoading">refresh</mat-icon>
    </button>
  </div>
  
  <!-- Loading State -->
  <div *ngIf="(dashboardData$ | async)?.isLoading" class="loading-container">
    <mat-spinner diameter="40"></mat-spinner>
    <p>Dashboard-Daten werden geladen...</p>
  </div>
  
  <!-- Error State -->
  <div *ngIf="(dashboardData$ | async)?.hasError" class="error-container">
    <mat-icon class="error-icon">error</mat-icon>
    <p>{{ (dashboardData$ | async)?.errorMessage }}</p>
    <button mat-raised-button color="primary" (click)="refreshData()">
      Erneut versuchen
    </button>
  </div>
  
  <!-- Content -->
  <div *ngIf="!(dashboardData$ | async)?.isLoading && !(dashboardData$ | async)?.hasError" 
       class="dashboard-content">
    <!-- Dashboard components -->
  </div>
</div>
```

#### Refresh Functionality
```typescript
refreshData(): void {
  // Clear any existing errors
  this.incidentsFacade.clearIncidentsError();
  
  // Reload data
  this.incidentsFacade.loadIncidents();
  this.applicationsFacade.loadApplications({ isDeleted: false });
}
```

### 4. Styling & Responsive Design

#### SCSS Structure
```scss
.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
  
  h1 {
    margin: 0;
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--text-primary);
  }
  
  .spinning {
    animation: spin 1s linear infinite;
  }
}

.dashboard-content {
  display: grid;
  gap: 1.5rem;
  grid-template-areas: 
    "stats stats"
    "incidents applications";
  grid-template-columns: 1fr 1fr;
}

.stats-grid {
  grid-area: stats;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.incidents-card {
  grid-area: incidents;
}

.applications-card {
  grid-area: applications;
}

// Responsive breakpoints
@media (max-width: 768px) {
  .dashboard-content {
    grid-template-areas: 
      "stats"
      "incidents"
      "applications";
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

// Loading and error states
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
  color: var(--text-secondary);
  
  mat-icon {
    font-size: 3rem;
    width: 3rem;
    height: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
```

### 5. Performance Optimizations

#### Change Detection Strategy
```typescript
@Component({
  selector: 'app-dashboard',
  changeDetection: ChangeDetectionStrategy.OnPush,
  // ... rest of component
})
```

#### TrackBy Functions
```typescript
// For ngFor loops to optimize rendering
trackByIncidentId(index: number, incident: Incident): string {
  return incident.identifier;
}

trackByApplicationId(index: number, application: Application): string {
  return application.identifier;
}
```

#### Memoization for Expensive Calculations
```typescript
private metricsCache = new Map<string, DashboardMetrics>();

private calculateMetrics(incidents: Incident[], applications: Application[]): DashboardMetrics {
  const cacheKey = `${incidents.length}-${applications.length}-${incidents.map(i => i.updatedAt).join('')}`;
  
  if (this.metricsCache.has(cacheKey)) {
    return this.metricsCache.get(cacheKey)!;
  }
  
  const metrics = {
    // ... calculation logic
  };
  
  this.metricsCache.set(cacheKey, metrics);
  return metrics;
}
```

## Implementation Timeline

### Week 1: Foundation
- [ ] Enhance `DashboardComponent` with NgRx integration
- [ ] Implement data loading and state management
- [ ] Add loading states and error handling
- [ ] Create basic dashboard metrics calculation

### Week 2: Components & UI
- [ ] Create `DashboardStatsComponent` with real data
- [ ] Implement `ActiveIncidentsListComponent`
- [ ] Implement `RecentApplicationsListComponent`
- [ ] Add manual refresh functionality
- [ ] Implement responsive design

### Week 3: Polish & Testing
- [ ] Add empty states and improved error messages
- [ ] Performance optimizations (OnPush, TrackBy, memoization)
- [ ] Unit tests for dashboard components
- [ ] Integration testing with NgRx store
- [ ] Accessibility improvements

## Technical Considerations

### Dependencies
- No additional dependencies required
- Utilizes existing Angular Material components
- Leverages current NgRx setup

### Browser Compatibility
- Supports modern browsers (ES2020+)
- Responsive design for mobile devices
- Accessibility compliance (WCAG 2.1)

### Security
- No additional security concerns
- Uses existing authentication flow
- Data sanitization through Angular's built-in mechanisms

## Success Criteria

1. ✅ Dashboard displays real-time data from GraphQL APIs
2. ✅ Manual refresh functionality works correctly
3. ✅ Loading states provide good user experience
4. ✅ Error handling shows user-friendly messages
5. ✅ Responsive design works on all screen sizes
6. ✅ Performance meets Angular best practices
7. ✅ Code follows project's clean code standards
8. ✅ Components are reusable and maintainable
9. ✅ NgRx integration follows established patterns
10. ✅ User interface is intuitive and accessible

This comprehensive plan provides detailed technical implementation guidance for transforming the static dashboard into a dynamic, data-driven interface that leverages the existing GraphQL infrastructure and NgRx state management while maintaining simplicity and excellent user experience.