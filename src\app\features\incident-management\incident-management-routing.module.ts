import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { IncidentListComponent } from './components/incident-list/incident-list.component';
import { IncidentDetailComponent } from './components/incident-detail/incident-detail.component';

const routes: Routes = [
  {
    path: '',
    component: IncidentListComponent,
    title: 'Störungsverwaltung'
  },
  {
    path: ':id',
    component: IncidentDetailComponent,
    title: 'Störungsdetails'
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class IncidentManagementRoutingModule { }