# Implementierungsplan: System-Verwaltung

## 1. Übersicht und Ziele

Die System-Verwaltung ist ein zentraler Bestandteil des Störungs-Buddy, der die Verwaltung aller überwachten Systeme ermöglicht. 

### Primäre Ziele:
- Entwicklung einer übersichtlichen System-Liste mit erweiterten Suchfunktionen
- Implementierung einer detaillierten System-Ansicht mit Status-Historie
- Bereitstellung eines intuitiven Formulars für das Anlegen und Bearbeiten von Systemen
- Integration einer Abhängigkeitsvisualisierung zwischen Systemen

## 2. Komponenten-Struktur

### 2.1 Feature-Modul
```mermaid
graph TD
    A[SystemsModule] --> B[Components]
    A --> C[Services]
    A --> D[Store]
    A --> E[Models]
    
    B --> B1[SystemListComponent]
    B --> B2[SystemDetailComponent]
    B --> B3[SystemFormComponent]
    B --> B4[SystemDependencyGraphComponent]
    
    C --> C1[SystemService]
    
    D --> D1[Actions]
    D --> D2[Reducers]
    D --> D3[Effects]
    D --> D4[Selectors]
    
    E --> E1[System.interface]
    E --> E2[SystemStatus.enum]
    E --> E3[SystemCategory.interface]
```

### 2.2 Routing-Struktur
```mermaid
graph LR
    A[/systems] --> B[SystemListComponent]
    C[/systems/new] --> D[SystemFormComponent]
    E[/systems/:id] --> F[SystemDetailComponent]
    G[/systems/:id/edit] --> H[SystemFormComponent]
    I[/systems/:id/history] --> J[SystemHistoryComponent]
```

## 3. Datenmodell

### 3.1 System Interface
```typescript
interface System {
  id: string;
  name: string;
  description: string;
  category: SystemCategory;
  status: SystemStatus;
  dependencies: string[]; // System IDs
  responsibleTeam: string;
  contactPerson: string;
  createdAt: Date;
  updatedAt: Date;
  lastIncident?: Date;
  maintenanceWindow?: {
    start: string;
    end: string;
    frequency: string;
  };
}
```

### 3.2 SystemCategory Interface
```typescript
interface SystemCategory {
  id: string;
  name: string;
  description: string;
  color: string;
}
```

### 3.3 SystemStatus Enum
```typescript
enum SystemStatus {
  OPERATIONAL = 'operational',
  DEGRADED = 'degraded',
  DOWN = 'down',
  MAINTENANCE = 'maintenance'
}
```

## 4. Implementierungsdetails

### 4.1 Komponenten

#### SystemListComponent
- Tabellarische Darstellung aller Systeme
- Spalten: Name, Status, Kategorie, Letzter Vorfall
- Sortierung nach allen Spalten
- Filterung nach Status und Kategorie
- Suchfunktion für Namen und Beschreibung
- Bulk-Aktionen für ausgewählte Systeme

#### SystemDetailComponent
- Header mit System-Namen und Status
- Beschreibung und allgemeine Informationen
- Status-Historie mit Timeline
- Abhängigkeitsgraph
- Verknüpfte Störungen und Wartungen
- Quick-Actions (Status ändern, Bearbeiten, etc.)

#### SystemFormComponent
- Reaktives Formular mit Validierung
- Auto-Save Funktionalität
- Dynamische Abhängigkeitsauswahl
- Kategorie-Auswahl mit Farbvorschau
- Wartungsfenster-Definition

### 4.2 Store Integration

#### Actions
- LoadSystems
- LoadSystemSuccess/Failure
- CreateSystem
- UpdateSystem
- DeleteSystem
- UpdateSystemStatus
- LoadSystemHistory
- SetSystemFilter

#### State Interface
```typescript
interface SystemsState {
  entities: { [id: string]: System };
  loading: boolean;
  loaded: boolean;
  error: string | null;
  selectedSystemId: string | null;
  filters: {
    status?: SystemStatus;
    category?: string;
    search?: string;
  };
}
```

## 5. API-Integration

### 5.1 GraphQL Queries
```graphql
query GetSystems {
  systems {
    id
    name
    description
    status
    category {
      id
      name
      color
    }
    dependencies {
      id
      name
      status
    }
  }
}

query GetSystemById($id: ID!) {
  system(id: $id) {
    # All fields
  }
}
```

### 5.2 GraphQL Mutations
```graphql
mutation CreateSystem($input: SystemInput!) {
  createSystem(input: $input) {
    id
    # Other fields
  }
}

mutation UpdateSystem($id: ID!, $input: SystemInput!) {
  updateSystem(id: $id, input: $input) {
    id
    # Other fields
  }
}
```

## 6. Tests

### 6.1 Unit Tests
- Komponenten-Tests für alle UI-Komponenten
- Service-Tests für SystemService
- Store-Tests für Actions, Reducers, Effects
- Modell-Validierungstests

### 6.2 Integration Tests
- Formular-Submission-Tests
- Filter- und Suchtests
- Dependency-Graph-Interaktionstests
- Store-Integration-Tests

### 6.3 E2E Tests
- System CRUD-Operationen
- Filterfunktionen
- Dependency-Management
- Status-Änderungen