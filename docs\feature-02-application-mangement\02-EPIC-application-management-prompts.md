# Update EPIC

```
Aktualisiere die @/docs/02-EPIC-system-management.md mit den letzten Informationen aus der GraphQL-API @/docs/api/graphql-api.md 
```

# Init-Prompt

```
<PERSON><PERSON>elle unter docs\02-EPIC-system-management-plan.md einen Implementierungsplan mit Checklisten zur Umsetzung von @/docs/02-EPIC-system-management.md  . Stoppe dann zunächst!
```

# Run

```
<PERSON><PERSON> den @/docs/02-EPIC-system-management-plan.md schrittweise um!
```

# Update checkList

```
Aktualisiere die Checkliste gegenüber dem tatsächliche Implementierungsstand in @/docs/02-EPIC-system-management-plan.md 

Überprüfe ob das Backend korrekt eingebunden ist. Dieses findet sich aktuell unter localhost:5079
```

# Run Phase II

```
Setze den Plan schrittweise um für die noch offenen Punkte  
@/docs/02-EPIC-system-management-plan.md 

Aktualisiere anschließend die Checkliste!
```

# Dialog-Nutzung verifizieren und Sidebar nachziehen

```
Prüfe die aktuelle Implementierung mit dem Umsetzungsplan @/docs/02-EPIC-system-management-plan.md . Aktuell kann ich im Frontend keine Applikationen (Systeme) hinzufügen oder löschen! Das System reagiert hier nicht! Prüfe zudem die Implementierung der Sidebar navigation und ziehe es nach!
```

# Fix 1

```
In der UI muss in der APplikationsliste unter dem Pfad /applications immer einmal kurz der Status angeklickt werden und erst danach wird die Liste korrekt dargestellt. Bitte korrigieren! 
```

# Fix 2
```
Bitte die Dialoge und Forms moderner gestalten, ebenso die Sidebar!
```

# Fix 3
```
Prüfe die korrekte Backend-Anbindung zum Abruf über GraphQL.
Prüfe die Implementierung gegen @/docs/api/graphql-api.md . 

Der Backend-Server läuft unter dem localhost:5079. Die graphql-UI ist unter localhost:5079/graphql erreichbar! Ergänze Console-Logging-Ausgaben für den Abruf der Applications. 
```
# Fix 4
```
Ergänze Console.Logging-Ausgben in:

- @/src/app/core/services/application.service.ts 
- @/src/app/features/application-management/components/application-list/application-list.component.ts 

Aktuell wird der graphQL-Abruf für den Abruf der APplicationen als "GetAllApplications" nicht korrekt ausgeführt!
```

#  Fix 5

```
Prüfe die aktuelle Implementierung für die GraphQL-Implementierung zum Anlegen einer neuen Applikation:


mutation CreateApplicationInput {
createApplication(input: {
    name: "New Application",
    description: "This is a new application"
  }) {
    identifier
    name
    description
    isDeleted
    createdAt
    updatedAt
  }
}

Nutze mindestens:

- @/src/app/core/services/application.service.ts 
- @/src/app/features/application-management/components/application-list/application-list.component.ts 
```

# Fix 6

```
Prüfe die aktuelle Implementierung für die GraphQL-Implementierung zum Löschen einer Applikation:

Korrekt sollte es sein als Beispiel:

mutation get {
  deleteApplication(identifier: "24ec72ca3b3642829c82a4aff6345ede") {
    name
    createdAt
    description
    isDeleted
    updatedAt
  }

Nutze mindestens:

- @/src/app/core/services/application.service.ts 
- @/src/app/features/application-management/components/application-list/application-list.component.ts 

```

# Fix 7

```
Prüfe die aktuelle Implementierung für die GraphQL-Implementierung zum Aktualisieren einer Applikation:

Korrekt sollte es sein als Beispiel:


mutation get {
  updateApplication(identifier: "c904312225494995a80f45b3dad66ff1", name: "Demo2", description: "Demo2", isDeleted: false) {
    description
    identifier
    isDeleted
    name
  }
}

Nutze mindestens:

- @/src/app/core/services/application.service.ts 
- @/src/app/features/application-management/components/application-list/application-list.component.ts 
```

# Learnings aufarbeiten

```
Aktualisiere unter \.roo\rules als graphql-learnings.md eine Zusammenfassung der bisherigen fehlerhaften Implementierung für Update-Mutations!
```
