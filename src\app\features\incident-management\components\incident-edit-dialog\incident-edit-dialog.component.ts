import { Component, Inject, OnInit, OnDestroy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil } from 'rxjs';

// Angular Material
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

// Models and Components
import { Incident, UpdateIncidentInput } from '../../../../core/models/incident.model';
import { IncidentsFacade } from '../../../../store/incidents/incidents.facade';
import { IncidentEditFormComponent } from '../incident-edit-form/incident-edit-form.component';

export interface IncidentEditDialogData {
  incident: Incident;
  title?: string;
}

@Component({
  selector: 'app-incident-edit-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    IncidentEditFormComponent
  ],
  templateUrl: './incident-edit-dialog.component.html',
  styleUrls: ['./incident-edit-dialog.component.scss']
})
export class IncidentEditDialogComponent implements OnInit, OnDestroy {
  loading = false;
  error: string | null = null;
  private wasUpdating = false;
  
  private destroy$ = new Subject<void>();
  private incidentsFacade = inject(IncidentsFacade);
  private snackBar = inject(MatSnackBar);

  constructor(
    public dialogRef: MatDialogRef<IncidentEditDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: IncidentEditDialogData
  ) {
    // Configure dialog
    this.dialogRef.disableClose = true;
    this.dialogRef.updateSize('800px', 'auto');
  }

  ngOnInit(): void {
    this.setupStoreSubscriptions();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setupStoreSubscriptions(): void {
    // Subscribe to updating state
    this.incidentsFacade.isUpdating$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(isUpdating => {
      if (isUpdating) {
        this.wasUpdating = true;
      } else if (this.wasUpdating && !this.error) {
        // Update was successful - was updating and now stopped without error
        this.showSuccessMessage('Vorfall wurde erfolgreich aktualisiert');
        this.dialogRef.close({
          updated: true,
          incident: this.data.incident
        });
      }
      this.loading = isUpdating;
    });

    // Subscribe to update error state
    this.incidentsFacade.updateError$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(error => {
      this.error = error;
      if (error) {
        this.wasUpdating = false; // Reset flag on error
        this.showErrorMessage(error);
      }
    });
  }

  onFormSubmit(updateInput: UpdateIncidentInput): void {
    this.error = null;
    this.incidentsFacade.clearIncidentsError();
    
    // Dispatch update action
    this.incidentsFacade.updateIncident(updateInput);
  }

  onFormCancel(): void {
    if (this.loading) {
      return; // Prevent closing during loading
    }
    
    this.dialogRef.close({ updated: false });
  }

  onCloseDialog(): void {
    if (this.loading) {
      return; // Prevent closing during loading
    }
    
    this.dialogRef.close({ updated: false });
  }

  private showSuccessMessage(message: string): void {
    this.snackBar.open(message, 'Schließen', {
      duration: 5000,
      panelClass: ['success-snackbar']
    });
  }

  private showErrorMessage(message: string): void {
    this.snackBar.open(message, 'Schließen', {
      duration: 8000,
      panelClass: ['error-snackbar']
    });
  }

  get dialogTitle(): string {
    return this.data.title || `Vorfall bearbeiten: ${this.data.incident.title}`;
  }

  get canClose(): boolean {
    return !this.loading;
  }
}