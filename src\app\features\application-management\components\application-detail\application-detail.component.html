<div class="application-detail-container">
  <!-- Header with <PERSON>ton -->
  <div class="detail-header">
    <button mat-icon-button (click)="goBack()" class="back-button">
      <mat-icon>arrow_back</mat-icon>
    </button>
    <h2>Applikation Details</h2>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="loading$ | async" class="loading-container">
    <mat-spinner diameter="50"></mat-spinner>
    <p>Lade Applikation...</p>
  </div>

  <!-- Error Message -->
  <div *ngIf="(error$ | async) && !(loading$ | async)" class="error-container">
    <mat-icon color="warn">error</mat-icon>
    <span>{{ (error$ | async)?.message || 'Ein Fehler ist aufgetreten.' }}</span>
    <button mat-raised-button color="primary" (click)="goBack()">
      Zurück zur Liste
    </button>
  </div>

  <!-- Application Details -->
  <div *ngIf="(application$ | async) as application" class="detail-content">
    <!-- Basic Information Card -->
    <mat-card class="info-card">
      <mat-card-header>
        <mat-card-title>
          {{ application.name }}
          <mat-chip [color]="getStatusChipColor(application.isDeleted)" selected class="status-chip">
            {{ getStatusText(application.isDeleted) }}
          </mat-chip>
        </mat-card-title>
      </mat-card-header>
      
      <mat-card-content>
        <div class="info-grid">
          <div class="info-item">
            <label>Name:</label>
            <span>{{ application.name }}</span>
          </div>
          
          <div class="info-item">
            <label>Beschreibung:</label>
            <span>{{ application.description || 'Keine Beschreibung verfügbar' }}</span>
          </div>
          
          <div class="info-item">
            <label>Status:</label>
            <span>{{ getStatusText(application.isDeleted) }}</span>
          </div>
          
          <div class="info-item">
            <label>Erstellt am:</label>
            <span>{{ formatDate(application.createdAt) }}</span>
          </div>
          
          <div class="info-item">
            <label>Zuletzt aktualisiert:</label>
            <span>{{ formatDate(application.updatedAt) }}</span>
          </div>
          
          <div class="info-item">
            <label>Identifier:</label>
            <span class="identifier">{{ application.identifier }}</span>
          </div>
        </div>
      </mat-card-content>
      
      <mat-card-actions>
        <button mat-raised-button
                color="primary"
                [disabled]="application.isDeleted"
                (click)="openEditDialog(application)">
          <mat-icon>edit</mat-icon>
          Bearbeiten
        </button>
        <button mat-stroked-button
                color="warn"
                [disabled]="application.isDeleted"
                (click)="openDeleteDialog(application)">
          <mat-icon>delete</mat-icon>
          Löschen
        </button>
      </mat-card-actions>
    </mat-card>

    <!-- Tabs for Additional Information -->
    <mat-tab-group class="detail-tabs">
      <mat-tab label="Änderungshistorie">
        <div class="tab-content">
          <app-change-history
            [entityId]="application.identifier"
            [entityType]="entityType"
            [maxInitialItems]="10">
          </app-change-history>
        </div>
      </mat-tab>
    </mat-tab-group>
  </div>
</div>