import { Incident } from '../../core/models/incident.model';

export interface IncidentsState {
  incidents: Incident[];
  isLoading: boolean;
  error: string | null;
  isCreating: boolean;
  createError: string | null;
  isUpdating: boolean;
  updateError: string | null;
  isDeleting: boolean;
  deleteError: string | null;
  selectedIncident: Incident | null;
  selectedIncidentLoading: boolean;
  selectedIncidentError: string | null;
}

export const initialIncidentsState: IncidentsState = {
  incidents: [],
  isLoading: false,
  error: null,
  isCreating: false,
  createError: null,
  isUpdating: false,
  updateError: null,
  isDeleting: false,
  deleteError: null,
  selectedIncident: null,
  selectedIncidentLoading: false,
  selectedIncidentError: null
};