<div class="incident-detail-container">
  <!-- Header with navigation -->
  <div class="detail-header">
    <button mat-icon-button (click)="navigateToList()" matTooltip="Zurück zur Liste">
      <mat-icon>arrow_back</mat-icon>
    </button>
    <h2>Vorfall Details</h2>
    <div class="header-actions">
      <button mat-raised-button
              color="primary"
              [disabled]="loading || !incident"
              (click)="openEditDialog()">
        <mat-icon>edit</mat-icon>
        Bearbeiten
      </button>
      <button mat-stroked-button
              color="warn"
              [disabled]="loading || !incident"
              (click)="openDeleteDialog()">
        <mat-icon>delete</mat-icon>
        Löschen
      </button>
    </div>
  </div>

  <!-- Error Message -->
  <div *ngIf="error && !loading" class="error-container">
    <mat-icon color="warn">error</mat-icon>
    <span>{{ error }}</span>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="loading" class="loading-container">
    <mat-spinner diameter="50"></mat-spinner>
    <p>Lade Vorfall...</p>
  </div>

  <!-- Incident Details -->
  <div *ngIf="!loading && incident" class="incident-content">
    <!-- Basic Information Card -->
    <mat-card class="info-card">
      <mat-card-header>
        <mat-card-title>
          <div class="title-section">
            <span class="incident-title">{{ incident.title }}</span>
            <mat-chip [color]="getIncidentTypeColor(incident.type)" selected>
              {{ getIncidentTypeText(incident.type) }}
            </mat-chip>
          </div>
        </mat-card-title>
        <mat-card-subtitle>
          <div class="status-section">
            <mat-chip [color]="getStatusChipColor(incident.isResolved)" selected>
              <mat-icon>{{ incident.isResolved ? 'check_circle' : 'warning' }}</mat-icon>
              {{ getStatusText(incident.isResolved) }}
            </mat-chip>
            <span class="incident-id">ID: {{ incident.identifier }}</span>
          </div>
        </mat-card-subtitle>
      </mat-card-header>

      <mat-card-content>
        <div class="info-grid">
          <!-- Description -->
          <div class="info-item full-width" *ngIf="incident.description">
            <div class="info-label">
              <mat-icon>description</mat-icon>
              Beschreibung
            </div>
            <div class="info-value description-text">
              {{ incident.description }}
            </div>
          </div>

          <!-- Start Time -->
          <div class="info-item">
            <div class="info-label">
              <mat-icon>schedule</mat-icon>
              Startzeit
            </div>
            <div class="info-value">
              {{ formatDateLong(incident.startTime) }}
            </div>
          </div>

          <!-- Planned End Time -->
          <div class="info-item" *ngIf="incident.plannedEndTime">
            <div class="info-label">
              <mat-icon>event</mat-icon>
              Geplantes Ende
            </div>
            <div class="info-value">
              {{ formatDateLong(incident.plannedEndTime) }}
            </div>
          </div>

          <!-- Actual End Time -->
          <div class="info-item" *ngIf="incident.actualEndTime">
            <div class="info-label">
              <mat-icon>event_available</mat-icon>
              Tatsächliches Ende
            </div>
            <div class="info-value">
              {{ formatDateLong(incident.actualEndTime) }}
            </div>
          </div>

          <!-- Duration -->
          <div class="info-item">
            <div class="info-label">
              <mat-icon>timer</mat-icon>
              Dauer
            </div>
            <div class="info-value">
              {{ calculateDuration() }}
            </div>
          </div>

          <!-- Alternatives -->
          <div class="info-item full-width" *ngIf="incident.alternatives">
            <div class="info-label">
              <mat-icon>alt_route</mat-icon>
              Alternativen
            </div>
            <div class="info-value alternatives-text">
              {{ incident.alternatives }}
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Affected Applications Card -->
    <mat-card class="applications-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>apps</mat-icon>
          Betroffene Applikationen
          <span class="app-count">({{ incident.applications.length }})</span>
        </mat-card-title>
      </mat-card-header>

      <mat-card-content>
        <div *ngIf="incident.applications.length > 0; else noApplications" class="applications-grid">
          <mat-chip-set>
            <mat-chip *ngFor="let app of incident.applications" 
                      [matTooltip]="app.description || app.name"
                      color="primary">
              <mat-icon matChipAvatar>{{ app.name.charAt(0).toUpperCase() }}</mat-icon>
              {{ app.name }}
            </mat-chip>
          </mat-chip-set>
        </div>
        
        <ng-template #noApplications>
          <div class="no-applications">
            <mat-icon>info</mat-icon>
            <span>Keine Applikationen betroffen</span>
          </div>
        </ng-template>
      </mat-card-content>
    </mat-card>

    <!-- Timestamps Card -->
    <mat-card class="timestamps-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>history</mat-icon>
          Zeitstempel
        </mat-card-title>
      </mat-card-header>

      <mat-card-content>
        <div class="timestamps-grid">
          <div class="timestamp-item">
            <div class="timestamp-label">
              <mat-icon>add_circle</mat-icon>
              Erstellt
            </div>
            <div class="timestamp-value">
              {{ formatDateLong(incident.createdAt) }}
            </div>
          </div>

          <div class="timestamp-item">
            <div class="timestamp-label">
              <mat-icon>update</mat-icon>
              Zuletzt aktualisiert
            </div>
            <div class="timestamp-value">
              {{ formatDateLong(incident.updatedAt) }}
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Action Buttons -->
    <div class="action-section">
      <button mat-raised-button
              color="primary"
              (click)="openEditDialog()">
        <mat-icon>edit</mat-icon>
        Bearbeiten
      </button>

      <button mat-stroked-button
              color="warn"
              (click)="openDeleteDialog()">
        <mat-icon>delete</mat-icon>
        Löschen
      </button>

      <button mat-raised-button
              [color]="incident.isResolved ? 'warn' : 'accent'"
              (click)="onToggleResolution()"
              [disabled]="true">
        <mat-icon>{{ incident.isResolved ? 'refresh' : 'check' }}</mat-icon>
        {{ incident.isResolved ? 'Wieder öffnen' : 'Als gelöst markieren' }}
      </button>

      <button mat-stroked-button (click)="navigateToList()">
        <mat-icon>list</mat-icon>
        Zurück zur Liste
      </button>
    </div>
  </div>

  <!-- No Incident Found -->
  <div *ngIf="!loading && !incident && !error" class="no-incident-container">
    <mat-icon>search_off</mat-icon>
    <h3>Vorfall nicht gefunden</h3>
    <p>Der angeforderte Vorfall konnte nicht gefunden werden.</p>
    <button mat-raised-button color="primary" (click)="navigateToList()">
      <mat-icon>list</mat-icon>
      Zur Übersicht
    </button>
  </div>
</div>