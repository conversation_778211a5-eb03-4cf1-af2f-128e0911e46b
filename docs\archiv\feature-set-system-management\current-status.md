# Aktueller Stand: System-Verwaltung

## Übersicht

Dieses Dokument beschreibt den aktuellen Implementierungsstand der System-Verwaltung im StoerungsBuddy-Frontend, insbesondere hinsichtlich der Funktionen zum Anlegen, Bearbeiten, Löschen und der Listendarstellung von Systemen.

## Routing

Die Routing-Konfiguration wurde kürzlich aktualisiert, um die korrekte Navigation innerhalb des System-Features zu ermöglichen:

- Die Route `/systems` zeigt die Systemliste an
- Die Route `/systems/new` zeigt das Formular zum Anlegen eines neuen Systems an
- Die Routen für Detailansicht (`/systems/:id`) und Bearbeitung (`/systems/:id/edit`) sind in der Konfiguration vorgesehen, aber noch nicht vollständig implementiert

```mermaid
graph LR
    A[/systems] --> B[SystemListComponent]
    C[/systems/new] --> D[SystemFormComponent]
    E[/systems/:id] -.-> F[Noch nicht implementiert]
    G[/systems/:id/edit] -.-> H[Noch nicht implementiert]
```

## Komponenten

### SystemsComponent

Eine einfache Container-Komponente, die aktuell nur die SystemListComponent enthält. Diese Komponente wurde in der Routing-Konfiguration durch direkte Verwendung der Kinder-Routen ersetzt, um die Navigation zu `/systems/new` zu ermöglichen.

### SystemListComponent

Zeigt eine Liste der Systeme in einer einfachen Tabelle an:

- Derzeit werden Mock-Daten verwendet (3 Beispielsysteme)
- Die Tabelle zeigt Name, Status, Kategorie und letzten Vorfall
- Ein Button zum Anlegen eines neuen Systems ist vorhanden und verlinkt zu `/systems/new`
- Funktionen wie Sortierung, Filterung und Pagination sind noch nicht implementiert
- Die Anbindung an den Store/Service fehlt noch

### SystemFormComponent

Ein reaktives Formular für das Anlegen und Bearbeiten von Systemen:

- Alle Felder gemäß dem System-Interface sind vorhanden
- Grundlegende Validierung ist implementiert (required-Felder)
- Die Logik zum Speichern oder Aktualisieren des Systems ist noch nicht implementiert
- Der Bearbeitungsmodus ist vorbereitet, aber die Logik zum Laden existierender Daten fehlt noch
- Kategorien werden derzeit als Mock-Daten bereitgestellt

## Datenmodelle

Die grundlegenden Datenmodelle sind implementiert:

### System Interface

```typescript
interface System {
  id: string;
  name: string;
  description: string;
  category: SystemCategory;
  status: SystemStatus;
  dependencies: string[]; // System IDs
  responsibleTeam: string;
  contactPerson: string;
  createdAt: Date;
  updatedAt: Date;
  lastIncident?: Date;
  maintenanceWindow?: {
    start: string;
    end: string;
    frequency: string;
  };
}
```

### SystemCategory Interface

```typescript
interface SystemCategory {
  id: string;
  name: string;
  description: string;
  color: string;
}
```

### SystemStatus Enum

```typescript
enum SystemStatus {
  OPERATIONAL = 'operational',
  DEGRADED = 'degraded',
  DOWN = 'down',
  MAINTENANCE = 'maintenance'
}
```

## Funktionalität

### Listendarstellung

- ✅ Grundlegende Tabelle mit Name, Status, Kategorie und letztem Vorfall
- ❌ Sortierung nach Spalten
- ❌ Filterung nach Status und Kategorie
- ❌ Suchfunktion
- ❌ Pagination
- ❌ Anbindung an den Store/Service

### Anlage neuer Systeme

- ✅ Formular zum Anlegen eines neuen Systems
- ✅ Grundlegende Validierung
- ❌ Speicherlogik
- ❌ Anbindung an den Store/Service
- ❌ Erfolgsmeldung und Weiterleitung nach dem Speichern

### Bearbeitung von Systemen

- ✅ Formular kann auch für die Bearbeitung verwendet werden
- ❌ Logik zum Laden existierender Daten
- ❌ Aktualisierungslogik
- ❌ Anbindung an den Store/Service
- ❌ Erfolgsmeldung und Weiterleitung nach dem Speichern

### Löschen von Systemen

- ❌ Button/Funktion zum Löschen
- ❌ Bestätigungsdialog
- ❌ Löschlogik
- ❌ Anbindung an den Store/Service
- ❌ Erfolgsmeldung nach dem Löschen

## Nächste Schritte

Basierend auf dem aktuellen Stand sollten folgende Aufgaben priorisiert werden:

1. **Store-Integration**
   - Actions, Reducer, Effects und Selectors für Systeme implementieren
   - Komponenten an den Store anbinden

2. **API-Integration**
   - GraphQL Queries und Mutations implementieren
   - SystemService erstellen

3. **Detailansicht**
   - SystemDetailComponent implementieren
   - Routing für `/systems/:id` einrichten

4. **Bearbeitungsfunktionalität**
   - Logik zum Laden existierender Daten implementieren
   - Aktualisierungslogik implementieren
   - Routing für `/systems/:id/edit` einrichten

5. **Löschfunktionalität**
   - Button/Funktion zum Löschen hinzufügen
   - Bestätigungsdialog implementieren
   - Löschlogik implementieren

6. **Erweiterte Funktionen für die Listendarstellung**
   - Sortierung, Filterung, Suche und Pagination implementieren