.application-selector {
  width: 100%;

  .selected-applications {
    margin-bottom: 16px;

    mat-chip-set {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    mat-chip {
      background-color: var(--mat-primary-50);
      color: var(--mat-primary-700);
      border: 1px solid var(--mat-primary-200);

      mat-icon {
        color: var(--mat-primary-600);
        cursor: pointer;

        &:hover {
          color: var(--mat-primary-800);
        }
      }
    }
  }

  .search-field {
    width: 100%;

    mat-spinner {
      margin-right: 8px;
    }
  }

  .application-option {
    display: flex;
    flex-direction: column;
    padding: 4px 0;

    .application-name {
      font-weight: 500;
      color: var(--mat-text-primary);
    }

    .application-description {
      font-size: 0.875rem;
      color: var(--mat-text-secondary);
      margin-top: 2px;
      line-height: 1.2;
    }
  }

  .no-results {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--mat-text-secondary);
    font-style: italic;

    mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .application-selector {
    .selected-applications {
      mat-chip {
        font-size: 0.875rem;
      }
    }

    .application-option {
      .application-description {
        font-size: 0.8125rem;
      }
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .application-selector {
    .selected-applications {
      mat-chip {
        border-width: 2px;
      }
    }
  }
}

// Dark theme adjustments
.dark-theme {
  .application-selector {
    .selected-applications {
      mat-chip {
        background-color: var(--mat-primary-900);
        color: var(--mat-primary-100);
        border-color: var(--mat-primary-700);

        mat-icon {
          color: var(--mat-primary-300);

          &:hover {
            color: var(--mat-primary-100);
          }
        }
      }
    }
  }
}