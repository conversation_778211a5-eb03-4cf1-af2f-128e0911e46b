import { Component, Input, Output, EventEmitter, OnInit, On<PERSON><PERSON>roy, inject, forwardRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, ReactiveFormsModule, ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { Subject, takeUntil, startWith, debounceTime, distinctUntilChanged, switchMap, of } from 'rxjs';

// Angular Material
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatChipsModule } from '@angular/material/chips';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

// Models and Services
import { Application } from '../../../../core/models/application.model';
import { ApplicationService } from '../../../../core/services/application.service';

@Component({
  selector: 'app-application-selector',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatAutocompleteModule,
    MatChipsModule,
    MatIconModule,
    MatProgressSpinnerModule
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => ApplicationSelectorComponent),
      multi: true
    }
  ],
  templateUrl: './application-selector.component.html',
  styleUrls: ['./application-selector.component.scss']
})
export class ApplicationSelectorComponent implements OnInit, OnDestroy, ControlValueAccessor {
  @Input() placeholder = 'Anwendungen auswählen...';
  @Input() required = false;
  
  @Output() selectionChange = new EventEmitter<Application[]>();

  selectedApplications: Application[] = [];
  searchControl = new FormControl('');
  filteredApplications: Application[] = [];
  allApplications: Application[] = [];
  loading = false;
  disabled = false;
  
  private destroy$ = new Subject<void>();
  private applicationService = inject(ApplicationService);
  
  // ControlValueAccessor implementation
  private onChange = (value: string[]) => {};
  private onTouched = () => {};
  private pendingValue: string[] | null = null;

  ngOnInit(): void {
    this.loadApplications();
    this.setupSearch();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadApplications(): void {
    this.loading = true;
    this.applicationService.getAllApplications({ isDeleted: false })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (applications) => {
          this.allApplications = applications;
          this.filteredApplications = applications;
          this.loading = false;
          
          // Apply pending value if exists
          if (this.pendingValue) {
            this.writeValue(this.pendingValue);
          }
        },
        error: (error) => {
          console.error('Error loading applications:', error);
          this.loading = false;
        }
      });
  }

  private setupSearch(): void {
    this.searchControl.valueChanges
      .pipe(
        startWith(''),
        debounceTime(300),
        distinctUntilChanged(),
        switchMap(searchTerm => {
          if (!searchTerm || searchTerm.trim() === '') {
            return of(this.allApplications);
          }
          return this.applicationService.searchApplicationsByName(searchTerm);
        }),
        takeUntil(this.destroy$)
      )
      .subscribe({
        next: (applications) => {
          this.filteredApplications = applications.filter(app => 
            !this.selectedApplications.some(selected => selected.identifier === app.identifier)
          );
        },
        error: (error) => {
          console.error('Error searching applications:', error);
        }
      });
  }

  onApplicationSelected(application: Application): void {
    if (!this.selectedApplications.some(selected => selected.identifier === application.identifier)) {
      const updatedSelection = [...this.selectedApplications, application];
      this.selectedApplications = updatedSelection;
      this.selectionChange.emit(updatedSelection);
      this.searchControl.setValue('');
      this.updateFilteredApplications();
      
      // Notify form control
      const applicationIds = updatedSelection.map(app => app.identifier);
      this.onChange(applicationIds);
      this.onTouched();
    }
  }

  onApplicationRemoved(application: Application): void {
    const updatedSelection = this.selectedApplications.filter(
      selected => selected.identifier !== application.identifier
    );
    this.selectedApplications = updatedSelection;
    this.selectionChange.emit(updatedSelection);
    this.updateFilteredApplications();
    
    // Notify form control
    const applicationIds = updatedSelection.map(app => app.identifier);
    this.onChange(applicationIds);
    this.onTouched();
  }

  private updateFilteredApplications(): void {
    this.filteredApplications = this.allApplications.filter(app => 
      !this.selectedApplications.some(selected => selected.identifier === app.identifier)
    );
  }

  displayFn(application: Application): string {
    return application ? application.name : '';
  }

  get hasError(): boolean {
    return this.required && this.selectedApplications.length === 0;
  }

  get errorMessage(): string {
    if (this.hasError) {
      return 'Mindestens eine Anwendung muss ausgewählt werden';
    }
    return '';
  }

  // ControlValueAccessor implementation
  writeValue(value: string[]): void {
    if (value && Array.isArray(value)) {
      if (this.allApplications.length > 0) {
        // Find applications by their IDs
        this.selectedApplications = this.allApplications.filter(app =>
          value.includes(app.identifier)
        );
        this.updateFilteredApplications();
        this.pendingValue = null;
      } else {
        // Store the value to apply later when applications are loaded
        this.pendingValue = value;
      }
    } else {
      this.selectedApplications = [];
      this.updateFilteredApplications();
      this.pendingValue = null;
    }
  }

  registerOnChange(fn: (value: string[]) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
    if (isDisabled) {
      this.searchControl.disable();
    } else {
      this.searchControl.enable();
    }
  }
}