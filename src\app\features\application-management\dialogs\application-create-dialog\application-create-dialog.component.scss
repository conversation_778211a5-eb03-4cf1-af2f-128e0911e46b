.application-create-dialog {
  display: flex;
  flex-direction: column;
  max-height: 90vh;
  min-width: 500px;
  max-width: 600px;

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 24px 16px 24px;
    border-bottom: 1px solid var(--mat-divider-color);
    background-color: var(--mat-surface);

    .header-content {
      display: flex;
      align-items: center;
      gap: 12px;
      flex: 1;

      .header-icon {
        color: var(--mat-primary);
        font-size: 24px;
        width: 24px;
        height: 24px;
      }

      .dialog-title {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 500;
        color: var(--mat-text-primary);
        line-height: 1.2;
      }
    }

    .close-button {
      color: var(--mat-text-secondary);
      
      &:hover:not(:disabled) {
        color: var(--mat-text-primary);
        background-color: var(--mat-action-hover);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }

  .dialog-content {
    position: relative;
    flex: 1;
    overflow-y: auto;
    padding: 0;
    max-height: calc(90vh - 120px);

    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.8);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 16px;
      z-index: 10;
      backdrop-filter: blur(2px);

      .loading-text {
        margin: 0;
        color: var(--mat-text-secondary);
        font-weight: 500;
      }
    }

    .error-banner {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      padding: 16px 24px;
      background-color: var(--mat-error-container);
      border-left: 4px solid var(--mat-error);
      margin: 16px 24px;
      border-radius: 4px;

      .error-icon {
        color: var(--mat-error);
        font-size: 20px;
        width: 20px;
        height: 20px;
        margin-top: 2px;
      }

      .error-content {
        flex: 1;

        h4 {
          margin: 0 0 4px 0;
          font-size: 0.875rem;
          font-weight: 600;
          color: var(--mat-error);
        }

        p {
          margin: 0;
          font-size: 0.8125rem;
          color: var(--mat-on-error-container);
          line-height: 1.4;
        }
      }
    }

    .form-container {
      transition: opacity 0.2s ease-in-out;
      padding: 24px;

      &.disabled {
        opacity: 0.6;
        pointer-events: none;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .application-create-dialog {
    min-width: 100%;
    max-width: 100%;
    max-height: 100vh;

    .dialog-header {
      padding: 16px;

      .header-content {
        .dialog-title {
          font-size: 1.125rem;
        }
      }
    }

    .dialog-content {
      max-height: calc(100vh - 100px);
      
      .error-banner {
        margin: 12px 16px;
        padding: 12px 16px;
      }

      .form-container {
        padding: 16px;
      }
    }
  }
}

@media (max-width: 480px) {
  .application-create-dialog {
    .dialog-header {
      .header-content {
        .dialog-title {
          font-size: 1rem;
          line-height: 1.3;
        }
      }
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .application-create-dialog {
    .dialog-header {
      border-bottom-width: 2px;
    }

    .error-banner {
      border-left-width: 6px;
      border: 2px solid var(--mat-error);
    }
  }
}

// Dark theme adjustments
.dark-theme {
  .application-create-dialog {
    .dialog-content {
      .loading-overlay {
        background-color: rgba(0, 0, 0, 0.8);
      }
    }
  }
}

// Focus management
.application-create-dialog {
  .dialog-header {
    .close-button {
      &:focus {
        outline: 2px solid var(--mat-primary);
        outline-offset: 2px;
      }
    }
  }
}

// Animation for loading overlay
.application-create-dialog {
  .dialog-content {
    .loading-overlay {
      animation: fadeIn 0.3s ease-in-out;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}