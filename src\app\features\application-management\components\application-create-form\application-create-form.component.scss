.application-create-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-width: 100%;

  .full-width {
    width: 100%;
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 16px;
    border-top: 1px solid var(--mat-divider-color);

    button {
      min-width: 120px;

      mat-icon {
        margin-right: 8px;
        animation: spin 1s linear infinite;
      }
    }
  }

  // Form field customizations
  mat-form-field {
    .mat-mdc-form-field-subscript-wrapper {
      margin-top: 4px;
    }

    .mat-mdc-form-field-hint-wrapper {
      padding-top: 4px;
    }

    textarea {
      resize: vertical;
      min-height: 80px;
    }
  }

  // Error state styling
  .mat-mdc-form-field.mat-form-field-invalid {
    .mat-mdc-form-field-outline-thick {
      border-color: var(--mat-error);
    }
  }

  // Disabled state styling
  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }
}

// Responsive design
@media (max-width: 768px) {
  .application-create-form {
    gap: 16px;

    .form-actions {
      flex-direction: column-reverse;
      gap: 8px;

      button {
        width: 100%;
      }
    }
  }
}

@media (max-width: 480px) {
  .application-create-form {
    gap: 12px;
  }
}

// Animation for loading spinner
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .application-create-form {
    .form-actions {
      border-top-width: 2px;
    }
  }
}

// Focus management
.application-create-form {
  mat-form-field {
    &.mat-focused {
      .mat-mdc-form-field-outline-thick {
        border-width: 2px;
      }
    }
  }

  button {
    &:focus {
      outline: 2px solid var(--mat-primary);
      outline-offset: 2px;
    }
  }
}

// Dark theme adjustments
.dark-theme {
  .application-create-form {
    .form-actions {
      border-top-color: rgba(255, 255, 255, 0.12);
    }
  }
}

// Print styles
@media print {
  .application-create-form {
    .form-actions {
      display: none;
    }
  }
}