<form [formGroup]="editForm" (ngSubmit)="onSubmit()" class="incident-edit-form">
  <!-- Title Field -->
  <mat-form-field appearance="outline" class="full-width">
    <mat-label>Titel *</mat-label>
    <input
      matInput
      formControlName="title"
      placeholder="Titel des Vorfalls eingeben"
      maxlength="200"
      aria-label="Titel des Vorfalls">
    <mat-hint align="end">{{ editForm.get('title')?.value?.length || 0 }}/200</mat-hint>
    <mat-error *ngIf="hasFieldError('title')">
      {{ getFieldError('title') }}
    </mat-error>
  </mat-form-field>

  <!-- Type Field -->
  <mat-form-field appearance="outline" class="full-width">
    <mat-label>Typ *</mat-label>
    <mat-select
      formControlName="type"
      aria-label="Typ des Vorfalls">
      <mat-option 
        *ngFor="let type of incidentTypes" 
        [value]="type.value"
        [attr.aria-label]="'Typ auswählen: ' + type.label">
        {{ type.label }}
      </mat-option>
    </mat-select>
    <mat-error *ngIf="hasFieldError('type')">
      {{ getFieldError('type') }}
    </mat-error>
  </mat-form-field>

  <!-- Description Field -->
  <mat-form-field appearance="outline" class="full-width">
    <mat-label>Beschreibung</mat-label>
    <textarea
      matInput
      formControlName="description"
      placeholder="Detaillierte Beschreibung des Vorfalls"
      maxlength="2000"
      rows="4"
      aria-label="Beschreibung des Vorfalls">
    </textarea>
    <mat-hint align="end">{{ editForm.get('description')?.value?.length || 0 }}/2000</mat-hint>
    <mat-error *ngIf="hasFieldError('description')">
      {{ getFieldError('description') }}
    </mat-error>
  </mat-form-field>

  <!-- Date and Time Fields Row -->
  <div class="date-time-row">
    <!-- Start Time -->
    <div class="datetime-field">
      <label class="field-label">Startzeit *</label>
      <app-datetime-picker
        formControlName="startTime"
        [required]="true"
        dateLabel="Datum"
        timeLabel="Uhrzeit"
        datePlaceholder="TT.MM.JJJJ"
        timePlaceholder="HH:MM"
        dateAriaLabel="Startdatum auswählen"
        timeAriaLabel="Startzeit eingeben"
        requiredErrorMessage="Startzeit ist erforderlich"
        invalidDateErrorMessage="Ungültiges Startdatum"
        invalidTimeErrorMessage="Ungültige Startzeit (Format: HH:MM)">
      </app-datetime-picker>
      <div class="error-message" *ngIf="hasFieldError('startTime')">
        {{ getFieldError('startTime') }}
      </div>
    </div>

    <!-- Planned End Time -->
    <div class="datetime-field">
      <label class="field-label optional">Geplante Endzeit</label>
      <app-datetime-picker
        formControlName="plannedEndTime"
        [required]="false"
        dateLabel="Datum"
        timeLabel="Uhrzeit"
        datePlaceholder="TT.MM.JJJJ"
        timePlaceholder="HH:MM"
        dateAriaLabel="Geplantes Enddatum auswählen"
        timeAriaLabel="Geplante Endzeit eingeben"
        invalidDateErrorMessage="Ungültiges Enddatum"
        invalidTimeErrorMessage="Ungültige Endzeit (Format: HH:MM)">
      </app-datetime-picker>
      <div class="error-message" *ngIf="hasFieldError('plannedEndTime')">
        {{ getFieldError('plannedEndTime') }}
      </div>
    </div>
  </div>

  <!-- Actual End Time (only show if incident is resolved or being resolved) -->
  <div class="datetime-field full-width" *ngIf="editForm.get('isResolved')?.value">
    <label class="field-label">Tatsächliche Endzeit</label>
    <app-datetime-picker
      formControlName="actualEndTime"
      [required]="false"
      dateLabel="Datum"
      timeLabel="Uhrzeit"
      datePlaceholder="TT.MM.JJJJ"
      timePlaceholder="HH:MM"
      dateAriaLabel="Tatsächliches Enddatum auswählen"
      timeAriaLabel="Tatsächliche Endzeit eingeben"
      invalidDateErrorMessage="Ungültiges Enddatum"
      invalidTimeErrorMessage="Ungültige Endzeit (Format: HH:MM)">
    </app-datetime-picker>
    <div class="error-message" *ngIf="hasFieldError('actualEndTime')">
      {{ getFieldError('actualEndTime') }}
    </div>
  </div>

  <!-- Alternatives Field -->
  <mat-form-field appearance="outline" class="full-width">
    <mat-label>Alternativen/Workarounds</mat-label>
    <textarea
      matInput
      formControlName="alternatives"
      placeholder="Verfügbare Alternativen oder Workarounds beschreiben"
      maxlength="1000"
      rows="3"
      aria-label="Alternativen oder Workarounds">
    </textarea>
    <mat-hint align="end">{{ editForm.get('alternatives')?.value?.length || 0 }}/1000</mat-hint>
    <mat-error *ngIf="hasFieldError('alternatives')">
      {{ getFieldError('alternatives') }}
    </mat-error>
  </mat-form-field>

  <!-- Applications Selector -->
  <div class="applications-section">
    <label class="section-label">Betroffene Anwendungen *</label>
    <app-application-selector
      formControlName="applicationIds"
      [required]="true"
      placeholder="Betroffene Anwendungen auswählen..."
      aria-describedby="applications-error">
    </app-application-selector>
    <div class="error-message" id="applications-error" *ngIf="hasApplicationError">
      {{ getFieldError('applicationIds') }}
    </div>
  </div>

  <!-- Resolution Status -->
  <div class="resolution-section">
    <mat-checkbox
      formControlName="isResolved"
      color="primary"
      aria-label="Vorfall als gelöst markieren">
      Vorfall als gelöst markieren
    </mat-checkbox>
  </div>

  <!-- Form Actions -->
  <div class="form-actions">
    <button 
      type="button" 
      mat-button 
      (click)="onCancel()"
      [disabled]="loading"
      aria-label="Bearbeitung abbrechen">
      Abbrechen
    </button>
    
    <button 
      type="submit" 
      mat-raised-button 
      color="primary"
      [disabled]="!isFormValid || loading"
      aria-label="Änderungen speichern">
      <mat-icon *ngIf="loading">hourglass_empty</mat-icon>
      <span *ngIf="!loading">Speichern</span>
      <span *ngIf="loading">Wird gespeichert...</span>
    </button>
  </div>
</form>