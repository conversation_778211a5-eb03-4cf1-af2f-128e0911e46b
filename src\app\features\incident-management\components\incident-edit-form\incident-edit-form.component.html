<form [formGroup]="editForm" (ngSubmit)="onSubmit()" class="incident-edit-form">
  <!-- Title Field -->
  <mat-form-field appearance="outline" class="full-width">
    <mat-label>Titel *</mat-label>
    <input 
      matInput 
      formControlName="title"
      placeholder="Titel des Vorfalls eingeben"
      [disabled]="disabled"
      maxlength="200"
      aria-label="Titel des Vorfalls">
    <mat-hint align="end">{{ editForm.get('title')?.value?.length || 0 }}/200</mat-hint>
    <mat-error *ngIf="hasFieldError('title')">
      {{ getFieldError('title') }}
    </mat-error>
  </mat-form-field>

  <!-- Type Field -->
  <mat-form-field appearance="outline" class="full-width">
    <mat-label>Typ *</mat-label>
    <mat-select 
      formControlName="type"
      [disabled]="disabled"
      aria-label="Typ des Vorfalls">
      <mat-option 
        *ngFor="let type of incidentTypes" 
        [value]="type.value"
        [attr.aria-label]="'Typ auswählen: ' + type.label">
        {{ type.label }}
      </mat-option>
    </mat-select>
    <mat-error *ngIf="hasFieldError('type')">
      {{ getFieldError('type') }}
    </mat-error>
  </mat-form-field>

  <!-- Description Field -->
  <mat-form-field appearance="outline" class="full-width">
    <mat-label>Beschreibung</mat-label>
    <textarea 
      matInput 
      formControlName="description"
      placeholder="Detaillierte Beschreibung des Vorfalls"
      [disabled]="disabled"
      maxlength="2000"
      rows="4"
      aria-label="Beschreibung des Vorfalls">
    </textarea>
    <mat-hint align="end">{{ editForm.get('description')?.value?.length || 0 }}/2000</mat-hint>
    <mat-error *ngIf="hasFieldError('description')">
      {{ getFieldError('description') }}
    </mat-error>
  </mat-form-field>

  <!-- Date and Time Fields Row -->
  <div class="date-time-row">
    <!-- Start Time -->
    <mat-form-field appearance="outline" class="date-field">
      <mat-label>Startzeit *</mat-label>
      <input 
        matInput 
        [matDatepicker]="startPicker"
        formControlName="startTime"
        [disabled]="disabled"
        placeholder="Startzeit auswählen"
        aria-label="Startzeit des Vorfalls">
      <mat-datepicker-toggle matIconSuffix [for]="startPicker"></mat-datepicker-toggle>
      <mat-datepicker #startPicker></mat-datepicker>
      <mat-error *ngIf="hasFieldError('startTime')">
        {{ getFieldError('startTime') }}
      </mat-error>
    </mat-form-field>

    <!-- Planned End Time -->
    <mat-form-field appearance="outline" class="date-field">
      <mat-label>Geplante Endzeit</mat-label>
      <input 
        matInput 
        [matDatepicker]="plannedEndPicker"
        formControlName="plannedEndTime"
        [disabled]="disabled"
        placeholder="Geplante Endzeit auswählen"
        aria-label="Geplante Endzeit des Vorfalls">
      <mat-datepicker-toggle matIconSuffix [for]="plannedEndPicker"></mat-datepicker-toggle>
      <mat-datepicker #plannedEndPicker></mat-datepicker>
      <mat-error *ngIf="hasFieldError('plannedEndTime')">
        {{ getFieldError('plannedEndTime') }}
      </mat-error>
    </mat-form-field>
  </div>

  <!-- Actual End Time (only show if incident is resolved or being resolved) -->
  <mat-form-field 
    appearance="outline" 
    class="full-width"
    *ngIf="editForm.get('isResolved')?.value">
    <mat-label>Tatsächliche Endzeit</mat-label>
    <input 
      matInput 
      [matDatepicker]="actualEndPicker"
      formControlName="actualEndTime"
      [disabled]="disabled"
      placeholder="Tatsächliche Endzeit auswählen"
      aria-label="Tatsächliche Endzeit des Vorfalls">
    <mat-datepicker-toggle matIconSuffix [for]="actualEndPicker"></mat-datepicker-toggle>
    <mat-datepicker #actualEndPicker></mat-datepicker>
    <mat-error *ngIf="hasFieldError('actualEndTime')">
      {{ getFieldError('actualEndTime') }}
    </mat-error>
  </mat-form-field>

  <!-- Alternatives Field -->
  <mat-form-field appearance="outline" class="full-width">
    <mat-label>Alternativen/Workarounds</mat-label>
    <textarea 
      matInput 
      formControlName="alternatives"
      placeholder="Verfügbare Alternativen oder Workarounds beschreiben"
      [disabled]="disabled"
      maxlength="1000"
      rows="3"
      aria-label="Alternativen oder Workarounds">
    </textarea>
    <mat-hint align="end">{{ editForm.get('alternatives')?.value?.length || 0 }}/1000</mat-hint>
    <mat-error *ngIf="hasFieldError('alternatives')">
      {{ getFieldError('alternatives') }}
    </mat-error>
  </mat-form-field>

  <!-- Applications Selector -->
  <div class="applications-section">
    <label class="section-label">Betroffene Anwendungen *</label>
    <app-application-selector
      formControlName="applicationIds"
      [required]="true"
      placeholder="Betroffene Anwendungen auswählen..."
      aria-describedby="applications-error">
    </app-application-selector>
    <div class="error-message" id="applications-error" *ngIf="hasApplicationError">
      {{ getFieldError('applicationIds') }}
    </div>
  </div>

  <!-- Resolution Status -->
  <div class="resolution-section">
    <mat-checkbox 
      formControlName="isResolved"
      [disabled]="disabled"
      color="primary"
      aria-label="Vorfall als gelöst markieren">
      Vorfall als gelöst markieren
    </mat-checkbox>
  </div>

  <!-- Form Actions -->
  <div class="form-actions">
    <button 
      type="button" 
      mat-button 
      (click)="onCancel()"
      [disabled]="loading"
      aria-label="Bearbeitung abbrechen">
      Abbrechen
    </button>
    
    <button 
      type="submit" 
      mat-raised-button 
      color="primary"
      [disabled]="!isFormValid || loading"
      aria-label="Änderungen speichern">
      <mat-icon *ngIf="loading">hourglass_empty</mat-icon>
      <span *ngIf="!loading">Speichern</span>
      <span *ngIf="loading">Wird gespeichert...</span>
    </button>
  </div>
</form>