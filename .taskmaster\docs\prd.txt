# Product Requirements Document: Uhrzeit-Erfassung in Incident-Dialogen

## Übersicht
Erweitere die bestehenden Incident-Dialoge im StoerungsBuddy Frontend um die Erfassung und Anzeige von Uhrzeiten zusätzlich zu den bereits vorhandenen Datums-Feldern.

## Hintergrund
Aktuell erfassen die Incident-Dialoge nur Datums-Informationen für startTime, plannedEndTime und actualEndTime. Für eine präzisere Incident-Verwaltung ist es notwendig, auch die genaue Uhrzeit zu erfassen und anzuzeigen.

## Ziele
- Benutzer können bei der Erstellung und Bearbeitung von Incidents sowohl Datum als auch Uhrzeit eingeben
- Alle Zeitangaben werden mit vollständiger Datum-Zeit-Information gespeichert und angezeigt
- Die bestehende Funktionalität bleibt vollständig erhalten
- Konsistente Darstellung von Datum und Uhrzeit in allen Incident-Ansichten

## Funktionale Anforderungen

### 1. Incident Create Dialog Erweiterung
- Erweitere IncidentCreateFormComponent um Uhrzeit-Eingabe für startTime und plannedEndTime
- Verwende Angular Material Datetime-Picker oder separate Time-Picker
- Validierung für sinnvolle Zeitangaben (plannedEndTime nach startTime)

### 2. Incident Edit Dialog Erweiterung  
- Erweitere IncidentEditFormComponent um Uhrzeit-Eingabe für alle Zeit-Felder
- Zeige bestehende Uhrzeiten korrekt an beim Laden des Formulars
- Ermögliche Bearbeitung von actualEndTime mit Datum und Uhrzeit

### 3. Incident Detail View Erweiterung
- Zeige in IncidentDetailComponent alle Zeitangaben mit Datum und Uhrzeit an
- Aktualisiere die Dauer-Berechnung um Uhrzeiten zu berücksichtigen
- Formatiere Zeitangaben benutzerfreundlich (z.B. "27.06.2025, 16:30")

### 4. Incident List View Erweiterung
- Zeige in IncidentListComponent Uhrzeiten in der Tabelle an
- Aktualisiere Sortierung um vollständige Datum-Zeit-Information zu nutzen
- Kompakte Darstellung für Listenansicht (z.B. "27.06. 16:30")

### 5. Dashboard Erweiterung
- Aktualisiere ActiveIncidentsListComponent um Uhrzeiten anzuzeigen
- Zeige in der Dashboard-Übersicht Uhrzeiten für aktuelle Incidents

## Technische Anforderungen

### Frontend (Angular)
- Verwende Angular Material DateTimePicker oder kombiniere DatePicker mit TimePicker
- Aktualisiere alle FormControls für Datum-Zeit-Eingabe
- Implementiere Validatoren für Datum-Zeit-Kombinationen
- Aktualisiere Pipes für Datum-Zeit-Formatierung

### Datenmodell
- Stelle sicher, dass Incident.model.ts vollständige DateTime-Objekte unterstützt
- Aktualisiere GraphQL-Schemas falls notwendig
- Prüfe Backend-Kompatibilität für DateTime-Formate

### State Management (NgRx)
- Aktualisiere Incident-Actions für DateTime-Handling
- Prüfe Selectors für korrekte Datum-Zeit-Verarbeitung
- Aktualisiere Effects für API-Calls mit DateTime-Daten

## UI/UX Anforderungen
- Intuitive Datum-Zeit-Eingabe mit klarer Trennung zwischen Datum und Uhrzeit
- Konsistente Formatierung in deutscher Lokalisierung
- Responsive Design für mobile Geräte
- Accessibility-konforme Implementierung

## Akzeptanzkriterien
1. Benutzer können bei Incident-Erstellung Datum und Uhrzeit für Start- und geplante Endzeit eingeben
2. Benutzer können bei Incident-Bearbeitung alle Zeitfelder mit Datum und Uhrzeit bearbeiten
3. Alle Incident-Ansichten zeigen vollständige Datum-Zeit-Informationen an
4. Dauer-Berechnungen berücksichtigen Uhrzeiten korrekt
5. Bestehende Incidents ohne Uhrzeiten werden korrekt behandelt (Fallback auf 00:00)
6. Validierung verhindert ungültige Datum-Zeit-Kombinationen
7. Mobile Ansicht funktioniert einwandfrei mit neuen Eingabefeldern

## Technische Constraints
- Angular 19 mit Standalone Components
- Angular Material für UI-Komponenten
- NgRx für State Management
- TypeScript mit strikter Typisierung
- SCSS für Styling
- Kompatibilität mit bestehendem GraphQL-Backend

## Ausgeschlossen
- Zeitzone-Verwaltung (verwende lokale Zeit)
- Automatische Zeiterfassung
- Erinnerungen oder Benachrichtigungen
- Import/Export von Zeitdaten