import { Component, ChangeDetectionStrategy, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Observable, Subject } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';
import { Actions, ofType } from '@ngrx/effects';

import { ApplicationsFacade } from '../../../../store/applications/applications.facade';
import { ApplicationCreateFormComponent } from '../../components/application-create-form/application-create-form.component';
import { CreateApplicationInput } from '../../../../core/models/application.model';
import * as ApplicationActions from '../../../../store/applications/applications.actions';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

@Component({
  selector: 'app-application-create-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatIconModule,
    MatTooltipModule,
    ApplicationCreateFormComponent,
    MatSnackBarModule
  ],
  templateUrl: './application-create-dialog.component.html',
  styleUrls: ['./application-create-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ApplicationCreateDialogComponent implements OnInit, OnDestroy {
  loading$: Observable<boolean>;
  error$: Observable<any>;
  private destroy$ = new Subject<void>();

  constructor(
    private dialogRef: MatDialogRef<ApplicationCreateDialogComponent>,
    private applicationsFacade: ApplicationsFacade,
    private actions$: Actions,
    private snackBar: MatSnackBar
  ) {
    this.loading$ = this.applicationsFacade.loading$;
    this.error$ = this.applicationsFacade.error$;
  }

  ngOnInit(): void {
    this.actions$.pipe(
      ofType(ApplicationActions.createApplicationSuccess),
      takeUntil(this.destroy$)
    ).subscribe(() => {
      this.snackBar.open('Application created successfully.', 'OK', { duration: 3000 });
      this.dialogRef.close(true);
    });

    this.actions$.pipe(
      ofType(ApplicationActions.createApplicationFailure),
      takeUntil(this.destroy$)
    ).subscribe(({ error }) => {
       this.snackBar.open(`Error creating application: ${error.message}`, 'OK', {
         duration: 5000,
         panelClass: ['error-snackbar']
       });
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }

  onSubmit(application: CreateApplicationInput): void {
    this.applicationsFacade.createApplication(application);
  }
}