import { createFeatureSelector, createSelector } from '@ngrx/store';
import { IncidentsState } from './incidents.state';
import { IncidentType } from '../../core/models/incident.model';

export const selectIncidentsState = createFeatureSelector<IncidentsState>('incidents');

export const selectAllIncidents = createSelector(
  selectIncidentsState,
  (state: IncidentsState) => state.incidents
);

export const selectIncidentsLoading = createSelector(
  selectIncidentsState,
  (state: IncidentsState) => state.isLoading
);

export const selectIncidentsError = createSelector(
  selectIncidentsState,
  (state: IncidentsState) => state.error
);

export const selectIncidentsCreating = createSelector(
  selectIncidentsState,
  (state: IncidentsState) => state.isCreating
);

export const selectIncidentsCreateError = createSelector(
  selectIncidentsState,
  (state: IncidentsState) => state.createError
);

export const selectIncidentsUpdating = createSelector(
  selectIncidentsState,
  (state: IncidentsState) => state.isUpdating
);

export const selectIncidentsUpdateError = createSelector(
  selectIncidentsState,
  (state: IncidentsState) => state.updateError
);

export const selectIncidentsDeleting = createSelector(
  selectIncidentsState,
  (state: IncidentsState) => state.isDeleting
);

export const selectIncidentsDeleteError = createSelector(
  selectIncidentsState,
  (state: IncidentsState) => state.deleteError
);

export const selectResolvedIncidents = createSelector(
  selectAllIncidents,
  (incidents) => incidents.filter(incident => incident.isResolved)
);

export const selectUnresolvedIncidents = createSelector(
  selectAllIncidents,
  (incidents) => incidents.filter(incident => !incident.isResolved)
);

export const selectIncidentsByType = (type: IncidentType) => createSelector(
  selectAllIncidents,
  (incidents) => incidents.filter(incident => incident.type === type)
);

export const selectSelectedIncident = createSelector(
  selectIncidentsState,
  (state: IncidentsState) => state.selectedIncident
);

export const selectSelectedIncidentLoading = createSelector(
  selectIncidentsState,
  (state: IncidentsState) => state.selectedIncidentLoading
);

export const selectSelectedIncidentError = createSelector(
  selectIncidentsState,
  (state: IncidentsState) => state.selectedIncidentError
);

export const selectIncidentsStatus = createSelector(
  selectAllIncidents,
  selectIncidentsLoading,
  selectIncidentsError,
  (incidents, isLoading, error) => ({
    incidents,
    isLoading,
    hasError: !!error,
    error
  })
);