import { gql } from 'apollo-angular';

export const UPDATE_INCIDENT = gql`
  mutation UpdateIncident($input: UpdateIncidentInput!) {
    updateIncident(input: $input) {
      identifier
      title
      type
      description
      startTime
      plannedEndTime
      actualEndTime
      alternatives
      isResolved
      createdAt
      updatedAt
      applications {
        identifier
        name
        description
        isDeleted
        createdAt
        updatedAt
      }
    }
  }
`;

export const CREATE_INCIDENT = gql`
  mutation CreateIncident($input: CreateIncidentInput!) {
    createIncident(input: $input) {
      identifier
      title
      type
      description
      startTime
      plannedEndTime
      actualEndTime
      alternatives
      isResolved
      createdAt
      updatedAt
      applications {
        identifier
        name
        description
        isDeleted
        createdAt
        updatedAt
      }
    }
  }
`;

export const RESOLVE_INCIDENT = gql`
  mutation ResolveIncident($identifier: UUID!, $actualEndTime: DateTime!) {
    resolveIncident(identifier: $identifier, actualEndTime: $actualEndTime) {
      identifier
      title
      type
      description
      startTime
      plannedEndTime
      actualEndTime
      alternatives
      isResolved
      createdAt
      updatedAt
      applications {
        identifier
        name
        description
        isDeleted
        createdAt
        updatedAt
      }
    }
  }
`;

export const DELETE_INCIDENT = gql`
  mutation DeleteIncident($identifier: UUID!) {
    deleteIncident(identifier: $identifier)
  }
`;