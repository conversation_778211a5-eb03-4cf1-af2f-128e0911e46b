<div class="application-list-container">
  <!-- Header mit Titel und Add-Button -->
  <div class="list-header">
    <h2>Applikationsverwaltung</h2>
    <button mat-raised-button color="primary" class="modern-button-base" (click)="openAddDialog()" [disabled]="loading$ | async">
      <mat-icon>add</mat-icon>
      Neue Applikation
    </button>
  </div>

  <!-- Filter und Suche -->
  <app-application-filter
    (filterChange)="onFilterChange($event)"
    (searchChange)="onSearchChange($event)">
  </app-application-filter>

  <!-- Results Summary -->
  <div *ngIf="!(loading$ | async) && dataSource.data.length > 0" class="results-summary">
    <mat-icon>info</mat-icon>
    <span>{{ getResultsSummaryText() }}</span>
  </div>

  <!-- Error Message -->
  <div *ngIf="(error$ | async) && !(loading$ | async)" class="error-container">
    <mat-icon color="warn">error</mat-icon>
    <span>{{ (error$ | async)?.message || '<PERSON> ist aufgetreten.' }}</span>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="loading$ | async" class="loading-container">
    <mat-spinner diameter="50"></mat-spinner>
    <p>Lade Applikationen...</p>
  </div>

  <!-- Tabelle -->
  <div class="table-container" *ngIf="!(loading$ | async)">
    <table mat-table [dataSource]="dataSource" matSort class="application-table">
      
      <!-- Name Column -->
      <ng-container matColumnDef="name">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
        <td mat-cell *matCellDef="let application">
          <a [routerLink]="[application.identifier]" class="application-link">
            {{ application.name }}
          </a>
        </td>
      </ng-container>

      <!-- Description Column -->
      <ng-container matColumnDef="description">
        <th mat-header-cell *matHeaderCellDef>Beschreibung</th>
        <td mat-cell *matCellDef="let application">
          <span [matTooltip]="application.description || 'Keine Beschreibung'"
                class="description-text">
            {{ application.description || '-' }}
          </span>
        </td>
      </ng-container>

      <!-- Status Column -->
      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef>Status</th>
        <td mat-cell *matCellDef="let application">
          <mat-chip [color]="getStatusChipColor(application.isDeleted)" selected>
            {{ getStatusText(application.isDeleted) }}
          </mat-chip>
        </td>
      </ng-container>

      <!-- Created At Column -->
      <ng-container matColumnDef="createdAt">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Erstellt</th>
        <td mat-cell *matCellDef="let application">
          {{ formatDate(application.createdAt) }}
        </td>
      </ng-container>

      <!-- Updated At Column -->
      <ng-container matColumnDef="updatedAt">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Aktualisiert</th>
        <td mat-cell *matCellDef="let application">
          {{ formatDate(application.updatedAt) }}
        </td>
      </ng-container>

      <!-- Actions Column -->
      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef>Aktionen</th>
        <td mat-cell *matCellDef="let application">
          <div class="action-buttons">
            <button mat-icon-button
                    class="modern-button-base"
                    [matTooltip]="'Details anzeigen'"
                    [routerLink]="[application.identifier]">
              <mat-icon>visibility</mat-icon>
            </button>
            
            <button mat-icon-button
                    class="modern-button-base"
                    [matTooltip]="'Bearbeiten'"
                    (click)="openEditDialog(application)"
                    [disabled]="application.isDeleted">
              <mat-icon>edit</mat-icon>
            </button>
            
            <button mat-icon-button
                    class="modern-button-base"
                    [matTooltip]="'Löschen'"
                    (click)="openDeleteDialog(application)"
                    [disabled]="application.isDeleted"
                    color="warn">
              <mat-icon>delete</mat-icon>
            </button>
          </div>
        </td>
      </ng-container>

      <!-- Table Header and Rows -->
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"
          [class.deleted-row]="row.isDeleted"></tr>
    </table>

    <!-- No Data Message -->
    <div *ngIf="dataSource.data.length === 0 && !(loading$ | async)" class="no-data-container">
      <mat-icon>inbox</mat-icon>
      <h3>Keine Applikationen gefunden</h3>
      <p>Es wurden keine Applikationen gefunden, die Ihren Suchkriterien entsprechen.</p>
      <button mat-raised-button color="primary" class="modern-button-base" (click)="openAddDialog()">
        <mat-icon>add</mat-icon>
        Erste Applikation erstellen
      </button>
    </div>

    <!-- Paginator -->
    <mat-paginator
      *ngIf="dataSource.data.length > 0"
      [pageSizeOptions]="[10, 25, 50, 100]"
      [pageSize]="25"
      showFirstLastButtons>
    </mat-paginator>
  </div>
</div>