import { createReducer, on } from '@ngrx/store';
import { IncidentsState, initialIncidentsState } from './incidents.state';
import * as IncidentsActions from './incidents.actions';

export const incidentsReducer = createReducer(
  initialIncidentsState,

  // Load All Incidents Actions
  on(IncidentsActions.loadIncidents, (state) => ({
    ...state,
    isLoading: true,
    error: null
  })),

  on(IncidentsActions.loadIncidentsSuccess, (state, { incidents }) => ({
    ...state,
    incidents,
    isLoading: false,
    error: null
  })),

  on(IncidentsActions.loadIncidentsFailure, (state, { error }) => ({
    ...state,
    isLoading: false,
    error
  })),

  // Load My Incidents Actions
  on(IncidentsActions.loadMyIncidents, (state) => ({
    ...state,
    isLoading: true,
    error: null
  })),

  on(IncidentsActions.loadMyIncidentsSuccess, (state, { incidents }) => ({
    ...state,
    incidents,
    isLoading: false,
    error: null
  })),

  on(IncidentsActions.loadMyIncidentsFailure, (state, { error }) => ({
    ...state,
    isLoading: false,
    error
  })),

  // Create Incident Actions
  on(IncidentsActions.createIncident, (state) => ({
    ...state,
    isCreating: true,
    createError: null
  })),

  on(IncidentsActions.createIncidentSuccess, (state, { incident }) => ({
    ...state,
    incidents: [...state.incidents, incident],
    isCreating: false,
    createError: null
  })),

  on(IncidentsActions.createIncidentFailure, (state, { error }) => ({
    ...state,
    isCreating: false,
    createError: error
  })),

  // Update Incident Actions
  on(IncidentsActions.updateIncident, (state) => ({
    ...state,
    isUpdating: true,
    updateError: null
  })),

  on(IncidentsActions.updateIncidentSuccess, (state, { incident }) => ({
    ...state,
    incidents: state.incidents.map(i =>
      i.identifier === incident.identifier ? incident : i
    ),
    isUpdating: false,
    updateError: null
  })),

  on(IncidentsActions.updateIncidentFailure, (state, { error }) => ({
    ...state,
    isUpdating: false,
    updateError: error
  })),

  // Delete Incident Actions
  on(IncidentsActions.deleteIncident, (state) => ({
    ...state,
    isDeleting: true,
    deleteError: null
  })),

  on(IncidentsActions.deleteIncidentSuccess, (state, { identifier }) => ({
    ...state,
    incidents: state.incidents.filter(i => i.identifier !== identifier),
    isDeleting: false,
    deleteError: null
  })),

  on(IncidentsActions.deleteIncidentFailure, (state, { error }) => ({
    ...state,
    isDeleting: false,
    deleteError: error
  })),

  // Load Single Incident Actions
  on(IncidentsActions.loadIncident, (state) => ({
    ...state,
    selectedIncidentLoading: true,
    selectedIncidentError: null
  })),

  on(IncidentsActions.loadIncidentSuccess, (state, { incident }) => ({
    ...state,
    selectedIncident: incident,
    selectedIncidentLoading: false,
    selectedIncidentError: null
  })),

  on(IncidentsActions.loadIncidentFailure, (state, { error }) => ({
    ...state,
    selectedIncidentLoading: false,
    selectedIncidentError: error
  })),

  // Clear Error
  on(IncidentsActions.clearIncidentsError, (state) => ({
    ...state,
    error: null,
    createError: null,
    updateError: null,
    deleteError: null,
    selectedIncidentError: null
  }))
);