import { TestBed } from '@angular/core/testing';
import { Apollo } from 'apollo-angular';
import { of, throwError } from 'rxjs';
import { ApolloQueryResult } from '@apollo/client/core';

import { IncidentService } from './incident.service';
import { Incident, IncidentType, UpdateIncidentInput } from '../models/incident.model';
import { GET_ALL_INCIDENTS, GET_MY_INCIDENTS } from '../graphql/incident.queries';
import { UPDATE_INCIDENT } from '../graphql/incident.mutations';
import { UpdateIncidentResponse, IncidentResponse } from '../graphql/types';

describe('IncidentService', () => {
  let service: IncidentService;
  let apolloSpy: jasmine.SpyObj<Apollo>;

  beforeEach(() => {
    const spy = jasmine.createSpyObj('Apollo', ['query', 'mutate']);

    TestBed.configureTestingModule({
      providers: [
        IncidentService,
        { provide: Apollo, useValue: spy }
      ]
    });

    service = TestBed.inject(IncidentService);
    apolloSpy = TestBed.inject(Apollo) as jasmine.SpyObj<Apollo>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAllIncidents', () => {
    it('should call getAllIncidents with correct query', () => {
      // Arrange
      const mockIncidents: Incident[] = [
        {
          identifier: 'incident-1',
          title: 'System Outage',
          type: IncidentType.STOERUNG,
          description: 'Major system outage',
          startTime: '2024-06-20T10:00:00Z',
          plannedEndTime: '2024-06-20T14:00:00Z',
          isResolved: false,
          createdAt: '2024-06-20T09:30:00Z',
          updatedAt: '2024-06-20T09:30:00Z',
          applications: [
            {
              identifier: 'app-1',
              name: 'Customer Portal',
              description: 'Customer facing portal',
              isDeleted: false,
              createdAt: '2024-01-01T00:00:00Z',
              updatedAt: '2024-01-01T00:00:00Z'
            }
          ]
        }
      ];

      const expectedResponse: ApolloQueryResult<{ incidents: Incident[] }> = {
        data: {
          incidents: mockIncidents
        },
        loading: false,
        networkStatus: 7
      };

      apolloSpy.query.and.returnValue(of(expectedResponse));

      // Act
      let result: Incident[] | undefined;
      service.getAllIncidents().subscribe(incidents => {
        result = incidents;
      });

      // Assert
      expect(apolloSpy.query).toHaveBeenCalledWith({
        query: GET_ALL_INCIDENTS
      });
      expect(result).toEqual(mockIncidents);
    });
  });

  describe('getMyIncidents', () => {
    it('should call getMyIncidents with isResolved parameter', () => {
      // Arrange
      const isResolved = true;
      const mockIncidents: Incident[] = [
        {
          identifier: 'incident-2',
          title: 'Maintenance Window',
          type: IncidentType.WARTUNGSFENSTER,
          description: 'Scheduled maintenance',
          startTime: '2024-06-15T22:00:00Z',
          plannedEndTime: '2024-06-16T02:00:00Z',
          actualEndTime: '2024-06-16T01:30:00Z',
          isResolved: true,
          createdAt: '2024-06-14T10:00:00Z',
          updatedAt: '2024-06-16T01:30:00Z',
          applications: [
            {
              identifier: 'app-2',
              name: 'Admin Dashboard',
              description: 'Admin management interface',
              isDeleted: false,
              createdAt: '2024-01-01T00:00:00Z',
              updatedAt: '2024-01-01T00:00:00Z'
            }
          ]
        }
      ];

      const expectedResponse: ApolloQueryResult<{ myIncidents: Incident[] }> = {
        data: {
          myIncidents: mockIncidents
        },
        loading: false,
        networkStatus: 7
      };

      apolloSpy.query.and.returnValue(of(expectedResponse));

      // Act
      let result: Incident[] | undefined;
      service.getMyIncidents(isResolved).subscribe(incidents => {
        result = incidents;
      });

      // Assert
      expect(apolloSpy.query).toHaveBeenCalledWith({
        query: GET_MY_INCIDENTS,
        variables: { isResolved },
        fetchPolicy: 'network-only'
      });
      expect(result).toEqual(mockIncidents);
    });

    it('should call getMyIncidents without isResolved parameter', () => {
      // Arrange
      const mockIncidents: Incident[] = [
        {
          identifier: 'incident-1',
          title: 'System Outage',
          type: IncidentType.STOERUNG,
          description: 'Major system outage',
          startTime: '2024-06-20T10:00:00Z',
          plannedEndTime: '2024-06-20T14:00:00Z',
          isResolved: false,
          createdAt: '2024-06-20T09:30:00Z',
          updatedAt: '2024-06-20T09:30:00Z',
          applications: []
        },
        {
          identifier: 'incident-2',
          title: 'Maintenance Window',
          type: IncidentType.WARTUNGSFENSTER,
          description: 'Scheduled maintenance',
          startTime: '2024-06-15T22:00:00Z',
          plannedEndTime: '2024-06-16T02:00:00Z',
          actualEndTime: '2024-06-16T01:30:00Z',
          isResolved: true,
          createdAt: '2024-06-14T10:00:00Z',
          updatedAt: '2024-06-16T01:30:00Z',
          applications: []
        }
      ];

      const expectedResponse: ApolloQueryResult<{ myIncidents: Incident[] }> = {
        data: {
          myIncidents: mockIncidents
        },
        loading: false,
        networkStatus: 7
      };

      apolloSpy.query.and.returnValue(of(expectedResponse));

      // Act
      let result: Incident[] | undefined;
      service.getMyIncidents().subscribe(incidents => {
        result = incidents;
      });

      // Assert
      expect(apolloSpy.query).toHaveBeenCalledWith({
        query: GET_MY_INCIDENTS,
        variables: { isResolved: undefined },
        fetchPolicy: 'network-only'
      });
      expect(result).toEqual(mockIncidents);
    });
  });

  describe('updateIncident', () => {
    it('should call updateIncident with correct mutation and variables', () => {
      // Arrange
      const updateInput: UpdateIncidentInput = {
        identifier: 'incident-1',
        title: 'Updated System Outage',
        type: IncidentType.STOERUNG,
        description: 'Updated major system outage',
        startTime: '2024-06-20T10:00:00Z',
        plannedEndTime: '2024-06-20T15:00:00Z',
        actualEndTime: '2024-06-20T14:30:00Z',
        alternatives: 'Use backup system'
      };

      const mockUpdatedIncidentResponse: IncidentResponse = {
        identifier: 'incident-1',
        title: 'Updated System Outage',
        type: IncidentType.STOERUNG,
        description: 'Updated major system outage',
        startTime: '2024-06-20T10:00:00Z',
        plannedEndTime: '2024-06-20T15:00:00Z',
        actualEndTime: '2024-06-20T14:30:00Z',
        alternatives: 'Use backup system',
        isResolved: true,
        createdAt: '2024-06-20T09:30:00Z',
        updatedAt: '2024-06-20T14:30:00Z',
        applications: [
          {
            identifier: 'app-1',
            name: 'Customer Portal',
            description: 'Customer facing portal',
            isDeleted: false,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z'
          }
        ]
      };

      const expectedResponse: { data: UpdateIncidentResponse } = {
        data: {
          updateIncident: mockUpdatedIncidentResponse
        }
      };

      apolloSpy.mutate.and.returnValue(of(expectedResponse));

      // Act
      let result: Incident | undefined;
      service.updateIncident(updateInput).subscribe(incident => {
        result = incident;
      });

      // Assert
      expect(apolloSpy.mutate).toHaveBeenCalledWith({
        mutation: UPDATE_INCIDENT,
        variables: { input: updateInput }
      });
      expect(result).toBeDefined();
      expect(result!.identifier).toBe('incident-1');
      expect(result!.title).toBe('Updated System Outage');
    });

    it('should handle GraphQL errors properly', () => {
      // Arrange
      const updateInput: UpdateIncidentInput = {
        identifier: 'incident-1',
        title: 'Updated System Outage'
      };

      const mockError = {
        graphQLErrors: [{ message: 'Incident not found' }],
        networkError: null,
        message: 'GraphQL error: Incident not found'
      };

      apolloSpy.mutate.and.returnValue(throwError(() => mockError));

      // Act & Assert
      service.updateIncident(updateInput).subscribe({
        next: () => fail('Expected error'),
        error: (error) => {
          expect(error.message).toBe('Incident not found');
        }
      });

      expect(apolloSpy.mutate).toHaveBeenCalledWith({
        mutation: UPDATE_INCIDENT,
        variables: { input: updateInput }
      });
    });

    it('should handle network errors properly', () => {
      // Arrange
      const updateInput: UpdateIncidentInput = {
        identifier: 'incident-1',
        title: 'Updated System Outage'
      };

      const mockError = {
        graphQLErrors: [],
        networkError: { message: 'Network connection failed' },
        message: 'Network error'
      };

      apolloSpy.mutate.and.returnValue(throwError(() => mockError));

      // Act & Assert
      service.updateIncident(updateInput).subscribe({
        next: () => fail('Expected error'),
        error: (error) => {
          expect(error.message).toBe('Netzwerkfehler. Bitte versuchen Sie es später erneut.');
        }
      });

      expect(apolloSpy.mutate).toHaveBeenCalledWith({
        mutation: UPDATE_INCIDENT,
        variables: { input: updateInput }
      });
    });
  });
});