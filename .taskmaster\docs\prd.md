<context>
# Overview  
The "Störungs-Buddy" Frontend project is an administration UI designed to manage system master data, incident reports, and maintenance windows. It serves as a central platform for administrators to oversee and maintain system health and communication. The application aims to provide a professional, intuitive, and efficient interface for managing critical operational data.

# Core Features  

## F1: Incident and Maintenance Management
- **Description:** Create, edit, and resolve incident reports and maintenance windows.
- **Importance:** Central to the system's purpose, enabling administrators to track and manage system disruptions and planned downtimes.
- **High-level Functionality:** Users can log new incidents, update their status (e.g., resolved), and manage planned maintenance schedules. This includes defining incident types (Störung, Wartungsfenster, Keine Störung), associating them with applications, and tracking their lifecycle.

## F2: Application Management (Master Data)
- **Description:** Centralized management of applications in a non-hierarchical structure.
- **Importance:** Provides the foundational data for incident reporting, allowing incidents to be linked to specific applications.
- **High-level Functionality:** Administrators can add, edit, and remove applications, including their name, description, and status (e.g., deleted). This feature supports CRUD operations for application entities.

## F3: User Subscriptions
- **Description:** Management of user subscriptions to applications.
- **Importance:** Enables targeted communication of incidents and maintenance windows to relevant users.
- **High-level Functionality:** Administrators can view and manage which users are subscribed to which applications. Initial implementation assigns all applications to a user upon their first login.

## F4: Prioritization of Messages
- **Description:** Mark messages by importance (High-Critical and Normal).
- **Importance:** Helps administrators quickly identify and address critical issues.
- **High-level Functionality:** Incidents can be assigned a priority level, influencing their visibility and handling.

## F5: API Provisioning
- **Description:** Provide interfaces for the frontend.
- **Importance:** Ensures the frontend has access to all necessary data for display and interaction.
- **High-level Functionality:** Endpoints for current user subscriptions, all available systems, and current system messages for incidents and maintenance windows.

## F-AUTH-001: User Authentication
- **Description:** Secure administrator login via a login mask (email + password) with OAuth2 fallback for MVP.
- **Importance:** Secures access to the administration UI, ensuring only authorized personnel can manage system data.
- **High-level Functionality:** Users provide credentials, which are authenticated against a backend API. Successful login grants access and establishes a session using JWT tokens.

## F-AUTH-003: Logout Functionality
- **Description:** Secure logout and session cleanup.
- **Importance:** Prevents unauthorized access after a user has finished their session.
- **High-level Functionality:** A logout button in the navigation triggers session invalidation and redirects the user to the login page.

## F-DES-001: Corporate Design Integration
- **Description:** Implement and apply the Funk Corporate Design throughout the UI.
- **Importance:** Ensures a professional, consistent, and trustworthy visual identity for the system.
- **High-level Functionality:** Integration of Funk logo, consistent application of Funk color palette, and adherence to corporate design guidelines across all UI elements.

## F-DES-002: Color Scheme Implementation
- **Description:** Implement a consistent and accessible color scheme based on corporate guidelines.
- **Importance:** Enhances usability and accessibility, making the interface pleasant and easy to navigate.
- **High-level Functionality:** Defines primary, secondary, accent, and status colors with specific hex/RGB values, ensuring sufficient contrast for accessibility.

## F-DES-003: Typography and Fonts
- **Description:** Implement readable and hierarchically structured text.
- **Importance:** Improves information readability and comprehension.
- **High-level Functionality:** Defines consistent font families, sizes, weights, and line heights for all text elements, with responsive adjustments.

## F-DES-004: Layout Components
- **Description:** Implement a structured and intuitive layout.
- **Importance:** Facilitates quick orientation and efficient navigation within the system.
- **High-level Functionality:** Defines the layout of header, sidebar, main content area, and footer, with consistent spacing and responsive adaptations.

## F-DES-005: UI Component Styling
- **Description:** Ensure uniformly designed controls.
- **Importance:** Provides an intuitive and consistent user experience across all interactive elements.
- **High-level Functionality:** Specifies styling for buttons, form elements, tables, cards, and icons, including hover and focus states.

## F-DES-006: Status and Feedback Colors
- **Description:** Use colors to immediately indicate the status of elements.
- **Importance:** Allows users to quickly grasp critical information and system feedback.
- **High-level Functionality:** Defines specific colors for success, warning, error, and info states, applied consistently in notifications, badges, and indicators.

## F-DES-007: Responsive Design Adjustments
- **Description:** Ensure an optimal visual experience across all devices.
- **Importance:** Provides flexibility for users to work from various screen sizes.
- **High-level Functionality:** Mobile-first approach with adaptive layouts, typography, and touch targets for different breakpoints.

## F-DES-009: Accessibility in Design
- **Description:** Implement an accessible design for users with special needs.
- **Importance:** Ensures equal access and usability for all users.
- **High-level Functionality:** Adherence to WCAG 2.1 AA guidelines for contrast, color coding, focus indicators, and scalable font sizes.

</context>
<PRD>
# Technical Architecture  

## Framework
- **Framework**: Angular 19 (Standalone Components)
- **State Management**: NgRx Store + Effects
- **UI Framework**: Angular Material (Indigo-Pink Theme)
- **API Integration**: Apollo Client for GraphQL
- **Styling**: SCSS

## Data Models
The core data models include:
- **Application**: `identifier`, `name`, `description`, `isDeleted`, `createdAt`, `updatedAt`
- **Incident**: `identifier`, `title`, `description`, `type` (`STOERUNG`, `WARTUNGSFENSTER`, `KEINE_STOERUNG`), `startTime`, `plannedEndTime`, `actualEndTime`, `isResolved`, `applications` (list of Application), `alternatives`
- **User**: `identifier`, `email`, `displayName`, `role` (`USER`, `ADMIN`)
- **UserSubscription**: `identifier`, `userId`, `applicationId`, `application`, `isActive`
- **ChangeHistoryEntry**: `identifier`, `entityType`, `entityIdentifier`, `changedBy`, `changeDescription`, `changedAt`

## APIs and Integrations
- **GraphQL API**: Primary communication with the backend.
  - **Queries**: `allApplications`, `application(identifier: ID!)`, `applicationsByName(name: String!)`, `incidentsForCurrentUser(isResolved: Boolean)`, `currentUser`, `currentUserSubscriptions`, `currentUserSubscribedApplications`.
  - **Mutations**: `createApplication`, `updateApplication`, `deleteApplication`, `createIncident`, `updateIncident`, `resolveIncident`, `deleteIncident`, `subscribeToApplication`, `unsubscribeFromApplication`.
  - **Subscriptions**: `currentUserIncidentCreated`, `currentUserIncidentUpdated`, `currentUserIncidentResolved` for real-time updates.
- **Authentication API**: REST `POST /api/auth/login` for initial login, returning JWT token.
- **Interceptors**: `AuthInterceptor` to attach `Authorization: Bearer <token>` header to all HTTP requests.

## Architectural Principles
- **Component Architecture**: Standalone components, Smart/Container and Dumb/Presentational pattern, OnPush change detection.
- **State Management**: Unidirectional data flow, Actions, Reducers, Effects pattern, Selectors for state queries, Immutable state updates.
- **API Communication**: GraphQL-first approach, type-safe operations, centralized error handling, optimistic updates.
- **Code Quality**: Strict TypeScript, consistent naming, modular architecture, clean code.
- **Security**: Token-based authentication, input sanitization, XSS/CSRF prevention.
- **Performance**: Lazy loading, tree shaking, optimized builds, efficient change detection.

# Development Roadmap  

## MVP Requirements
The initial MVP focuses on core functionalities:
1.  **Project Initialization**: Setup of Angular 19, NgRx, Angular Material, Apollo Client.
2.  **Admin UI Foundation**: Basic layout (header, sidebar), navigation, and dashboard implementation.
3.  **Application Management**: Full CRUD operations for applications (list, create, edit, delete, details).
4.  **Incident Management**: Displaying incident list, creating new incidents, editing existing incidents, and status updates.
5.  **Authentication**: Secure login and logout functionality.
6.  **Core Design System**: Implementation of Funk Corporate Design colors, typography, and basic UI component styling.

## Future Enhancements
- **Extended Incident Management**: Archiving, bulk actions, real-time updates via GraphQL Subscriptions.
- **Advanced Authentication**: OAuth2 integration, refresh token rotation, secure cookie management.
- **Enhanced Design System**: Dark Mode, comprehensive accessibility features, detailed design system documentation.
- **Historical Data**: Display of past incidents with status history.
- **User Management**: Full user management features in conjunction with AD integration.

# Logical Dependency Chain
1.  **Project Initialization**: Foundation for everything.
2.  **Admin UI Foundation**: Provides the basic shell for the application.
3.  **Authentication**: Essential for securing access to the UI.
4.  **Corporate Design Integration**: Establishes the visual identity early on.
5.  **Application Management**: Provides master data necessary for incident management.
6.  **Incident Management**: Core business logic, dependent on applications and authentication.
7.  **UI Design Optimization**: Refinement of the UI based on initial implementation and corporate design.
8.  **Real-time Updates**: Enhances user experience after core features are in place.

# Risks and Mitigations  

## Technical Challenges
- **GraphQL Schema Changes**: Backend API changes can impact frontend.
  - **Mitigation**: Use TypeScript interfaces, API versioning, code-first approach, and schema validation.
- **Performance with Large Data Sets**: Potential slowdowns with many applications or incidents.
  - **Mitigation**: Implement pagination, virtual scrolling, lazy loading, and GraphQL query optimization.
- **Concurrent Modifications**: Multiple users editing the same data.
  - **Mitigation**: Implement optimistic locking and leverage change history for audit trails.
- **Token Security**: Secure storage and handling of JWT tokens.
  - **Mitigation**: Use HttpOnly cookies (future), secure flags, and robust token validation.
- **Browser Compatibility**: Inconsistent behavior across different browsers.
  - **Mitigation**: Extensive cross-browser testing and polyfills where necessary.

## Project Risks
- **API Not Available**: Backend API delays.
  - **Mitigation**: Implement mock data for frontend development, enabling parallel work.
- **Scope Creep**: Expanding features beyond initial plan.
  - **Mitigation**: Clear phase definitions and strict adherence to MVP scope.
- **Design Changes**: Frequent UI/UX revisions.
  - **Mitigation**: Component-based architecture and a well-defined design system to absorb changes efficiently.

# Appendix  

## GraphQL Schema (Current)
```graphql
enum IncidentType {
  STOERUNG
  WARTUNGSFENSTER
  KEINE_STOERUNG
}

enum UserRole {
  USER
  ADMIN
}

type Application {
  identifier: ID!
  name: String!
  description: String
  isDeleted: Boolean!
  createdAt: String!
  updatedAt: String!
}

type Incident {
  identifier: ID!
  title: String!
  type: IncidentType!
  description: String
  startTime: String!
  plannedEndTime: String
  actualEndTime: String
  isResolved: Boolean!
  alternatives: String
  applications: [Application!]
}

type User {
  identifier: ID!
  email: String!
  displayName: String!
  role: UserRole!
}

type UserSubscription {
  identifier: ID!
  userId: ID!
  applicationId: ID!
  application: Application!
  isActive: Boolean!
}

type Query {
  allApplications(isDeleted: Boolean = false): [Application!]
  application(identifier: ID!): Application
  applicationsByName(name: String!): [Application!]
  incidentsForCurrentUser(isResolved: Boolean): [Incident!]
  currentUser: User
  currentUserSubscriptions: [UserSubscription!]
  currentUserSubscribedApplications: [Application!]
}

type Mutation {
  subscribeToApplication(applicationId: ID!): UserSubscription
  unsubscribeFromApplication(applicationId: ID!): Boolean
  createIncident(input: CreateIncidentInput!): Incident!
  updateIncident(input: UpdateIncidentInput!): Incident!
  resolveIncident(identifier: ID!): Incident!
  deleteIncident(identifier: ID!): Boolean!
  createApplication(name: String!, description: String): Application!
  updateApplication(identifier: ID!, name: String, description: String, isDeleted: Boolean): Application!
  deleteApplication(identifier: ID!): Application
}

input CreateIncidentInput {
  title: String!
  type: IncidentType!
  description: String
  startTime: String!
  plannedEndTime: String
  alternatives: String
  applicationIds: [ID!]!
}

input UpdateIncidentInput {
  identifier: ID!
  title: String
  type: IncidentType
  description: String
  startTime: String
  plannedEndTime: String
  actualEndTime: String
  alternatives: String
  applicationIds: [ID!]
}

input CreateApplicationInput {
  name: String!
  description: String
}

type Subscription {
  currentUserIncidentCreated: Incident
  currentUserIncidentUpdated: Incident
  currentUserIncidentResolved: Incident
}
```

## Funk Corporate Design Color Palette
### Primary Colors
- `$funk-blue`: `#002D74` (RGB: 0, 45, 116)

### Secondary Colors
- `$funk-gray`: `#58585A` (RGB: 88, 88, 90)
- `$funk-light-gray`: `#D9DADB` (RGB: 217, 218, 219)

### Accent Colors
- `$funk-light-blue`: `#8EAEC8` (RGB: 142, 174, 200)
- `$funk-white`: `#FFFFFF` (RGB: 255, 255, 255)

### Status Colors
- `$success-color`: `#28a745` (Green)
- `$warning-color`: `#ffc107` (Yellow)
- `$error-color`: `#dc3545` (Red)
- `$info-color`: `#8EAEC8` (Light Blue)

## Typography System
- **Font Sizes**: `0.75rem` (12px) to `1.875rem` (30px)
- **Font Weights**: `300` (light) to `700` (bold)

## Spacing System
- **Spacing**: `0.25rem` (4px) to `3rem` (48px)
</PRD>