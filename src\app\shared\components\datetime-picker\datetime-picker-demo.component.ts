import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';

import { DateTimePickerComponent } from './datetime-picker.component';
import { formatDateTime, isPastDateTime, isFutureDateTime, calculateDuration, formatDuration } from '../../../core/utils/datetime.utils';

/**
 * Demo-Komponente für den DateTime-Picker
 * <PERSON>eigt verschiedene Verwendungsszenarien und Features
 */
@Component({
  selector: 'app-datetime-picker-demo',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatCardModule,
    MatButtonModule,
    MatDividerModule,
    MatCheckboxModule,
    MatFormFieldModule,
    MatInputModule,
    DateTimePickerComponent
  ],
  template: `
    <div class="demo-container">
      <h1>DateTime-Picker Demo</h1>
      
      <!-- Basic Usage -->
      <mat-card class="demo-card">
        <mat-card-header>
          <mat-card-title>Grundlegende Verwendung</mat-card-title>
          <mat-card-subtitle>Einfache DateTime-Eingabe mit ngModel</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <app-datetime-picker
            [(ngModel)]="basicDateTime"
            (dateTimeChange)="onBasicDateTimeChange($event)"
            dateLabel="Datum"
            timeLabel="Uhrzeit">
          </app-datetime-picker>
          
          <div class="result-display">
            <strong>Ausgewählter Wert:</strong> {{ formatBasicDateTime() }}<br>
            <strong>Status:</strong> {{ getDateTimeStatus(basicDateTime) }}
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Reactive Forms -->
      <mat-card class="demo-card">
        <mat-card-header>
          <mat-card-title>Reactive Forms Integration</mat-card-title>
          <mat-card-subtitle>Integration mit Angular Reactive Forms und Validierung</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <form [formGroup]="reactiveForm" class="reactive-form">
            <app-datetime-picker
              formControlName="startTime"
              dateLabel="Startzeit"
              timeLabel="Uhrzeit"
              [required]="true"
              [minDate]="today"
              requiredErrorMessage="Startzeit ist erforderlich"
              minDateErrorMessage="Startzeit darf nicht in der Vergangenheit liegen">
            </app-datetime-picker>

            <app-datetime-picker
              formControlName="endTime"
              dateLabel="Endzeit"
              timeLabel="Uhrzeit"
              [minDate]="reactiveForm.get('startTime')?.value">
            </app-datetime-picker>

            <div class="form-actions">
              <button mat-raised-button color="primary" 
                      [disabled]="reactiveForm.invalid"
                      (click)="submitReactiveForm()">
                Formular absenden
              </button>
              <button mat-button (click)="resetReactiveForm()">
                Zurücksetzen
              </button>
            </div>

            @if (reactiveForm.invalid && reactiveForm.touched) {
              <div class="form-errors">
                <strong>Formular-Fehler:</strong>
                @if (reactiveForm.get('startTime')?.errors?.['required']) {
                  <div>• Startzeit ist erforderlich</div>
                }
                @if (formHasDateRangeError()) {
                  <div>• Endzeit muss nach Startzeit liegen</div>
                }
              </div>
            }

            @if (reactiveFormResult) {
              <div class="form-result">
                <strong>Formular-Ergebnis:</strong><br>
                {{ reactiveFormResult }}
              </div>
            }
          </form>
        </mat-card-content>
      </mat-card>

      <!-- Advanced Features -->
      <mat-card class="demo-card">
        <mat-card-header>
          <mat-card-title>Erweiterte Features</mat-card-title>
          <mat-card-subtitle>Konfiguration und programmatische Steuerung</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div class="config-options">
            <mat-checkbox [(ngModel)]="advancedConfig.required">
              Pflichtfeld
            </mat-checkbox>
            <mat-checkbox [(ngModel)]="advancedConfig.disabled">
              Deaktiviert
            </mat-checkbox>
            <mat-checkbox [(ngModel)]="advancedConfig.showClearButton">
              Löschen-Button anzeigen
            </mat-checkbox>
          </div>

          <app-datetime-picker
            #advancedPicker
            [(ngModel)]="advancedDateTime"
            [required]="advancedConfig.required"
            [disabled]="advancedConfig.disabled"
            [showClearButton]="advancedConfig.showClearButton"
            [minDate]="advancedConfig.minDate"
            [maxDate]="advancedConfig.maxDate"
            dateLabel="Erweiterte Konfiguration"
            timeLabel="Uhrzeit"
            (dateTimeChange)="onAdvancedDateTimeChange($event)"
            (dateChange)="onAdvancedDateChange($event)"
            (timeChange)="onAdvancedTimeChange($event)">
          </app-datetime-picker>

          <div class="advanced-actions">
            <button mat-button (click)="setCurrentDateTime()">
              Aktuelle Zeit setzen
            </button>
            <button mat-button (click)="clearAdvancedDateTime()">
              Löschen
            </button>
            <button mat-button (click)="focusAdvancedPicker()">
              Fokus setzen
            </button>
            <button mat-button (click)="validateAdvancedPicker()">
              Validieren
            </button>
          </div>

          @if (advancedEvents.length > 0) {
            <div class="event-log">
              <strong>Event-Log:</strong>
              <div class="events">
                @for (event of advancedEvents; track event.timestamp) {
                  <div class="event-item">
                    <span class="event-time">{{ event.timestamp | date:'HH:mm:ss' }}</span>
                    <span class="event-type">{{ event.type }}</span>
                    <span class="event-value">{{ event.value }}</span>
                  </div>
                }
              </div>
              <button mat-button (click)="clearEventLog()">Log löschen</button>
            </div>
          }
        </mat-card-content>
      </mat-card>

      <!-- DateTime Utilities Demo -->
      <mat-card class="demo-card">
        <mat-card-header>
          <mat-card-title>DateTime-Utilities Integration</mat-card-title>
          <mat-card-subtitle>Verwendung der DateTime-Utilities aus Task 1</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <app-datetime-picker
            [(ngModel)]="utilsDateTime"
            dateLabel="Test-DateTime"
            timeLabel="Uhrzeit">
          </app-datetime-picker>

          @if (utilsDateTime) {
            <div class="utils-results">
              <div><strong>Formatiert (DE):</strong> {{ formatDateTime(utilsDateTime, 'de-DE') }}</div>
              <div><strong>Formatiert (EN):</strong> {{ formatDateTime(utilsDateTime, 'en-US') }}</div>
              <div><strong>ISO String:</strong> {{ utilsDateTime.toISOString() }}</div>
              <div><strong>Ist Vergangenheit:</strong> {{ isPastDateTime(utilsDateTime) ? 'Ja' : 'Nein' }}</div>
              <div><strong>Ist Zukunft:</strong> {{ isFutureDateTime(utilsDateTime) ? 'Ja' : 'Nein' }}</div>
              @if (utilsCompareDateTime) {
                <div><strong>Dauer bis jetzt:</strong> {{ getDurationToNow() }}</div>
              }
            </div>
          }

          <div class="utils-actions">
            <button mat-button (click)="setUtilsDateTimeToNow()">
              Jetzt setzen
            </button>
            <button mat-button (click)="setUtilsDateTimeToFuture()">
              Zukunft setzen
            </button>
            <button mat-button (click)="setUtilsDateTimeToPast()">
              Vergangenheit setzen
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .demo-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    .demo-card {
      margin-bottom: 24px;
    }

    .result-display,
    .form-result,
    .utils-results {
      margin-top: 16px;
      padding: 12px;
      background-color: #f5f5f5;
      border-radius: 4px;
      font-family: monospace;
    }

    .reactive-form {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .form-actions,
    .advanced-actions,
    .utils-actions {
      display: flex;
      gap: 8px;
      margin-top: 16px;
    }

    .form-errors {
      color: #f44336;
      margin-top: 8px;
    }

    .config-options {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;
      flex-wrap: wrap;
    }

    .event-log {
      margin-top: 16px;
      max-height: 200px;
      overflow-y: auto;
    }

    .events {
      background-color: #f5f5f5;
      padding: 8px;
      border-radius: 4px;
      margin: 8px 0;
    }

    .event-item {
      display: flex;
      gap: 12px;
      padding: 4px 0;
      font-family: monospace;
      font-size: 12px;
    }

    .event-time {
      color: #666;
      min-width: 60px;
    }

    .event-type {
      color: #1976d2;
      min-width: 100px;
    }

    .event-value {
      color: #333;
    }

    @media (max-width: 768px) {
      .demo-container {
        padding: 12px;
      }
      
      .config-options,
      .form-actions,
      .advanced-actions,
      .utils-actions {
        flex-direction: column;
      }
    }
  `]
})
export class DateTimePickerDemoComponent implements OnInit {
  // Basic Usage
  basicDateTime: Date | null = null;

  // Reactive Forms
  reactiveForm: FormGroup;
  reactiveFormResult: string = '';

  // Advanced Features
  advancedDateTime: Date | null = null;
  advancedConfig = {
    required: false,
    disabled: false,
    showClearButton: true,
    minDate: null as Date | null,
    maxDate: null as Date | null
  };
  advancedEvents: Array<{timestamp: Date, type: string, value: string}> = [];

  // DateTime Utilities
  utilsDateTime: Date | null = null;
  utilsCompareDateTime: Date | null = null;

  // Helper properties
  today = new Date();

  constructor(private fb: FormBuilder) {
    this.reactiveForm = this.fb.group({
      startTime: [null, Validators.required],
      endTime: [null]
    }, {
      validators: [this.dateRangeValidator.bind(this)]
    });
  }

  ngOnInit(): void {
    // Set some default values for demo
    this.basicDateTime = new Date();
    this.utilsCompareDateTime = new Date();
    
    // Set min/max dates for advanced demo
    this.advancedConfig.minDate = new Date();
    this.advancedConfig.maxDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days from now
  }

  // Basic Usage Methods
  onBasicDateTimeChange(dateTime: Date | null): void {
    console.log('Basic DateTime changed:', dateTime);
  }

  formatBasicDateTime(): string {
    if (!this.basicDateTime) return 'Nicht gesetzt';
    return formatDateTime(this.basicDateTime, 'de-DE');
  }

  getDateTimeStatus(dateTime: Date | null): string {
    if (!dateTime) return 'Nicht gesetzt';
    
    const isPast = isPastDateTime(dateTime);
    const isFuture = isFutureDateTime(dateTime);
    
    if (isPast) return 'Vergangenheit';
    if (isFuture) return 'Zukunft';
    return 'Jetzt';
  }

  // Reactive Forms Methods
  dateRangeValidator(form: FormGroup): {[key: string]: any} | null {
    const startTime = form.get('startTime')?.value;
    const endTime = form.get('endTime')?.value;
    
    if (startTime && endTime && startTime >= endTime) {
      return { dateRange: true };
    }
    
    return null;
  }

  formHasDateRangeError(): boolean {
    return this.reactiveForm.hasError('dateRange') && this.reactiveForm.touched;
  }

  submitReactiveForm(): void {
    if (this.reactiveForm.valid) {
      const formValue = this.reactiveForm.value;
      const startTime = formValue.startTime;
      const endTime = formValue.endTime;
      
      let result = `Startzeit: ${formatDateTime(startTime, 'de-DE')}`;
      
      if (endTime) {
        result += `\nEndzeit: ${formatDateTime(endTime, 'de-DE')}`;
        const duration = calculateDuration(startTime, endTime);
        result += `\nDauer: ${formatDuration(duration, 'de-DE')}`;
      }
      
      this.reactiveFormResult = result;
    }
  }

  resetReactiveForm(): void {
    this.reactiveForm.reset();
    this.reactiveFormResult = '';
  }

  // Advanced Features Methods
  onAdvancedDateTimeChange(dateTime: Date | null): void {
    this.logEvent('dateTimeChange', dateTime ? formatDateTime(dateTime, 'de-DE') : 'null');
  }

  onAdvancedDateChange(date: Date | null): void {
    this.logEvent('dateChange', date ? date.toDateString() : 'null');
  }

  onAdvancedTimeChange(time: string): void {
    this.logEvent('timeChange', time || 'empty');
  }

  setCurrentDateTime(): void {
    this.advancedDateTime = new Date();
  }

  clearAdvancedDateTime(): void {
    this.advancedDateTime = null;
  }

  focusAdvancedPicker(): void {
    // This would focus the picker if we had a ViewChild reference
    console.log('Focus advanced picker');
  }

  validateAdvancedPicker(): void {
    // This would validate the picker if we had a ViewChild reference
    console.log('Validate advanced picker');
  }

  logEvent(type: string, value: string): void {
    this.advancedEvents.unshift({
      timestamp: new Date(),
      type,
      value
    });
    
    // Keep only last 10 events
    if (this.advancedEvents.length > 10) {
      this.advancedEvents = this.advancedEvents.slice(0, 10);
    }
  }

  clearEventLog(): void {
    this.advancedEvents = [];
  }

  // DateTime Utilities Methods
  setUtilsDateTimeToNow(): void {
    this.utilsDateTime = new Date();
  }

  setUtilsDateTimeToFuture(): void {
    this.utilsDateTime = new Date(Date.now() + 2 * 60 * 60 * 1000); // 2 hours from now
  }

  setUtilsDateTimeToPast(): void {
    this.utilsDateTime = new Date(Date.now() - 2 * 60 * 60 * 1000); // 2 hours ago
  }

  getDurationToNow(): string {
    if (!this.utilsDateTime) return '';
    
    const duration = Math.abs(calculateDuration(this.utilsDateTime, new Date()));
    return formatDuration(duration, 'de-DE');
  }

  // Expose utilities for template
  formatDateTime = formatDateTime;
  isPastDateTime = isPastDateTime;
  isFutureDateTime = isFutureDateTime;
}