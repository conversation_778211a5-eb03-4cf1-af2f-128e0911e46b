import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, AbstractControl, ValidationErrors } from '@angular/forms';

// Angular Material
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCheckboxModule } from '@angular/material/checkbox';

// Models and Components
import { Incident, IncidentType, UpdateIncidentInput } from '../../../../core/models/incident.model';
import { Application } from '../../../../core/models/application.model';
import { ApplicationSelectorComponent } from '../application-selector/application-selector.component';

@Component({
  selector: 'app-incident-edit-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatButtonModule,
    MatIconModule,
    MatCheckboxModule,
    ApplicationSelectorComponent
  ],
  templateUrl: './incident-edit-form.component.html',
  styleUrls: ['./incident-edit-form.component.scss']
})
export class IncidentEditFormComponent implements OnInit, OnChanges {
  @Input() incident: Incident | null = null;
  @Input() loading = false;
  @Input() disabled = false;

  @Output() formSubmit = new EventEmitter<UpdateIncidentInput>();
  @Output() formCancel = new EventEmitter<void>();

  editForm!: FormGroup;
  
  incidentTypes = [
    { value: IncidentType.STOERUNG, label: 'Störung' },
    { value: IncidentType.WARTUNGSFENSTER, label: 'Wartungsfenster' },
    { value: IncidentType.KEINE_STOERUNG, label: 'Keine Störung' }
  ];

  private fb = inject(FormBuilder);

  // Custom validator for applicationIds
  private applicationIdsValidator(control: AbstractControl): ValidationErrors | null {
    const value = control.value;
    if (!value || !Array.isArray(value) || value.length === 0) {
      return { required: true };
    }
    return null;
  }

  // Custom validator for date range validation
  private dateRangeValidator(startControlName: string, endControlName: string) {
    return (formGroup: AbstractControl): ValidationErrors | null => {
      const startControl = formGroup.get(startControlName);
      const endControl = formGroup.get(endControlName);
      
      if (!startControl || !endControl) {
        return null;
      }
      
      const startDate = startControl.value;
      const endDate = endControl.value;
      
      if (startDate && endDate && new Date(startDate) >= new Date(endDate)) {
        return { dateRange: { start: startControlName, end: endControlName } };
      }
      
      return null;
    };
  }

  // Custom validator for resolved incident validation
  private resolvedIncidentValidator(control: AbstractControl): ValidationErrors | null {
    const formGroup = control.parent;
    if (!formGroup) {
      return null;
    }
    
    const isResolved = control.value;
    const actualEndTime = formGroup.get('actualEndTime')?.value;
    
    if (isResolved && !actualEndTime) {
      return { resolvedWithoutEndTime: true };
    }
    
    return null;
  }

  // Custom validator for actual end time when resolved
  private actualEndTimeValidator(control: AbstractControl): ValidationErrors | null {
    const formGroup = control.parent;
    if (!formGroup) {
      return null;
    }
    
    const isResolved = formGroup.get('isResolved')?.value;
    const actualEndTime = control.value;
    const startTime = formGroup.get('startTime')?.value;
    
    if (isResolved && !actualEndTime) {
      return { required: true };
    }
    
    if (actualEndTime && startTime && new Date(actualEndTime) <= new Date(startTime)) {
      return { actualEndTimeBeforeStart: true };
    }
    
    return null;
  }

  ngOnInit(): void {
    this.initializeForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['incident'] && this.editForm) {
      this.populateForm();
    }
  }

  private initializeForm(): void {
    this.editForm = this.fb.group({
      title: ['', [Validators.required, Validators.maxLength(200)]],
      type: [IncidentType.STOERUNG, Validators.required],
      description: ['', Validators.maxLength(2000)],
      startTime: ['', Validators.required],
      plannedEndTime: [''],
      actualEndTime: ['', this.actualEndTimeValidator],
      alternatives: ['', Validators.maxLength(1000)],
      applicationIds: [[], this.applicationIdsValidator],
      isResolved: [false, this.resolvedIncidentValidator]
    }, {
      validators: [
        this.dateRangeValidator('startTime', 'plannedEndTime'),
        this.dateRangeValidator('startTime', 'actualEndTime')
      ]
    });

    // Watch for changes in isResolved to update actualEndTime validation
    this.editForm.get('isResolved')?.valueChanges.subscribe(() => {
      const actualEndTimeControl = this.editForm.get('actualEndTime');
      actualEndTimeControl?.updateValueAndValidity();
    });

    if (this.incident) {
      this.populateForm();
    }
  }

  private populateForm(): void {
    if (!this.incident || !this.editForm) return;

    this.editForm.patchValue({
      title: this.incident.title,
      type: this.incident.type,
      description: this.incident.description || '',
      startTime: this.parseDateTime(this.incident.startTime),
      plannedEndTime: this.parseDateTime(this.incident.plannedEndTime),
      actualEndTime: this.parseDateTime(this.incident.actualEndTime),
      alternatives: this.incident.alternatives || '',
      applicationIds: this.incident.applications.map(app => app.identifier),
      isResolved: this.incident.isResolved
    });
  }

  private parseDateTime(dateTime?: string | Date): Date | null {
    if (!dateTime) return null;
    if (dateTime instanceof Date) return dateTime;
    return new Date(dateTime);
  }

  private formatDateTime(date: Date | null): string | undefined {
    if (!date) return undefined;
    return date.toISOString();
  }


  onSubmit(): void {
    if (this.editForm.valid && this.incident) {
      const formValue = this.editForm.value;
      
      const updateInput: UpdateIncidentInput = {
        identifier: this.incident.identifier,
        title: formValue.title,
        type: formValue.type,
        description: formValue.description || undefined,
        startTime: this.formatDateTime(formValue.startTime),
        plannedEndTime: this.formatDateTime(formValue.plannedEndTime),
        actualEndTime: this.formatDateTime(formValue.actualEndTime),
        alternatives: formValue.alternatives || undefined,
        applicationIds: formValue.applicationIds
      };

      this.formSubmit.emit(updateInput);
    } else {
      this.markFormGroupTouched();
    }
  }

  onCancel(): void {
    this.formCancel.emit();
  }

  private markFormGroupTouched(): void {
    Object.keys(this.editForm.controls).forEach(key => {
      const control = this.editForm.get(key);
      control?.markAsTouched();
    });
  }

  getFieldError(fieldName: string): string {
    const control = this.editForm.get(fieldName);
    if (!control || !control.errors || !control.touched) {
      return '';
    }

    const errors = control.errors;
    
    if (errors['required']) {
      return this.getRequiredErrorMessage(fieldName);
    }
    
    if (errors['maxlength']) {
      const maxLength = errors['maxlength'].requiredLength;
      return `Maximal ${maxLength} Zeichen erlaubt`;
    }

    if (errors['resolvedWithoutEndTime']) {
      return 'Gelöste Vorfälle benötigen eine tatsächliche Endzeit';
    }

    if (errors['actualEndTimeBeforeStart']) {
      return 'Die tatsächliche Endzeit muss nach der Startzeit liegen';
    }

    // Check for form-level date range errors
    const formErrors = this.editForm.errors;
    if (formErrors && formErrors['dateRange']) {
      const dateRangeError = formErrors['dateRange'];
      if (dateRangeError.end === fieldName) {
        return 'Die Endzeit muss nach der Startzeit liegen';
      }
    }

    return 'Ungültiger Wert';
  }

  private getRequiredErrorMessage(fieldName: string): string {
    const fieldLabels: { [key: string]: string } = {
      title: 'Titel',
      type: 'Typ',
      startTime: 'Startzeit',
      applicationIds: 'Betroffene Anwendungen'
    };
    
    const label = fieldLabels[fieldName] || fieldName;
    return `${label} ist erforderlich`;
  }

  hasFieldError(fieldName: string): boolean {
    const control = this.editForm.get(fieldName);
    const hasControlError = !!(control && control.errors && control.touched);
    
    // Check for form-level date range errors
    const formErrors = this.editForm.errors;
    const hasFormError = !!(formErrors && formErrors['dateRange'] &&
                           formErrors['dateRange'].end === fieldName);
    
    return hasControlError || hasFormError;
  }

  get isFormValid(): boolean {
    return this.editForm.valid;
  }

  get hasApplicationError(): boolean {
    const control = this.editForm.get('applicationIds');
    return !!(control && control.errors && control.touched);
  }

  getIncidentTypeLabel(type: IncidentType): string {
    const typeOption = this.incidentTypes.find(t => t.value === type);
    return typeOption ? typeOption.label : type;
  }
}