import { createReducer, on } from '@ngrx/store';
import { AuthState, initialAuthState } from './auth.state';
import * as AuthActions from './auth.actions';

export const authReducer = createReducer(
  initialAuthState,

  // Login Actions
  on(AuthActions.login, (state) => ({
    ...state,
    isLoading: true,
    error: null
  })),

  on(AuthActions.loginSuccess, (state, { response }) => ({
    ...state,
    user: response.user,
    token: response.token,
    refreshToken: response.refreshToken,
    isAuthenticated: true,
    isLoading: false,
    error: null
  })),

  on(AuthActions.loginFailure, (state, { error }) => ({
    ...state,
    user: null,
    token: null,
    refreshToken: null,
    isAuthenticated: false,
    isLoading: false,
    error
  })),

  // Logout Actions
  on(AuthActions.logout, (state) => ({
    ...state,
    isLoading: true
  })),

  on(AuthActions.logoutSuccess, () => ({
    ...initialAuthState
  })),

  // Token Refresh Actions
  on(AuthActions.refreshToken, (state) => ({
    ...state,
    isLoading: true,
    error: null
  })),

  on(AuthActions.refreshTokenSuccess, (state, { token }) => ({
    ...state,
    token,
    isLoading: false,
    error: null
  })),

  on(AuthActions.refreshTokenFailure, (state, { error }) => ({
    ...state,
    isLoading: false,
    error
  })),

  // Hydrate Auth Actions (new as per plan)
  on(AuthActions.hydrateAuth, (state) => ({
    ...state,
    isLoading: true
  })),

  on(AuthActions.hydrateAuthSuccess, (state, { user, token, refreshToken }) => ({
    ...state,
    user,
    token,
    refreshToken,
    isAuthenticated: true,
    isLoading: false,
    error: null
  })),

  on(AuthActions.hydrateAuthFailure, (state) => ({
    ...state,
    isLoading: false,
    error: null
  })),

  // Initialize Auth Actions (keep for backward compatibility)
  on(AuthActions.initializeAuth, (state) => ({
    ...state,
    isLoading: true
  })),

  on(AuthActions.initializeAuthSuccess, (state, { user, token, refreshToken }) => ({
    ...state,
    user,
    token,
    refreshToken,
    isAuthenticated: true,
    isLoading: false,
    error: null
  })),

  on(AuthActions.initializeAuthFailure, (state) => ({
    ...state,
    isLoading: false,
    error: null
  })),

  // Clear Error
  on(AuthActions.clearAuthError, (state) => ({
    ...state,
    error: null
  })),

  // Update User Profile Actions
  on(AuthActions.updateUserProfile, (state) => ({
    ...state,
    isLoading: true,
    error: null
  })),

  on(AuthActions.updateUserProfileSuccess, (state, { user }) => ({
    ...state,
    user,
    isLoading: false,
    error: null
  })),

  on(AuthActions.updateUserProfileFailure, (state, { error }) => ({
    ...state,
    isLoading: false,
    error
  }))
);