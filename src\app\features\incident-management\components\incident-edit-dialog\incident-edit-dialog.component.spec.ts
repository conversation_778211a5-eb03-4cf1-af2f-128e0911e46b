import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { of } from 'rxjs';

import { IncidentEditDialogComponent, IncidentEditDialogData } from './incident-edit-dialog.component';
import { IncidentsFacade } from '../../../../store/incidents/incidents.facade';
import { Incident, IncidentType, UpdateIncidentInput } from '../../../../core/models/incident.model';
import { ApplicationService } from '../../../../core/services/application.service';

describe('IncidentEditDialogComponent', () => {
  let component: IncidentEditDialogComponent;
  let fixture: ComponentFixture<IncidentEditDialogComponent>;
  let mockDialogRef: jasmine.SpyObj<MatDialogRef<IncidentEditDialogComponent>>;
  let mockIncidentsFacade: jasmine.SpyObj<IncidentsFacade>;
  let mockSnackBar: jasmine.SpyObj<MatSnackBar>;
  let mockApplicationService: jasmine.SpyObj<ApplicationService>;

  const mockIncident: Incident = {
    identifier: 'test-id',
    title: 'Test Incident',
    type: IncidentType.STOERUNG,
    description: 'Test Description',
    startTime: '2024-01-01T10:00:00Z',
    plannedEndTime: '2024-01-01T12:00:00Z',
    actualEndTime: undefined,
    alternatives: 'Test Alternatives',
    isResolved: false,
    createdAt: '2024-01-01T09:00:00Z',
    updatedAt: '2024-01-01T09:00:00Z',
    applications: []
  };

  const mockDialogData: IncidentEditDialogData = {
    incident: mockIncident,
    title: 'Test Dialog Title'
  };

  beforeEach(async () => {
    const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['close', 'disableClose', 'updateSize']);
    const incidentsFacadeSpy = jasmine.createSpyObj('IncidentsFacade', [
      'updateIncident', 
      'clearIncidentsError'
    ], {
      isLoading$: of(false),
      error$: of(null)
    });
    const snackBarSpy = jasmine.createSpyObj('MatSnackBar', ['open']);
    const applicationServiceSpy = jasmine.createSpyObj('ApplicationService', ['getAllApplications', 'searchApplicationsByName']);

    await TestBed.configureTestingModule({
      imports: [
        IncidentEditDialogComponent,
        NoopAnimationsModule
      ],
      providers: [
        { provide: MatDialogRef, useValue: dialogRefSpy },
        { provide: MAT_DIALOG_DATA, useValue: mockDialogData },
        { provide: IncidentsFacade, useValue: incidentsFacadeSpy },
        { provide: MatSnackBar, useValue: snackBarSpy },
        { provide: ApplicationService, useValue: applicationServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(IncidentEditDialogComponent);
    component = fixture.componentInstance;
    mockDialogRef = TestBed.inject(MatDialogRef) as jasmine.SpyObj<MatDialogRef<IncidentEditDialogComponent>>;
    mockIncidentsFacade = TestBed.inject(IncidentsFacade) as jasmine.SpyObj<IncidentsFacade>;
    mockSnackBar = TestBed.inject(MatSnackBar) as jasmine.SpyObj<MatSnackBar>;
    mockApplicationService = TestBed.inject(ApplicationService) as jasmine.SpyObj<ApplicationService>;

    mockApplicationService.getAllApplications.and.returnValue(of([]));
    mockApplicationService.searchApplicationsByName.and.returnValue(of([]));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should configure dialog on construction', () => {
    expect(mockDialogRef.disableClose).toHaveBeenCalledWith(true);
    expect(mockDialogRef.updateSize).toHaveBeenCalledWith('800px', 'auto');
  });

  it('should set up store subscriptions on init', () => {
    fixture.detectChanges();
    
    expect(component.loading).toBeFalsy();
    expect(component.error).toBeNull();
  });

  it('should handle form submission', () => {
    const updateInput: UpdateIncidentInput = {
      identifier: 'test-id',
      title: 'Updated Title'
    };

    component.onFormSubmit(updateInput);

    expect(component.error).toBeNull();
    expect(mockIncidentsFacade.clearIncidentsError).toHaveBeenCalled();
    expect(mockIncidentsFacade.updateIncident).toHaveBeenCalledWith(updateInput);
  });

  it('should close dialog on successful form submission', (done) => {
    const updateInput: UpdateIncidentInput = {
      identifier: 'test-id',
      title: 'Updated Title'
    };

    component.onFormSubmit(updateInput);

    // Wait for the timeout in onFormSubmit
    setTimeout(() => {
      expect(mockSnackBar.open).toHaveBeenCalledWith(
        'Vorfall wurde erfolgreich aktualisiert',
        'Schließen',
        jasmine.objectContaining({
          duration: 5000,
          panelClass: ['success-snackbar']
        })
      );
      expect(mockDialogRef.close).toHaveBeenCalledWith({
        updated: true,
        incident: jasmine.objectContaining({ ...mockIncident, ...updateInput })
      });
      done();
    }, 1100);
  });

  it('should handle form cancellation', () => {
    component.onFormCancel();
    
    expect(mockDialogRef.close).toHaveBeenCalledWith({ updated: false });
  });

  it('should handle dialog close', () => {
    component.onCloseDialog();
    
    expect(mockDialogRef.close).toHaveBeenCalledWith({ updated: false });
  });

  it('should prevent closing when loading', () => {
    component.loading = true;
    
    component.onFormCancel();
    component.onCloseDialog();
    
    expect(mockDialogRef.close).not.toHaveBeenCalled();
  });

  it('should return correct dialog title', () => {
    expect(component.dialogTitle).toBe('Test Dialog Title');
    
    // Test default title
    component.data.title = undefined;
    expect(component.dialogTitle).toBe(`Vorfall bearbeiten: ${mockIncident.title}`);
  });

  it('should return correct canClose status', () => {
    expect(component.canClose).toBeTruthy();
    
    component.loading = true;
    expect(component.canClose).toBeFalsy();
  });

  it('should handle loading state changes', () => {
    const loadingSubject = mockIncidentsFacade.isLoading$ as any;
    loadingSubject.next(true);
    
    fixture.detectChanges();
    
    expect(component.loading).toBeTruthy();
  });

  it('should handle error state changes', () => {
    const errorMessage = 'Test error message';
    const errorSubject = mockIncidentsFacade.error$ as any;
    errorSubject.next(errorMessage);
    
    fixture.detectChanges();
    
    expect(component.error).toBe(errorMessage);
    expect(mockSnackBar.open).toHaveBeenCalledWith(
      errorMessage,
      'Schließen',
      jasmine.objectContaining({
        duration: 8000,
        panelClass: ['error-snackbar']
      })
    );
  });

  it('should clear error when error is set to null', () => {
    component.error = 'Some error';
    
    component.error = null;
    
    expect(component.error).toBeNull();
  });

  it('should show success message', () => {
    const message = 'Success message';
    
    (component as any).showSuccessMessage(message);
    
    expect(mockSnackBar.open).toHaveBeenCalledWith(
      message,
      'Schließen',
      jasmine.objectContaining({
        duration: 5000,
        panelClass: ['success-snackbar']
      })
    );
  });

  it('should show error message', () => {
    const message = 'Error message';
    
    (component as any).showErrorMessage(message);
    
    expect(mockSnackBar.open).toHaveBeenCalledWith(
      message,
      'Schließen',
      jasmine.objectContaining({
        duration: 8000,
        panelClass: ['error-snackbar']
      })
    );
  });
});