import { TestBed } from '@angular/core/testing';
import { ApplicationValidators } from './application-validators';
import { FormControl, ValidationErrors } from '@angular/forms';
import { Observable } from 'rxjs';

describe('ApplicationValidators', () => {
  let validator: ApplicationValidators;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [ApplicationValidators]
    });
    validator = TestBed.inject(ApplicationValidators);
  });

  it('should be created', () => {
    expect(validator).toBeTruthy();
  });

  describe('nameUniquenessValidator', () => {
    it('should return null when name is unique', (done) => {
      const control = new FormControl('unique-name');
      const validatorFn = validator.nameUniquenessValidator();
      const result$ = validatorFn(control) as Observable<ValidationErrors | null>;

      result$.subscribe((result: ValidationErrors | null) => {
        expect(result).toBeNull();
        done();
      });
    });
  });
});