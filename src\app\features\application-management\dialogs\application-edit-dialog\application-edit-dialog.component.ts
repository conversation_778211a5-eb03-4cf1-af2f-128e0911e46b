import { Component, ChangeDetectionStrategy, <PERSON><PERSON><PERSON><PERSON>, OnInit, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MatDialogModule, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { Actions, ofType } from '@ngrx/effects';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

import { ApplicationsFacade } from '../../../../store/applications/applications.facade';
import { ApplicationEditFormComponent } from '../../components/application-edit-form/application-edit-form.component';
import { Application, UpdateApplicationInput } from '../../../../core/models/application.model';
import * as ApplicationActions from '../../../../store/applications/applications.actions';

@Component({
  selector: 'app-application-edit-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatIconModule,
    MatTooltipModule,
    ApplicationEditFormComponent,
    MatSnackBarModule
  ],
  templateUrl: './application-edit-dialog.component.html',
  styleUrls: ['./application-edit-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ApplicationEditDialogComponent implements OnInit, OnDestroy {
  loading$: Observable<boolean>;
  error$: Observable<any>;
  private destroy$ = new Subject<void>();

  constructor(
    private dialogRef: MatDialogRef<ApplicationEditDialogComponent>,
    private applicationsFacade: ApplicationsFacade,
    private actions$: Actions,
    private snackBar: MatSnackBar,
    @Inject(MAT_DIALOG_DATA) public data: Application
  ) {
    this.loading$ = this.applicationsFacade.loading$;
    this.error$ = this.applicationsFacade.error$;
  }

  ngOnInit(): void {
    this.actions$.pipe(
      ofType(ApplicationActions.updateApplicationSuccess),
      takeUntil(this.destroy$)
    ).subscribe(() => {
      this.snackBar.open('Application updated successfully.', 'OK', { duration: 3000 });
      this.dialogRef.close(true);
    });

    this.actions$.pipe(
      ofType(ApplicationActions.updateApplicationFailure),
      takeUntil(this.destroy$)
    ).subscribe(({ error }) => {
       this.snackBar.open(`Error updating application: ${error.message}`, 'OK', {
         duration: 5000,
         panelClass: ['error-snackbar']
       });
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }

  onSubmit(application: Partial<UpdateApplicationInput>): void {
    const update: UpdateApplicationInput = {
      ...application,
      identifier: this.data.identifier,
    };
    this.applicationsFacade.updateApplication(update);
  }
}