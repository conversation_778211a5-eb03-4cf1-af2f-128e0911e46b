import { Component, Input, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { DashboardMetrics } from '../../models/dashboard-metrics.interface';

@Component({
  selector: 'app-dashboard-stats',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatIconModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <div class="stats-grid">
      <mat-card class="stats-card" *ngFor="let stat of statsCards; trackBy: trackByTitle">
        <mat-card-header>
          <mat-card-title>{{ stat.title }}</mat-card-title>
          <mat-icon [class]="stat.iconClass">{{ stat.icon }}</mat-icon>
        </mat-card-header>
        <mat-card-content>
          <div class="stats-number">{{ stat.value }}</div>
          <div class="stats-label">{{ stat.label }}</div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .stats-card {
      min-height: 120px;
      border: 1px solid var(--border-color);
      box-shadow: none;
      border-radius: var(--border-radius-md);
      transition: border-color 0.2s ease;
      background-color: var(--funk-white);
    }

    .stats-card:hover {
      border-color: var(--funk-light-blue);
      box-shadow: none;
    }

    .stats-card mat-card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 0.5rem;
    }

    .stats-card mat-card-title {
      font-size: 0.875rem;
      font-weight: 500;
      color: var(--funk-gray);
      text-transform: uppercase;
      letter-spacing: 0.05em;
      margin: 0;
    }

    .stats-icon {
      font-size: 1.25rem;
      width: 1.25rem;
      height: 1.25rem;
      color: var(--funk-light-gray);
    }

    .stats-icon.error {
      color: var(--error-color);
    }

    .stats-icon.warning {
      color: var(--warning-color);
    }

    .stats-icon.success {
      color: var(--success-color);
    }

    .stats-icon.info {
      color: var(--info-color);
    }

    .stats-number {
      font-size: 2.25rem;
      font-weight: 700;
      color: var(--funk-blue);
      margin: 0.25rem 0 0.5rem 0;
      line-height: 1;
    }

    .stats-label {
      color: var(--funk-gray);
      font-size: 0.875rem;
      font-weight: 400;
    }

    @media (max-width: 768px) {
      .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      }
      
      .stats-card {
        min-height: 100px;
      }
      
      .stats-number {
        font-size: 2rem;
      }
    }
  `]
})
export class DashboardStatsComponent {
  @Input() metrics!: DashboardMetrics;

  get statsCards() {
    if (!this.metrics) {
      return [];
    }

    return [
      {
        title: 'Aktive Vorfälle',
        value: this.metrics.activeIncidents,
        label: 'Ungelöst',
        icon: 'warning',
        iconClass: 'stats-icon warning'
      },
      {
        title: 'Geplante Wartungen',
        value: this.metrics.plannedMaintenance,
        label: 'Anstehend',
        icon: 'build',
        iconClass: 'stats-icon info'
      },
      {
        title: 'Verwaltete Anwendungen',
        value: this.metrics.activeApplications,
        label: 'Aktiv',
        icon: 'apps',
        iconClass: 'stats-icon success'
      },
      {
        title: 'Kürzliche Aktivität',
        value: this.metrics.recentActivity,
        label: 'Letzte 7 Tage',
        icon: 'trending_up',
        iconClass: 'stats-icon info'
      }
    ];
  }

  trackByTitle(index: number, item: any): string {
    return item.title;
  }
}