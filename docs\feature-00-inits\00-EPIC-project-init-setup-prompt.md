# Init - Prompt

Initialisiere die technologischen Frontend-Komponenten im Rootverzeichnis. Nutze die spezifischen Framework-Initialisierungs-Routinen für den Start und generiere nicht selbst Code.

Schreibe zusätzlich unter \.roo\rules\technical-constaints.md eine Basis-Beschreibung des Technologie-Stacks als allgemeingültige Orientierung für alle nachfolgenden Prompts und Befehle.

Weitere Technologie-Vorgaben:

- **Framework**: Angular 19 
- **State Management**: NgRx
- **UI-Framework**: Angular Material
- **API-Integration**: Apollo Client für GraphQL