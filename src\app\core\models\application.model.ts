export interface Application {
  identifier: string;
  name: string;
  description?: string;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateApplicationInput {
  name: string;
  description?: string;
}

export interface UpdateApplicationInput {
  identifier: string;
  name?: string;
  description?: string;
  isDeleted?: boolean;
}

export interface ApplicationFilter {
  isDeleted?: boolean;
  searchTerm?: string;
}