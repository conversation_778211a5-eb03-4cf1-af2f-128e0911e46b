# Implementierungsplan: Integration des GraphQL-Schemas für System-Verwaltung

## 1. Übersicht

Dieser Plan beschreibt die Integration des bereitgestellten GraphQL-Schemas in die System-Verwaltung des StoerungsBuddy-Frontends. Das Schema definiert die Struktur der Daten und Operationen, die für die Verwaltung von Systemen verfügbar sind.

```mermaid
graph TD
    A[Frontend] -->|GraphQL Requests| B[GraphQL Endpoint]
    B -->|GraphQL Responses| A
    C[Store] <-->|Actions/State| A
    D[SystemService] -->|API Calls| B
    A -->|Dispatch Actions| C
    A -->|Service Calls| D
    D -->|Dispatch Actions| C
```

## 2. Analyse des GraphQL-Schemas

### 2.1 System-Modell

Das GraphQL-Schema definiert ein `System`-Typ mit folgenden Eigenschaften:

```graphql
type System {
  systemID: Int!
  name: String!
  description: String
  status: SystemStatus
  createdAt: DateTime!
  updatedAt: DateTime!
}
```

Im Vergleich zum aktuellen Frontend-Modell fehlen folgende Eigenschaften:
- `category` (SystemCategory)
- `dependencies` (string[])
- `responsibleTeam` (string)
### 2.2 SystemStatus-Enum

Das Schema definiert ein `SystemStatus`-Enum mit folgenden Werten:

```graphql
enum SystemStatus {
  OPERATIONAL
  DEGRADED
  OUTAGE
  MAINTENANCE
  UNKNOWN
}
```

Im Vergleich zum aktuellen Frontend-Enum gibt es folgende Unterschiede:
- `OUTAGE` statt `DOWN`
- Zusätzlicher Wert `UNKNOWN`

### 2.3 Verfügbare Operationen

Das Schema definiert folgende Operationen:

**Queries:**
- `systems`: Alle Systeme abrufen
- `system(id: Int!)`: Ein System nach ID abrufen

**Mutations:**
- `createSystem(input: SystemInput!)`: Ein neues System erstellen
- `createSystemWithInput(input: CreateSystemInput!)`: Ein neues System mit vereinfachtem Input erstellen
- `updateSystem(id: Int!, input: SystemInput!)`: Ein bestehendes System aktualisieren
- `deleteSystem(id: Int!)`: Ein System löschen

## 3. Notwendige Anpassungen

### 3.1 Anpassung der Datenmodelle

#### 3.1.1 System Interface

Aktualisierung der Datei `src/app/features/systems/models/system.interface.ts`:

```typescript
import { SystemStatus } from './system-status.enum';
import { SystemCategory } from './system-category.interface';

export interface System {
  // Primäre Eigenschaften aus dem GraphQL-Schema
  systemID: number;
  name: string;
  description?: string;
  status: SystemStatus;
  createdAt: Date;
  updatedAt: Date;
#### 3.1.2 SystemStatus Enum

Aktualisierung der Datei `src/app/features/systems/models/system-status.enum.ts`:

```typescript
export enum SystemStatus {
  OPERATIONAL = 'OPERATIONAL',
  DEGRADED = 'DEGRADED',
  OUTAGE = 'OUTAGE',
  MAINTENANCE = 'MAINTENANCE',
  UNKNOWN = 'UNKNOWN'
}
```

#### 3.1.3 Input-Interfaces

Erstellung einer neuen Datei `src/app/features/systems/models/system-input.interface.ts`:

```typescript
import { SystemStatus } from './system-status.enum';

export interface SystemInput {
  systemID: number;
  name: string;
  description?: string;
  status: SystemStatus;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateSystemInput {
  name: string;
  description?: string;
  status: SystemStatus;
}
```

### 3.2 GraphQL-Schemas definieren

Erstellung einer neuen Datei `src/app/features/systems/graphql/system.graphql.ts`:

```typescript
import { gql } from 'apollo-angular';

// Fragments für wiederverwendbare Teile
export const SYSTEM_FRAGMENT = gql`
  fragment SystemFields on System {
    systemID
    name
    description
    status
    createdAt
    updatedAt
  }
`;

// Queries
export const GET_SYSTEMS = gql`
  query GetSystems {
    systems {
      ...SystemFields
    }
  }
  ${SYSTEM_FRAGMENT}
`;

export const GET_SYSTEM = gql`
  query GetSystem($id: Int!) {
    system(id: $id) {
      ...SystemFields
    }
  }
  ${SYSTEM_FRAGMENT}
`;

// Mutations
export const CREATE_SYSTEM = gql`
  mutation CreateSystem($input: SystemInput!) {
    createSystem(input: $input) {
      ...SystemFields
    }
  }
  ${SYSTEM_FRAGMENT}
`;

export const CREATE_SYSTEM_SIMPLIFIED = gql`
  mutation CreateSystemWithInput($input: CreateSystemInput!) {
    createSystemWithInput(input: $input) {
      ...SystemFields
    }
  }
  ${SYSTEM_FRAGMENT}
`;

export const UPDATE_SYSTEM = gql`
  mutation UpdateSystem($id: Int!, $input: SystemInput!) {
    updateSystem(id: $id, input: $input) {
      ...SystemFields
    }
  }
### 3.3 SystemService implementieren

Erstellung einer neuen Datei `src/app/features/systems/services/system.service.ts`:

```typescript
import { Injectable } from '@angular/core';
import { Apollo } from 'apollo-angular';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { System } from '../models/system.interface';
import { SystemStatus } from '../models/system-status.enum';
import { SystemInput, CreateSystemInput } from '../models/system-input.interface';
import {
  GET_SYSTEMS,
  GET_SYSTEM,
  CREATE_SYSTEM,
  CREATE_SYSTEM_SIMPLIFIED,
  UPDATE_SYSTEM,
  DELETE_SYSTEM
} from '../graphql/system.graphql';

@Injectable({
  providedIn: 'root'
})
export class SystemService {
  constructor(private apollo: Apollo) {}

  getSystems(): Observable<System[]> {
    return this.apollo.watchQuery<{ systems: System[] }>({
      query: GET_SYSTEMS
    }).valueChanges.pipe(
      map(result => result.data.systems)
    );
  }

  getSystem(id: number): Observable<System> {
    return this.apollo.watchQuery<{ system: System }>({
      query: GET_SYSTEM,
      variables: { id }
    }).valueChanges.pipe(
      map(result => result.data.system)
    );
  }

  createSystem(input: SystemInput): Observable<System> {
    return this.apollo.mutate<{ createSystem: System }>({
      mutation: CREATE_SYSTEM,
      variables: { input },
      refetchQueries: [{ query: GET_SYSTEMS }]
    }).pipe(
      map(result => result.data!.createSystem)
    );
  }

  createSystemSimplified(input: CreateSystemInput): Observable<System> {
    return this.apollo.mutate<{ createSystemWithInput: System }>({
      mutation: CREATE_SYSTEM_SIMPLIFIED,
      variables: { input },
      refetchQueries: [{ query: GET_SYSTEMS }]
    }).pipe(
      map(result => result.data!.createSystemWithInput)
    );
  }

  updateSystem(id: number, input: SystemInput): Observable<System> {
    return this.apollo.mutate<{ updateSystem: System }>({
      mutation: UPDATE_SYSTEM,
      variables: { id, input },
      refetchQueries: [{ query: GET_SYSTEMS }]
    }).pipe(
      map(result => result.data!.updateSystem)
    );
  }

  deleteSystem(id: number): Observable<boolean> {
    return this.apollo.mutate<{ deleteSystem: boolean }>({
      mutation: DELETE_SYSTEM,
      variables: { id },
      refetchQueries: [{ query: GET_SYSTEMS }]
    }).pipe(
      map(result => result.data!.deleteSystem)
    );
  }
}
```

### 3.4 Store-Integration

#### 3.4.1 Actions

Erstellung einer neuen Datei `src/app/features/systems/store/system.actions.ts`:

```typescript
import { createAction, props } from '@ngrx/store';
import { System } from '../models/system.interface';
import { SystemInput, CreateSystemInput } from '../models/system-input.interface';
import { SystemStatus } from '../models/system-status.enum';

// Load Systems
export const loadSystems = createAction('[System] Load Systems');
export const loadSystemsSuccess = createAction(
  '[System] Load Systems Success',
  props<{ systems: System[] }>()
);
export const loadSystemsFailure = createAction(
  '[System] Load Systems Failure',
  props<{ error: any }>()
);

// Load System
export const loadSystem = createAction(
  '[System] Load System',
  props<{ id: number }>()
);
export const loadSystemSuccess = createAction(
  '[System] Load System Success',
  props<{ system: System }>()
);
export const loadSystemFailure = createAction(
  '[System] Load System Failure',
  props<{ error: any }>()
);

// Create System
export const createSystem = createAction(
  '[System] Create System',
  props<{ input: SystemInput }>()
);
export const createSystemSuccess = createAction(
  '[System] Create System Success',
  props<{ system: System }>()
);
export const createSystemFailure = createAction(
  '[System] Create System Failure',
  props<{ error: any }>()
);

// Create System Simplified
export const createSystemSimplified = createAction(
  '[System] Create System Simplified',
  props<{ input: CreateSystemInput }>()
);
export const createSystemSimplifiedSuccess = createAction(
  '[System] Create System Simplified Success',
  props<{ system: System }>()
);
export const createSystemSimplifiedFailure = createAction(
  '[System] Create System Simplified Failure',
  props<{ error: any }>()
);

// Update System
export const updateSystem = createAction(
  '[System] Update System',
  props<{ id: number; input: SystemInput }>()
);
export const updateSystemSuccess = createAction(
  '[System] Update System Success',
  props<{ system: System }>()
#### 3.4.2 Reducer

Erstellung einer neuen Datei `src/app/features/systems/store/system.reducer.ts`:

```typescript
import { createReducer, on } from '@ngrx/store';
import { EntityState, EntityAdapter, createEntityAdapter } from '@ngrx/entity';
import { System } from '../models/system.interface';
import { SystemStatus } from '../models/system-status.enum';
import * as SystemActions from './system.actions';

export const SYSTEM_FEATURE_KEY = 'systems';

export interface SystemState extends EntityState<System> {
  selectedSystemId: number | null;
  loading: boolean;
  error: any;
  filters: {
    status?: SystemStatus;
    search?: string;
  };
}

export const adapter: EntityAdapter<System> = createEntityAdapter<System>({
  selectId: (system: System) => system.systemID
});

export const initialState: SystemState = adapter.getInitialState({
  selectedSystemId: null,
  loading: false,
  error: null,
  filters: {}
});

export const systemReducer = createReducer(
  initialState,
  
  // Load Systems
  on(SystemActions.loadSystems, (state) => ({
    ...state,
    loading: true,
    error: null
  })),
  on(SystemActions.loadSystemsSuccess, (state, { systems }) => 
    adapter.setAll(systems, {
      ...state,
      loading: false
    })
  ),
  on(SystemActions.loadSystemsFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),
  
  // Load System
  on(SystemActions.loadSystem, (state) => ({
    ...state,
    loading: true,
    error: null
  })),
  on(SystemActions.loadSystemSuccess, (state, { system }) => 
    adapter.upsertOne(system, {
      ...state,
      selectedSystemId: system.systemID,
      loading: false
    })
  ),
  on(SystemActions.loadSystemFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),
  
  // Create System
  on(SystemActions.createSystem, (state) => ({
    ...state,
    loading: true,
    error: null
  })),
  on(SystemActions.createSystemSuccess, (state, { system }) => 
    adapter.addOne(system, {
      ...state,
      loading: false
    })
  ),
  on(SystemActions.createSystemFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),
  
  // Create System Simplified
  on(SystemActions.createSystemSimplified, (state) => ({
    ...state,
    loading: true,
    error: null
  })),
  on(SystemActions.createSystemSimplifiedSuccess, (state, { system }) => 
    adapter.addOne(system, {
      ...state,
      loading: false
    })
  ),
  on(SystemActions.createSystemSimplifiedFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),
  
  // Update System
  on(SystemActions.updateSystem, (state) => ({
    ...state,
    loading: true,
    error: null
  })),
  on(SystemActions.updateSystemSuccess, (state, { system }) => 
    adapter.updateOne(
      { id: system.systemID, changes: system },
      {
        ...state,
        loading: false
      }
    )
  ),
  on(SystemActions.updateSystemFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),
  
  // Delete System
  on(SystemActions.deleteSystem, (state) => ({
    ...state,
    loading: true,
    error: null
  })),
  on(SystemActions.deleteSystemSuccess, (state, { id }) => 
    adapter.removeOne(id, {
      ...state,
      loading: false
    })
  ),
  on(SystemActions.deleteSystemFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),
  
  // Set System Filters
  on(SystemActions.setSystemFilters, (state, { filters }) => ({
    ...state,
    filters
  }))
);

export const {
  selectIds,
  selectEntities,
  selectAll,
  selectTotal
} = adapter.getSelectors();
```

#### 3.4.3 Selectors

Erstellung einer neuen Datei `src/app/features/systems/store/system.selectors.ts`:

```typescript
import { createFeatureSelector, createSelector } from '@ngrx/store';
import { SystemState, SYSTEM_FEATURE_KEY, selectAll } from './system.reducer';
import { SystemStatus } from '../models/system-status.enum';

export const selectSystemState = createFeatureSelector<SystemState>(SYSTEM_FEATURE_KEY);

export const selectAllSystems = createSelector(
  selectSystemState,
  selectAll
);

export const selectSystemsLoading = createSelector(
  selectSystemState,
  (state: SystemState) => state.loading
);

export const selectSystemsError = createSelector(
  selectSystemState,
  (state: SystemState) => state.error
);

export const selectSystemFilters = createSelector(
  selectSystemState,
  (state: SystemState) => state.filters
);

export const selectFilteredSystems = createSelector(
  selectAllSystems,
  selectSystemFilters,
  (systems, filters) => {
    return systems.filter(system => {
#### 3.4.4 Effects

Erstellung einer neuen Datei `src/app/features/systems/store/system.effects.ts`:

```typescript
import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';

import { SystemService } from '../services/system.service';
import * as SystemActions from './system.actions';

@Injectable()
export class SystemEffects {
  constructor(
    private actions$: Actions,
    private systemService: SystemService
  ) {}

  loadSystems$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SystemActions.loadSystems),
      switchMap(() =>
        this.systemService.getSystems().pipe(
          map(systems => SystemActions.loadSystemsSuccess({ systems })),
          catchError(error => of(SystemActions.loadSystemsFailure({ error })))
        )
      )
    )
  );

  loadSystem$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SystemActions.loadSystem),
      switchMap(({ id }) =>
        this.systemService.getSystem(id).pipe(
          map(system => SystemActions.loadSystemSuccess({ system })),
          catchError(error => of(SystemActions.loadSystemFailure({ error })))
        )
      )
    )
  );

  createSystem$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SystemActions.createSystem),
      switchMap(({ input }) =>
        this.systemService.createSystem(input).pipe(
          map(system => SystemActions.createSystemSuccess({ system })),
          catchError(error => of(SystemActions.createSystemFailure({ error })))
        )
      )
    )
  );

  createSystemSimplified$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SystemActions.createSystemSimplified),
      switchMap(({ input }) =>
        this.systemService.createSystemSimplified(input).pipe(
          map(system => SystemActions.createSystemSimplifiedSuccess({ system })),
          catchError(error => of(SystemActions.createSystemSimplifiedFailure({ error })))
        )
      )
    )
  );

  updateSystem$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SystemActions.updateSystem),
      switchMap(({ id, input }) =>
        this.systemService.updateSystem(id, input).pipe(
          map(system => SystemActions.updateSystemSuccess({ system })),
          catchError(error => of(SystemActions.updateSystemFailure({ error })))
        )
      )
    )
  );

  deleteSystem$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SystemActions.deleteSystem),
      switchMap(({ id }) =>
        this.systemService.deleteSystem(id).pipe(
          map(() => SystemActions.deleteSystemSuccess({ id })),
          catchError(error => of(SystemActions.deleteSystemFailure({ error })))
        )
      )
    )
  );
}
```

### 3.5 Apollo Client Konfiguration

Aktualisierung der Datei `src/app/app.config.ts`:

```typescript
import { ApplicationConfig, importProvidersFrom } from '@angular/core';
import { provideRouter } from '@angular/router';
import { provideStore } from '@ngrx/store';
import { provideEffects } from '@ngrx/effects';
import { provideStoreDevtools } from '@ngrx/store-devtools';
import { APOLLO_OPTIONS, ApolloModule } from 'apollo-angular';
import { HttpLink } from 'apollo-angular/http';
import { InMemoryCache } from '@apollo/client/core';
import { HttpClientModule } from '@angular/common/http';

import { routes } from './app.routes';
import { reducers, metaReducers } from './store';
import { SystemEffects } from './features/systems/store/system.effects';

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes),
    provideStore(reducers, { metaReducers }),
    provideEffects([SystemEffects]),
    provideStoreDevtools({
      maxAge: 25,
      logOnly: false
    }),
    importProvidersFrom(ApolloModule, HttpClientModule),
    {
      provide: APOLLO_OPTIONS,
      useFactory: (httpLink: HttpLink) => {
        return {
          cache: new InMemoryCache(),
          link: httpLink.create({
            uri: 'https://localhost:7007/graphql',
          }),
          defaultOptions: {
            watchQuery: {
              fetchPolicy: 'network-only',
              errorPolicy: 'ignore',
            },
            query: {
              fetchPolicy: 'network-only',
              errorPolicy: 'all',
            },
          }
        };
      },
      deps: [HttpLink],
    }
  ]
};
```

### 3.6 Anpassung der Komponenten

#### 3.6.1 SystemListComponent

Aktualisierung der Datei `src/app/features/systems/components/system-list/system-list.component.ts`:

```typescript
import { Component, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';

import { System } from '../../models/system.interface';
import { SystemStatus } from '../../models/system-status.enum';
import * as SystemActions from '../../store/system.actions';
import * as SystemSelectors from '../../store/system.selectors';

@Component({
  selector: 'app-system-list',
  templateUrl: './system-list.component.html',
  styleUrls: ['./system-list.component.scss']
})
export class SystemListComponent implements OnInit {
  systems$: Observable<System[]>;
  loading$: Observable<boolean>;
  error$: Observable<any>;

  constructor(private store: Store) {
    this.systems$ = this.store.select(SystemSelectors.selectFilteredSystems);
    this.loading$ = this.store.select(SystemSelectors.selectSystemsLoading);
    this.error$ = this.store.select(SystemSelectors.selectSystemsError);
  }

  ngOnInit(): void {
    this.loadSystems();
  }

  loadSystems(): void {
    this.store.dispatch(SystemActions.loadSystems());
  }

  deleteSystem(id: number): void {
    if (confirm('Sind Sie sicher, dass Sie dieses System löschen möchten?')) {
      this.store.dispatch(SystemActions.deleteSystem({ id }));
    }
  }

  filterByStatus(status: SystemStatus | null): void {
    this.store.dispatch(SystemActions.setSystemFilters({ 
      filters: { status: status || undefined } 
    }));
  }

  search(term: string): void {
    this.store.dispatch(SystemActions.setSystemFilters({ 
      filters: { search: term } 
    }));
  }
}
```

#### 3.6.2 SystemFormComponent

Aktualisierung der Datei `src/app/features/systems/components/system-form/system-form.component.ts`:

```typescript
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { filter, take } from 'rxjs/operators';

import { System } from '../../models/system.interface';
import { SystemStatus } from '../../models/system-status.enum';
import { CreateSystemInput } from '../../models/system-input.interface';
import * as SystemActions from '../../store/system.actions';
import * as SystemSelectors from '../../store/system.selectors';

@Component({
  selector: 'app-system-form',
  templateUrl: './system-form.component.html',
  styleUrls: ['./system-form.component.scss']
})
export class SystemFormComponent implements OnInit {
  systemForm: FormGroup;
  systemId: number | null = null;
  isEditMode = false;
  loading$: Observable<boolean>;
  error$: Observable<any>;
  systemStatusOptions = Object.values(SystemStatus);

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private store: Store
  ) {
    this.systemForm = this.createForm();
    this.loading$ = this.store.select(SystemSelectors.selectSystemsLoading);
    this.error$ = this.store.select(SystemSelectors.selectSystemsError);
  }

  ngOnInit(): void {
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      if (id) {
        this.systemId = +id;
        this.isEditMode = true;
        this.loadSystem(this.systemId);
      }
    });
  }

  createForm(): FormGroup {
    return this.fb.group({
      name: ['', [Validators.required]],
      description: [''],
      status: [SystemStatus.OPERATIONAL, [Validators.required]]
    });
  }

  loadSystem(id: number): void {
    this.store.dispatch(SystemActions.loadSystem({ id }));
    this.store.select(SystemSelectors.selectSelectedSystem)
      .pipe(
        filter(system => !!system),
        take(1)
      )
      .subscribe(system => {
        if (system) {
          this.systemForm.patchValue({
            name: system.name,
            description: system.description,
            status: system.status
          });
        }
      });
  }

  onSubmit(): void {
    if (this.systemForm.invalid) {
      return;
    }

    const formValue = this.systemForm.value;

    if (this.isEditMode && this.systemId) {
      // In einem realen Szenario würden wir hier das vollständige SystemInput erstellen
      // Für dieses Beispiel verwenden wir einen vereinfachten Ansatz
      this.store.select(SystemSelectors.selectSelectedSystem)
        .pipe(take(1))
        .subscribe(system => {
          if (system) {
            const input = {
              systemID: system.systemID,
              name: formValue.name,
              description: formValue.description,
              status: formValue.status,
              createdAt: system.createdAt,
              updatedAt: new Date()
            };
            this.store.dispatch(SystemActions.updateSystem({ 
              id: this.systemId!, 
              input 
            }));
          }
        });
    } else {
      const input: CreateSystemInput = {
        name: formValue.name,
        description: formValue.description,
        status: formValue.status
      };
      this.store.dispatch(SystemActions.createSystemSimplified({ input }));
    }

    // Nach dem Speichern zur Liste navigieren
    this.router.navigate(['/systems']);
  }

  onCancel(): void {
    this.router.navigate(['/systems']);
  }
}
```

## 4. Implementierungsschritte

1. **Anpassung der Datenmodelle**
   - System Interface aktualisieren
   - SystemStatus Enum aktualisieren
   - Input-Interfaces erstellen

2. **GraphQL-Integration**
   - Apollo Client konfigurieren
   - GraphQL-Schemas definieren
   - SystemService implementieren

3. **Store-Integration**
   - Actions definieren
   - Reducer implementieren
   - Selectors implementieren
   - Effects implementieren

4. **Komponenten anpassen**
   - SystemListComponent an den Store anbinden
   - SystemFormComponent für die Verwendung der neuen Datenstruktur anpassen

5. **Testing**
   - Unit-Tests für den SystemService
   - Unit-Tests für die Store-Komponenten
   - Integration-Tests für die Komponenten

## 5. Fazit

Die Integration des bereitgestellten GraphQL-Schemas in das StoerungsBuddy-Frontend erfordert einige Anpassungen an den bestehenden Datenmodellen und Komponenten. Die Hauptunterschiede liegen in der Struktur des System-Modells und den verfügbaren Operationen.

Durch die Verwendung von Apollo Client für die GraphQL-Integration und NgRx für das State Management wird eine saubere Trennung von Verantwortlichkeiten erreicht. Die Komponenten interagieren nur mit dem Store, während der SystemService die Kommunikation mit dem GraphQL-Endpunkt übernimmt.

Die vorgeschlagene Implementierung berücksichtigt die Unterschiede zwischen dem aktuellen Frontend-Modell und dem GraphQL-Schema und bietet eine flexible Lösung, die bei Bedarf erweitert werden kann.
      // Filter by status
      if (filters.status && system.status !== filters.status) {
        return false;
      }
      
      // Filter by search term
      if (filters.search && filters.search.trim() !== '') {
        const searchTerm = filters.search.toLowerCase();
        return (
          system.name.toLowerCase().includes(searchTerm) ||
          (system.description && system.description.toLowerCase().includes(searchTerm))
        );
      }
      
      return true;
    });
  }
);

export const selectSelectedSystemId = createSelector(
  selectSystemState,
  (state: SystemState) => state.selectedSystemId
);

export const selectSelectedSystem = createSelector(
  selectSystemState,
  selectSelectedSystemId,
  (state, selectedId) => selectedId ? state.entities[selectedId] : null
);
```
);
export const updateSystemFailure = createAction(
  '[System] Update System Failure',
  props<{ error: any }>()
);

// Delete System
export const deleteSystem = createAction(
  '[System] Delete System',
  props<{ id: number }>()
);
export const deleteSystemSuccess = createAction(
  '[System] Delete System Success',
  props<{ id: number }>()
);
export const deleteSystemFailure = createAction(
  '[System] Delete System Failure',
  props<{ error: any }>()
);

// Set System Filters
export const setSystemFilters = createAction(
  '[System] Set System Filters',
  props<{ filters: { status?: SystemStatus; search?: string } }>()
);
```
  ${SYSTEM_FRAGMENT}
`;

export const DELETE_SYSTEM = gql`
  mutation DeleteSystem($id: Int!) {
    deleteSystem(id: $id)
  }
`;
```
  
  // Zusätzliche Frontend-Eigenschaften (optional)
  category?: SystemCategory;
  dependencies?: string[];
  responsibleTeam?: string;
  contactPerson?: string;
  lastIncident?: Date;
  maintenanceWindow?: {
    start: string;
    end: string;
    frequency: string;
  };
}
```
- `contactPerson` (string)
- `lastIncident` (Date)
- `maintenanceWindow` (Object)

Außerdem verwendet das Schema `systemID` vom Typ `Int` statt `id` vom Typ `string`.