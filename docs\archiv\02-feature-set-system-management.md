# Feature-Set: Systemverwaltung (Stammdatenpflege)

## Übersicht

Dieses Feature-Set umfasst die zentrale Verwaltung von Systemen/Applikationen in einer nicht-hierarchischen Struktur, die als Grundlage für Störungsmeldungen dienen.

## Fachliche Anforderungen

### F-SYS-001: Systemliste anzeigen
**Als** Administrator  
**möchte ich** eine Übersicht aller verwalteten Systeme sehen  
**damit** ich einen schnellen Überblick über alle verfügbaren Systeme habe

#### Akzeptanzkriterien
- [ ] Tabellarische Darstellung aller Systeme
- [ ] Anzeige von Name, Beschreibung, Status und letzter Aktualisierung
- [ ] Filterung nach System-Status (OPERATIONAL, DEGRADED, OUTAGE, MAINTENANCE, UNKNOWN)
- [ ] Suchfunktion nach Systemname
- [ ] Sortierung nach verschiedenen Spalten

### F-SYS-002: System hinzufügen
**Als** Administrator  
**möchte ich** neue Systeme zur Verwaltung hinzufügen können  
**damit** diese für Störungsmeldungen verfügbar sind

#### Akzeptanzkriterien
- [ ] Formular mit Pflichtfeld "Name" und optionalem Feld "Beschreibung"
- [ ] Auswahl des initialen System-Status
- [ ] Validierung: Systemname darf nicht leer sein
- [ ] Validierung: Systemname sollte eindeutig sein
- [ ] Bestätigungsmeldung nach erfolgreichem Hinzufügen
- [ ] Automatische Aktualisierung der Systemliste

### F-SYS-003: System bearbeiten
**Als** Administrator  
**möchte ich** bestehende Systeme bearbeiten können  
**damit** ich Informationen aktuell halten kann

#### Akzeptanzkriterien
- [ ] Bearbeiten-Dialog mit vorausgefüllten Feldern
- [ ] Änderung von Name, Beschreibung und Status möglich
- [ ] Validierung wie bei Neuanlage
- [ ] Bestätigungsmeldung nach erfolgreichem Speichern
- [ ] Automatische Aktualisierung der Systemliste

### F-SYS-004: System entfernen
**Als** Administrator  
**möchte ich** nicht mehr benötigte Systeme entfernen können  
**damit** die Liste aktuell und übersichtlich bleibt

#### Akzeptanzkriterien
- [ ] Sicherheitsabfrage vor dem Löschen
- [ ] Prüfung auf bestehende Abhängigkeiten (Störungsmeldungen, Subscriptions)
- [ ] Warnung bei bestehenden Abhängigkeiten
- [ ] Bestätigungsmeldung nach erfolgreichem Löschen
- [ ] Automatische Aktualisierung der Systemliste

### F-SYS-005: System-Details anzeigen
**Als** Administrator  
**möchte ich** detaillierte Informationen zu einem System einsehen können  
**damit** ich vollständige Informationen zur Verfügung habe

#### Akzeptanzkriterien
- [ ] Detail-Ansicht mit allen Systeminformationen
- [ ] Anzeige von Erstellungs- und Änderungsdatum
- [ ] Verlinkung zu zugehörigen Störungsmeldungen
- [ ] Anzeige der Anzahl abonnierter Benutzer

## System-Status-Definitionen

| Status | Beschreibung | Verwendung |
|--------|--------------|------------|
| OPERATIONAL | System funktioniert normal | Standard-Betriebszustand |
| DEGRADED | Eingeschränkte Funktionalität | Teilausfälle oder Performance-Probleme |
| OUTAGE | System nicht verfügbar | Komplettausfall |
| MAINTENANCE | Wartungsarbeiten | Geplante Wartungsfenster |
| UNKNOWN | Status unbekannt | Monitoring nicht verfügbar |

## Technische Hinweise

- Verwendung der GraphQL-API für CRUD-Operationen
- Echtzeit-Updates über Subscriptions (optional)
- Responsive Design für verschiedene Bildschirmgrößen
- Barrierefreie Gestaltung

## Abhängigkeiten

- GraphQL-Backend-API
- Authentifizierung muss erfolgt sein
- Apollo Client für GraphQL-Kommunikation

### Hauptentitäten
```mermaid
erDiagram
    System {
        int systemID PK
        string name
        string description
        SystemStatus status
        datetime createdAt
        datetime updatedAt
    }
    
    User {
        int userID PK
        string name
        string email
        datetime registeredAt
        datetime lastActive
    }
    
    Incident {
        int incidentID PK
        string title
        string description
        IncidentType type
        Priority priority
        Status status
        int systemID FK
        datetime createdAt
        datetime resolvedAt
    }
    
    Subscription {
        int subscriptionID PK
        int userID FK
        int systemID FK
        SubscriptionType type
        datetime createdAt
    }
    
    System ||--o{ Incident : "affects"
    System ||--o{ Subscription : "subscribed to"
    User ||--o{ Subscription : "subscribes"
```