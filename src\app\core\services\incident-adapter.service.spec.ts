import { TestBed } from '@angular/core/testing';
import { IncidentAdapterService } from './incident-adapter.service';
import { Incident, IncidentType, CreateIncidentInput, UpdateIncidentInput } from '../models/incident.model';
import { IncidentResponse } from '../graphql/types';

describe('IncidentAdapterService', () => {
  let service: IncidentAdapterService;

  const mockApplicationResponse = {
    identifier: 'app-123',
    name: 'Test Application',
    description: 'Test Description',
    isDeleted: false,
    createdAt: '2024-01-01T10:00:00.000Z',
    updatedAt: '2024-01-01T10:00:00.000Z'
  };

  const mockIncidentResponse: IncidentResponse = {
    identifier: 'inc-123',
    title: 'Test Incident',
    type: IncidentType.STOERUNG,
    description: 'Test incident description',
    startTime: '2024-01-15T09:00:00.000Z',
    plannedEndTime: '2024-01-15T17:00:00.000Z',
    actualEndTime: '2024-01-15T16:30:00.000Z',
    alternatives: 'Use backup system',
    isResolved: true,
    createdAt: '2024-01-14T08:00:00.000Z',
    updatedAt: '2024-01-15T16:30:00.000Z',
    applications: [mockApplicationResponse]
  };

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(IncidentAdapterService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('fromGraphQLResponse', () => {
    it('should convert GraphQL response to Incident model with Date objects', () => {
      const result = service.fromGraphQLResponse(mockIncidentResponse);

      expect(result.identifier).toBe(mockIncidentResponse.identifier);
      expect(result.title).toBe(mockIncidentResponse.title);
      expect(result.type).toBe(mockIncidentResponse.type);
      expect(result.description).toBe(mockIncidentResponse.description);
      expect(result.alternatives).toBe(mockIncidentResponse.alternatives);
      expect(result.isResolved).toBe(mockIncidentResponse.isResolved);
      expect(result.applications).toEqual(mockIncidentResponse.applications);

      // Check that time fields are converted to Date objects
      expect(result.startTime).toBeInstanceOf(Date);
      expect(result.plannedEndTime).toBeInstanceOf(Date);
      expect(result.actualEndTime).toBeInstanceOf(Date);
      expect(result.createdAt).toBeInstanceOf(Date);
      expect(result.updatedAt).toBeInstanceOf(Date);

      // Check that Date values are correct
      expect((result.startTime as Date).toISOString()).toBe(mockIncidentResponse.startTime);
      expect((result.plannedEndTime as Date).toISOString()).toBe(mockIncidentResponse.plannedEndTime!);
      expect((result.actualEndTime as Date).toISOString()).toBe(mockIncidentResponse.actualEndTime!);
      expect((result.createdAt as Date).toISOString()).toBe(mockIncidentResponse.createdAt);
      expect((result.updatedAt as Date).toISOString()).toBe(mockIncidentResponse.updatedAt);
    });

    it('should handle optional time fields', () => {
      const responseWithoutOptionalFields: IncidentResponse = {
        ...mockIncidentResponse,
        plannedEndTime: undefined,
        actualEndTime: undefined,
        description: undefined,
        alternatives: undefined
      };

      const result = service.fromGraphQLResponse(responseWithoutOptionalFields);

      expect(result.plannedEndTime).toBeUndefined();
      expect(result.actualEndTime).toBeUndefined();
      expect(result.description).toBeUndefined();
      expect(result.alternatives).toBeUndefined();
      expect(result.startTime).toBeInstanceOf(Date);
      expect(result.createdAt).toBeInstanceOf(Date);
      expect(result.updatedAt).toBeInstanceOf(Date);
    });
  });

  describe('fromGraphQLResponseArray', () => {
    it('should convert array of GraphQL responses to Incident models', () => {
      const responses = [mockIncidentResponse, { ...mockIncidentResponse, identifier: 'inc-456' }];
      const result = service.fromGraphQLResponseArray(responses);

      expect(result.length).toBe(2);
      expect(result[0].identifier).toBe('inc-123');
      expect(result[1].identifier).toBe('inc-456');
      expect(result[0].startTime).toBeInstanceOf(Date);
      expect(result[1].startTime).toBeInstanceOf(Date);
    });

    it('should handle empty array', () => {
      const result = service.fromGraphQLResponseArray([]);
      expect(result).toEqual([]);
    });
  });

  describe('toGraphQLCreateInput', () => {
    it('should convert CreateIncidentInput with Date objects to GraphQL format', () => {
      const input: CreateIncidentInput = {
        title: 'New Incident',
        type: IncidentType.STOERUNG,
        description: 'New incident description',
        startTime: new Date('2024-01-15T09:00:00.000Z'),
        plannedEndTime: new Date('2024-01-15T17:00:00.000Z'),
        alternatives: 'Use backup',
        applicationIds: ['app-123', 'app-456']
      };

      const result = service.toGraphQLCreateInput(input);

      expect(result.title).toBe(input.title);
      expect(result.type).toBe(input.type);
      expect(result.description).toBe(input.description);
      expect(result.alternatives).toBe(input.alternatives);
      expect(result.applicationIds).toEqual(input.applicationIds);
      expect(result.startTime).toBe('2024-01-15T09:00:00.000Z');
      expect(result.plannedEndTime).toBe('2024-01-15T17:00:00.000Z');
    });

    it('should convert CreateIncidentInput with ISO strings to GraphQL format', () => {
      const input: CreateIncidentInput = {
        title: 'New Incident',
        type: IncidentType.STOERUNG,
        startTime: '2024-01-15T09:00:00.000Z',
        applicationIds: ['app-123']
      };

      const result = service.toGraphQLCreateInput(input);

      expect(result.startTime).toBe('2024-01-15T09:00:00.000Z');
      expect(result.plannedEndTime).toBeUndefined();
    });
  });

  describe('toGraphQLUpdateInput', () => {
    it('should convert UpdateIncidentInput with Date objects to GraphQL format', () => {
      const input: UpdateIncidentInput = {
        identifier: 'inc-123',
        title: 'Updated Incident',
        startTime: new Date('2024-01-15T09:00:00.000Z'),
        plannedEndTime: new Date('2024-01-15T17:00:00.000Z'),
        actualEndTime: new Date('2024-01-15T16:30:00.000Z')
      };

      const result = service.toGraphQLUpdateInput(input);

      expect(result.identifier).toBe(input.identifier);
      expect(result.title).toBe(input.title);
      expect(result.startTime).toBe('2024-01-15T09:00:00.000Z');
      expect(result.plannedEndTime).toBe('2024-01-15T17:00:00.000Z');
      expect(result.actualEndTime).toBe('2024-01-15T16:30:00.000Z');
    });

    it('should handle partial updates', () => {
      const input: UpdateIncidentInput = {
        identifier: 'inc-123',
        actualEndTime: new Date('2024-01-15T16:30:00.000Z')
      };

      const result = service.toGraphQLUpdateInput(input);

      expect(result.identifier).toBe('inc-123');
      expect(result.actualEndTime).toBe('2024-01-15T16:30:00.000Z');
      expect(result.title).toBeUndefined();
      expect(result.startTime).toBeUndefined();
    });
  });

  describe('toCreateInput', () => {
    it('should convert Incident model to CreateIncidentInput', () => {
      const incident: Incident = {
        identifier: 'inc-123',
        title: 'Test Incident',
        type: IncidentType.STOERUNG,
        description: 'Test description',
        startTime: new Date('2024-01-15T09:00:00.000Z'),
        plannedEndTime: new Date('2024-01-15T17:00:00.000Z'),
        alternatives: 'Use backup',
        isResolved: false,
        createdAt: new Date('2024-01-14T08:00:00.000Z'),
        updatedAt: new Date('2024-01-14T08:00:00.000Z'),
        applications: [mockApplicationResponse]
      };

      const result = service.toCreateInput(incident);

      expect(result.title).toBe(incident.title);
      expect(result.type).toBe(incident.type);
      expect(result.description).toBe(incident.description);
      expect(result.startTime).toBe(incident.startTime);
      expect(result.plannedEndTime).toBe(incident.plannedEndTime);
      expect(result.alternatives).toBe(incident.alternatives);
      expect(result.applicationIds).toEqual(['app-123']);
    });
  });

  describe('toUpdateInput', () => {
    it('should convert Incident model to UpdateIncidentInput', () => {
      const incident: Incident = {
        identifier: 'inc-123',
        title: 'Test Incident',
        type: IncidentType.STOERUNG,
        description: 'Test description',
        startTime: new Date('2024-01-15T09:00:00.000Z'),
        plannedEndTime: new Date('2024-01-15T17:00:00.000Z'),
        actualEndTime: new Date('2024-01-15T16:30:00.000Z'),
        alternatives: 'Use backup',
        isResolved: true,
        createdAt: new Date('2024-01-14T08:00:00.000Z'),
        updatedAt: new Date('2024-01-15T16:30:00.000Z'),
        applications: [mockApplicationResponse]
      };

      const result = service.toUpdateInput(incident);

      expect(result.identifier).toBe(incident.identifier);
      expect(result.title).toBe(incident.title);
      expect(result.type).toBe(incident.type);
      expect(result.description).toBe(incident.description);
      expect(result.startTime).toBe(incident.startTime);
      expect(result.plannedEndTime).toBe(incident.plannedEndTime);
      expect(result.actualEndTime).toBe(incident.actualEndTime);
      expect(result.alternatives).toBe(incident.alternatives);
      expect(result.applicationIds).toEqual(['app-123']);
    });

    it('should filter fields when fieldsToUpdate is specified', () => {
      const incident: Incident = {
        identifier: 'inc-123',
        title: 'Test Incident',
        type: IncidentType.STOERUNG,
        startTime: new Date('2024-01-15T09:00:00.000Z'),
        actualEndTime: new Date('2024-01-15T16:30:00.000Z'),
        isResolved: true,
        createdAt: new Date('2024-01-14T08:00:00.000Z'),
        updatedAt: new Date('2024-01-15T16:30:00.000Z'),
        applications: []
      };

      const result = service.toUpdateInput(incident, ['title', 'actualEndTime']);

      expect(result.identifier).toBe('inc-123');
      expect(result.title).toBe('Test Incident');
      expect(result.actualEndTime).toBe(incident.actualEndTime);
      expect(result.type).toBeUndefined();
      expect(result.startTime).toBeUndefined();
    });
  });

  describe('createResolveInput', () => {
    it('should create resolve input with custom end time', () => {
      const endTime = new Date('2024-01-15T16:30:00.000Z');
      const result = service.createResolveInput('inc-123', endTime);

      expect(result.identifier).toBe('inc-123');
      expect(result.actualEndTime).toBe(endTime);
    });

    it('should create resolve input with current time when no end time provided', () => {
      const beforeCall = Date.now();
      const result = service.createResolveInput('inc-123');
      const afterCall = Date.now();

      expect(result.identifier).toBe('inc-123');
      expect(result.actualEndTime).toBeInstanceOf(Date);
      const endTime = result.actualEndTime as Date;
      expect(endTime.getTime()).toBeGreaterThanOrEqual(beforeCall);
      expect(endTime.getTime()).toBeLessThanOrEqual(afterCall);
    });
  });

  describe('validateDateTimeFields', () => {
    it('should validate valid CreateIncidentInput', () => {
      const input: CreateIncidentInput = {
        title: 'Test',
        type: IncidentType.STOERUNG,
        startTime: new Date('2024-01-15T09:00:00.000Z'),
        plannedEndTime: new Date('2024-01-15T17:00:00.000Z'),
        applicationIds: []
      };

      const result = service.validateDateTimeFields(input);

      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual([]);
    });

    it('should validate valid UpdateIncidentInput', () => {
      const input: UpdateIncidentInput = {
        identifier: 'inc-123',
        actualEndTime: '2024-01-15T16:30:00.000Z'
      };

      const result = service.validateDateTimeFields(input);

      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual([]);
    });

    it('should detect missing startTime in CreateIncidentInput', () => {
      const input = {
        title: 'Test',
        type: IncidentType.STOERUNG,
        applicationIds: []
      } as unknown as CreateIncidentInput;

      const result = service.validateDateTimeFields(input);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('startTime is required');
    });

    it('should detect invalid date formats', () => {
      const input: CreateIncidentInput = {
        title: 'Test',
        type: IncidentType.STOERUNG,
        startTime: 'invalid-date',
        plannedEndTime: 'also-invalid',
        applicationIds: []
      };

      const result = service.validateDateTimeFields(input);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid startTime format');
      expect(result.errors).toContain('Invalid plannedEndTime format');
    });

    it('should detect logical date order issues', () => {
      const input: CreateIncidentInput = {
        title: 'Test',
        type: IncidentType.STOERUNG,
        startTime: new Date('2024-01-15T17:00:00.000Z'),
        plannedEndTime: new Date('2024-01-15T09:00:00.000Z'), // Before start time
        applicationIds: []
      };

      const result = service.validateDateTimeFields(input);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('plannedEndTime must be after startTime');
    });

    it('should handle equal start and planned end times', () => {
      const sameTime = new Date('2024-01-15T09:00:00.000Z');
      const input: CreateIncidentInput = {
        title: 'Test',
        type: IncidentType.STOERUNG,
        startTime: sameTime,
        plannedEndTime: sameTime,
        applicationIds: []
      };

      const result = service.validateDateTimeFields(input);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('plannedEndTime must be after startTime');
    });
  });
});