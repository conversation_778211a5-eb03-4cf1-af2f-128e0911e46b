import { gql } from 'apollo-angular';

export const GET_ALL_INCIDENTS = gql`
  query GetAllIncidents {
    incidents {
      identifier
      title
      type
      description
      startTime
      plannedEndTime
      actualEndTime
      alternatives
      isResolved
      createdAt
      updatedAt
      applications {
        identifier
        name
        description
        isDeleted
        createdAt
        updatedAt
      }
    }
  }
`;

export const GET_MY_INCIDENTS = gql`
  query GetMyIncidents($isResolved: Boolean) {
    myIncidents(isResolved: $isResolved) {
      identifier
      title
      type
      description
      startTime
      plannedEndTime
      actualEndTime
      alternatives
      isResolved
      createdAt
      updatedAt
      applications {
        identifier
        name
        description
        isDeleted
        createdAt
        updatedAt
      }
    }
  }
`;
