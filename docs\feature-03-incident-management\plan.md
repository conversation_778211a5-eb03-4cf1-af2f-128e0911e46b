# Implementierungsplan: Incident Management System

## Übersicht

Dieser Implementierungsplan basiert auf dem EPIC [`docs/03-EPIC-incident-management.md`](docs/03-EPIC-incident-management.md) und berücksichtigt die Erkenntnisse aus [`docs/learnings/graphql-learnings.md`](docs/learnings/graphql-learnings.md).

## 🎯 Aktueller Implementierungsstand

### ✅ Phase 1: Grundlegende Meldungsübersicht - **ABGESCHLOSSEN**
- **Projektstruktur**: Vollständige Ordnerstruktur erstellt
- **Datenmodelle**: Incident und Application Models implementiert
- **GraphQL Service**: Queries, Mutations und Service mit Apollo Client Integration
- **NgRx Store**: Actions, State, Reducer, Effects und Selectors vollständig implementiert
- **Incident List Component**: Standalone Component mit Angular Material Table, HTML Template und SCSS Styling

### ✅ Phase 2: Meldungserstellung - **ABGESCHLOSSEN**
- **Create Incident Dialog**: Vollständig implementiert mit Reactive Forms und Validierung
- **Application Selector Component**: Standalone Component mit ControlValueAccessor Interface
- **Reactive Forms mit Validierung**: Dynamische Validierung basierend auf Incident-Typ

### 🔄 Phase 3: Meldungsbearbeitung und Status-Updates - **ABGESCHLOSSEN**
- **Edit Incident Dialog**: Vollständig implementiert mit vorausgefüllten Formularen
- **Update-Logik**: Integration mit NgRx Store und GraphQL Service

### ⏳ Phase 4-5: **AUSSTEHEND**
- Meldungslöschung und Archivierung
- Real-time Updates und Benachrichtigungen

## Wichtige GraphQL-Erkenntnisse für die Implementierung

### 1. Create Mutations
- **Pattern**: Input-Object Pattern verwenden
- **Variables**: `variables: { input }` (verschachtelt)
- **Schema**: `createIncident(input: CreateIncidentInput!)`

### 2. Update Mutations  
- **Pattern**: Direkte Parameter verwenden
- **Variables**: Individuelle Parameter extrahieren
- **Schema**: `updateIncident(identifier: ID!, title: String, ...)`

### 3. Delete Mutations
- **Type**: `UUID!` statt `ID!` verwenden
- **Response**: Vollständige Objekte zurückgeben
- **Schema**: `deleteIncident(identifier: UUID!): Incident`

### 4. Type Safety
- **Konsistenz**: Gleiche Typen in allen GraphQL-Operationen
- **Required Fields**: Backend-Anforderungen exakt abbilden

---

# Phase 1: Grundlegende Meldungsübersicht

## 1.1 Projektstruktur erstellen

### Checkliste
- [x] **Ordnerstruktur erstellen**
  ```bash
  mkdir -p src/app/features/incident-management/{components,dialogs,services,models,store}
  mkdir -p src/app/features/incident-management/components/{incident-list,incident-filters,application-selector}
  mkdir -p src/app/features/incident-management/dialogs/{create-incident-dialog,edit-incident-dialog,resolve-incident-dialog,delete-incident-dialog}
  ```

- [x] **Routing-Datei erstellen**
  - Datei: `src/app/features/incident-management/incident-management.routes.ts`
  - Lazy Loading konfigurieren
  - ~~Guard für Admin-Berechtigung~~ (Guard noch nicht implementiert)

### Implementierungsdetails
```typescript
// incident-management.routes.ts
import { Routes } from '@angular/router';
import { AdminGuard } from '../../core/guards/admin.guard';

export const INCIDENT_MANAGEMENT_ROUTES: Routes = [
  {
    path: '',
    canActivate: [AdminGuard],
    children: [
      {
        path: '',
        loadComponent: () => import('./components/incident-list/incident-list.component')
          .then(m => m.IncidentListComponent)
      }
    ]
  }
];
```

## 1.2 Datenmodelle definieren

### Checkliste
- [x] **Incident Model erstellen**
  - Datei: `src/app/features/incident-management/models/incident.model.ts`
  - Interfaces basierend auf GraphQL Schema
  - Enums für IncidentType

- [x] **Application Model erstellen**
  - Datei: `src/app/features/incident-management/models/application.model.ts`
  - Wiederverwendung aus Application Management

### Implementierungsdetails
```typescript
// incident.model.ts
export interface Incident {
  identifier: string;
  title: string;
  description?: string;
  type: IncidentType;
  startTime: string;
  plannedEndTime?: string;
  actualEndTime?: string;
  isResolved: boolean;
  applications: Application[];
}

export enum IncidentType {
  STOERUNG = 'STOERUNG',
  WARTUNGSFENSTER = 'WARTUNGSFENSTER',
  KEINE_STOERUNG = 'KEINE_STOERUNG'
}

export interface CreateIncidentInput {
  title: string;
  type: IncidentType;
  description?: string;
  startTime: string;
  plannedEndTime?: string;
  alternatives?: string;
  applicationIds: string[];
}

export interface UpdateIncidentInput {
  identifier: string;
  title?: string;
  type?: IncidentType;
  description?: string;
  startTime?: string;
  plannedEndTime?: string;
  actualEndTime?: string;
  alternatives?: string;
  applicationIds?: string[];
}
```

## 1.3 GraphQL Service erstellen

### Checkliste
- [x] **GraphQL Queries definieren**
  - Datei: `src/app/features/incident-management/services/incident.queries.ts`
  - Korrekte Typen verwenden (`UUID!` für identifier)

- [x] **GraphQL Mutations definieren**
  - Datei: `src/app/features/incident-management/services/incident.mutations.ts`
  - Input-Object Pattern für Create
  - Direkte Parameter für Update
  - Vollständige Response-Objekte

- [x] **Incident Service implementieren**
  - Datei: `src/app/features/incident-management/services/incident.service.ts`
  - Apollo Client Integration
  - Cache-Update-Strategien

### Implementierungsdetails

#### GraphQL Queries
```typescript
// incident.queries.ts
import { gql } from 'apollo-angular';

export const GET_USER_INCIDENTS = gql`
  query GetUserIncidents($isResolved: Boolean) {
    incidentsForCurrentUser(isResolved: $isResolved) {
      identifier
      title
      type
      description
      startTime
      plannedEndTime
      actualEndTime
      isResolved
      applications {
        identifier
        name
        description
      }
    }
  }
`;

export const GET_ALL_APPLICATIONS = gql`
  query GetAllApplications {
    allApplications {
      identifier
      name
      description
      isDeleted
    }
  }
`;
```

#### GraphQL Mutations
```typescript
// incident.mutations.ts
import { gql } from 'apollo-angular';

// Create: Input-Object Pattern
export const CREATE_INCIDENT = gql`
  mutation CreateIncident($input: CreateIncidentInput!) {
    createIncident(input: $input) {
      identifier
      title
      type
      description
      startTime
      plannedEndTime
      actualEndTime
      isResolved
      applications {
        identifier
        name
      }
    }
  }
`;

// Update: Input-Object Pattern (Backend erwartet verschachteltes Input)
export const UPDATE_INCIDENT = gql`
  mutation UpdateIncident($input: UpdateIncidentInput!) {
    updateIncident(input: $input) {
      identifier
      title
      type
      description
      startTime
      plannedEndTime
      actualEndTime
      isResolved
      applications {
        identifier
        name
      }
    }
  }
`;

// Resolve: Einfache Mutation
export const RESOLVE_INCIDENT = gql`
  mutation ResolveIncident($identifier: ID!) {
    resolveIncident(identifier: $identifier) {
      identifier
      title
      isResolved
      actualEndTime
      applications {
        identifier
        name
      }
    }
  }
`;

// Delete: Boolean Response
export const DELETE_INCIDENT = gql`
  mutation DeleteIncident($identifier: ID!) {
    deleteIncident(identifier: $identifier)
  }
`;
#### Service Implementation
```typescript
// incident.service.ts
import { Injectable } from '@angular/core';
import { Apollo } from 'apollo-angular';
import { Observable } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class IncidentService {
  constructor(private apollo: Apollo) {}

  // Create: Input-Object Pattern
  createIncident(input: CreateIncidentInput): Observable<Incident> {
    return this.apollo.mutate<{ createIncident: Incident }>({
      mutation: CREATE_INCIDENT,
      variables: { input }, // Korrekte Verschachtelung
      update: (cache, { data }) => {
        if (data?.createIncident) {
          this.updateCacheAfterCreate(cache, data.createIncident);
        }
      }
    }).pipe(
      map(result => result.data!.createIncident),
      catchError(this.handleError)
    );
  }

  // Update: Input-Object Pattern (Backend erwartet verschachteltes Input)
  updateIncident(input: UpdateIncidentInput): Observable<Incident> {
    return this.apollo.mutate<{ updateIncident: Incident }>({
      mutation: UPDATE_INCIDENT,
      variables: { input }, // Backend erwartet verschachteltes Input
      update: (cache, { data }) => {
        if (data?.updateIncident) {
          this.updateCacheAfterUpdate(cache, data.updateIncident);
        }
      }
    }).pipe(
      map(result => result.data!.updateIncident),
      catchError(this.handleError)
    );
  }

  // Resolve: Einfache Parameter
  resolveIncident(identifier: string): Observable<Incident> {
    return this.apollo.mutate<{ resolveIncident: Incident }>({
      mutation: RESOLVE_INCIDENT,
      variables: { identifier },
      update: (cache, { data }) => {
        if (data?.resolveIncident) {
          this.updateCacheAfterResolve(cache, data.resolveIncident);
        }
      }
    }).pipe(
      map(result => result.data!.resolveIncident),
      catchError(this.handleError)
    );
  }

  // Delete: Boolean Response
  deleteIncident(identifier: string): Observable<boolean> {
    return this.apollo.mutate<{ deleteIncident: boolean }>({
      mutation: DELETE_INCIDENT,
      variables: { identifier },
      update: (cache) => {
        this.updateCacheAfterDelete(cache, identifier);
      }
    }).pipe(
      map(result => result.data!.deleteIncident),
      catchError(this.handleError)
    );
  }

  getUserIncidents(isResolved?: boolean): Observable<Incident[]> {
    return this.apollo.query<{ incidentsForCurrentUser: Incident[] }>({
      query: GET_USER_INCIDENTS,
      variables: { isResolved },
      fetchPolicy: 'cache-and-network'
    }).pipe(
      map(result => result.data.incidentsForCurrentUser),
      catchError(this.handleError)
    );
  }

  private updateCacheAfterCreate(cache: any, newIncident: Incident): void {
    try {
      const existingData = cache.readQuery<{ incidentsForCurrentUser: Incident[] }>({
        query: GET_USER_INCIDENTS,
        variables: { isResolved: false }
      });

      if (existingData) {
        cache.writeQuery({
          query: GET_USER_INCIDENTS,
          variables: { isResolved: false },
          data: {
            incidentsForCurrentUser: [newIncident, ...existingData.incidentsForCurrentUser]
          }
        });
      }
    } catch (error) {
      console.warn('Cache update after create failed:', error);
    }
  }

  private updateCacheAfterUpdate(cache: any, updatedIncident: Incident): void {
    // Cache-Update-Logik für Updates
    try {
      const existingData = cache.readQuery<{ incidentsForCurrentUser: Incident[] }>({
        query: GET_USER_INCIDENTS,
        variables: { isResolved: false }
      });

      if (existingData) {
        const updatedIncidents = existingData.incidentsForCurrentUser.map(incident =>
          incident.identifier === updatedIncident.identifier ? updatedIncident : incident
        );

        cache.writeQuery({
          query: GET_USER_INCIDENTS,
          variables: { isResolved: false },
          data: {
            incidentsForCurrentUser: updatedIncidents
          }
        });
      }
    } catch (error) {
      console.warn('Cache update after update failed:', error);
    }
  }

  private updateCacheAfterResolve(cache: any, resolvedIncident: Incident): void {
    // Cache-Update-Logik für Resolve
    this.updateCacheAfterUpdate(cache, resolvedIncident);
  }

  private updateCacheAfterDelete(cache: any, deletedIdentifier: string): void {
    try {
      const existingData = cache.readQuery<{ incidentsForCurrentUser: Incident[] }>({
        query: GET_USER_INCIDENTS,
        variables: { isResolved: false }
      });

      if (existingData) {
        const filteredIncidents = existingData.incidentsForCurrentUser.filter(
          incident => incident.identifier !== deletedIdentifier
        );

        cache.writeQuery({
          query: GET_USER_INCIDENTS,
          variables: { isResolved: false },
          data: {
            incidentsForCurrentUser: filteredIncidents
          }
        });
      }
    } catch (error) {
      console.warn('Cache update after delete failed:', error);
    }
  }

  private handleError(error: any): Observable<never> {
    console.error('Incident service error:', error);
    throw error;
  }
}
```

## 1.4 NgRx Store Setup

### Checkliste
- [x] **Actions definieren**
  - Datei: `src/app/features/incident-management/store/incident.actions.ts`
  - CRUD-Aktionen
  - Filter-Aktionen

- [x] **State definieren**
  - Datei: `src/app/features/incident-management/store/incident.state.ts`
  - Loading States
  - Error Handling

- [x] **Reducer implementieren**
  - Datei: `src/app/features/incident-management/store/incident.reducer.ts`
  - Immutable Updates

- [x] **Effects implementieren**
  - Datei: `src/app/features/incident-management/store/incident.effects.ts`
  - Service-Integration

- [x] **Selectors definieren**
  - Datei: `src/app/features/incident-management/store/incident.selectors.ts`
  - Memoized Selectors

### Implementierungsdetails

#### Actions
```typescript
// incident.actions.ts
import { createActionGroup, emptyProps, props } from '@ngrx/store';
import { Incident, CreateIncidentInput, UpdateIncidentInput } from '../models/incident.model';

export const IncidentActions = createActionGroup({
  source: 'Incident',
  events: {
    // Load Actions
    'Load Incidents': props<{ isResolved?: boolean }>(),
    'Load Incidents Success': props<{ incidents: Incident[] }>(),
    'Load Incidents Failure': props<{ error: string }>(),

    // Create Actions
    'Create Incident': props<{ input: CreateIncidentInput }>(),
    'Create Incident Success': props<{ incident: Incident }>(),
    'Create Incident Failure': props<{ error: string }>(),

    // Update Actions
    'Update Incident': props<{ input: UpdateIncidentInput }>(),
    'Update Incident Success': props<{ incident: Incident }>(),
    'Update Incident Failure': props<{ error: string }>(),

    // Resolve Actions
    'Resolve Incident': props<{ identifier: string }>(),
    'Resolve Incident Success': props<{ incident: Incident }>(),
    'Resolve Incident Failure': props<{ error: string }>(),

    // Delete Actions
    'Delete Incident': props<{ identifier: string }>(),
    'Delete Incident Success': props<{ identifier: string }>(),
    'Delete Incident Failure': props<{ error: string }>(),

    // Filter Actions
    'Set Filter': props<{ filter: IncidentFilter }>(),
    'Clear Filter': emptyProps()
  }
});

export interface IncidentFilter {
  type?: IncidentType;
  isResolved?: boolean;
  applicationIds?: string[];
}
```

#### State
```typescript
// incident.state.ts
import { Incident } from '../models/incident.model';

export interface IncidentState {
  incidents: Incident[];
  loading: boolean;
  error: string | null;
  filter: IncidentFilter | null;
}

export const initialIncidentState: IncidentState = {
  incidents: [],
  loading: false,
  error: null,
  filter: null
};
```

#### Reducer
```typescript
// incident.reducer.ts
import { createReducer, on } from '@ngrx/store';
import { IncidentActions } from './incident.actions';
import { initialIncidentState } from './incident.state';

export const incidentReducer = createReducer(
  initialIncidentState,

  // Load Incidents
  on(IncidentActions.loadIncidents, (state) => ({
    ...state,
    loading: true,
    error: null
  })),

  on(IncidentActions.loadIncidentsSuccess, (state, { incidents }) => ({
    ...state,
    incidents,
    loading: false,
    error: null
  })),

  on(IncidentActions.loadIncidentsFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  // Create Incident
  on(IncidentActions.createIncident, (state) => ({
    ...state,
    loading: true,
    error: null
  })),

  on(IncidentActions.createIncidentSuccess, (state, { incident }) => ({
    ...state,
    incidents: [incident, ...state.incidents],
    loading: false,
    error: null
  })),

  on(IncidentActions.createIncidentFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  // Update Incident
  on(IncidentActions.updateIncident, (state) => ({
    ...state,
    loading: true,
    error: null
  })),

  on(IncidentActions.updateIncidentSuccess, (state, { incident }) => ({
    ...state,
    incidents: state.incidents.map(i => 
      i.identifier === incident.identifier ? incident : i
    ),
    loading: false,
    error: null
  })),

  on(IncidentActions.updateIncidentFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  // Resolve Incident
  on(IncidentActions.resolveIncident, (state) => ({
    ...state,
    loading: true,
    error: null
  })),

  on(IncidentActions.resolveIncidentSuccess, (state, { incident }) => ({
    ...state,
    incidents: state.incidents.map(i => 
      i.identifier === incident.identifier ? incident : i
    ),
    loading: false,
    error: null
  })),

  on(IncidentActions.resolveIncidentFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  // Delete Incident
  on(IncidentActions.deleteIncident, (state) => ({
    ...state,
    loading: true,
    error: null
  })),

  on(IncidentActions.deleteIncidentSuccess, (state, { identifier }) => ({
    ...state,
    incidents: state.incidents.filter(i => i.identifier !== identifier),
    loading: false,
    error: null
  })),

  on(IncidentActions.deleteIncidentFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  // Filter
  on(IncidentActions.setFilter, (state, { filter }) => ({
    ...state,
    filter
  })),

  on(IncidentActions.clearFilter, (state) => ({
    ...state,
    filter: null
  }))
);
```
#### Effects
```typescript
// incident.effects.ts
import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { of } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';

import { IncidentService } from '../services/incident.service';
import { IncidentActions } from './incident.actions';

@Injectable()
export class IncidentEffects {
  constructor(
    private actions$: Actions,
    private incidentService: IncidentService
  ) {}

  loadIncidents$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IncidentActions.loadIncidents),
      switchMap(({ isResolved }) =>
        this.incidentService.getUserIncidents(isResolved).pipe(
          map(incidents => IncidentActions.loadIncidentsSuccess({ incidents })),
          catchError(error => of(IncidentActions.loadIncidentsFailure({ 
            error: error.message || 'Fehler beim Laden der Incidents' 
          })))
        )
      )
    )
  );

  createIncident$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IncidentActions.createIncident),
      switchMap(({ input }) =>
        this.incidentService.createIncident(input).pipe(
          map(incident => IncidentActions.createIncidentSuccess({ incident })),
          catchError(error => of(IncidentActions.createIncidentFailure({ 
            error: error.message || 'Fehler beim Erstellen des Incidents' 
          })))
        )
      )
    )
  );

  updateIncident$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IncidentActions.updateIncident),
      switchMap(({ input }) =>
        this.incidentService.updateIncident(input).pipe(
          map(incident => IncidentActions.updateIncidentSuccess({ incident })),
          catchError(error => of(IncidentActions.updateIncidentFailure({ 
            error: error.message || 'Fehler beim Aktualisieren des Incidents' 
          })))
        )
      )
    )
  );

  resolveIncident$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IncidentActions.resolveIncident),
      switchMap(({ identifier }) =>
        this.incidentService.resolveIncident(identifier).pipe(
          map(incident => IncidentActions.resolveIncidentSuccess({ incident })),
          catchError(error => of(IncidentActions.resolveIncidentFailure({ 
            error: error.message || 'Fehler beim Beheben des Incidents' 
          })))
        )
      )
    )
  );

  deleteIncident$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IncidentActions.deleteIncident),
      switchMap(({ identifier }) =>
        this.incidentService.deleteIncident(identifier).pipe(
          map(() => IncidentActions.deleteIncidentSuccess({ identifier })),
          catchError(error => of(IncidentActions.deleteIncidentFailure({ 
            error: error.message || 'Fehler beim Löschen des Incidents' 
          })))
        )
      )
    )
  );
}
```

#### Selectors
```typescript
// incident.selectors.ts
import { createFeatureSelector, createSelector } from '@ngrx/store';
import { IncidentState } from './incident.state';

export const selectIncidentState = createFeatureSelector<IncidentState>('incidents');

export const selectAllIncidents = createSelector(
  selectIncidentState,
  (state) => state.incidents
);

export const selectIncidentsLoading = createSelector(
  selectIncidentState,
  (state) => state.loading
);

export const selectIncidentsError = createSelector(
  selectIncidentState,
  (state) => state.error
);

export const selectIncidentFilter = createSelector(
  selectIncidentState,
  (state) => state.filter
);

export const selectFilteredIncidents = createSelector(
  selectAllIncidents,
  selectIncidentFilter,
  (incidents, filter) => {
    if (!filter) return incidents;

    return incidents.filter(incident => {
      if (filter.type && incident.type !== filter.type) return false;
      if (filter.isResolved !== undefined && incident.isResolved !== filter.isResolved) return false;
      if (filter.applicationIds?.length && 
          !incident.applications.some(app => filter.applicationIds!.includes(app.identifier))) {
        return false;
      }
      return true;
    });
  }
);

export const selectActiveIncidents = createSelector(
  selectAllIncidents,
  (incidents) => incidents.filter(incident => !incident.isResolved)
);

export const selectResolvedIncidents = createSelector(
  selectAllIncidents,
  (incidents) => incidents.filter(incident => incident.isResolved)
);

export const selectIncidentById = (identifier: string) => createSelector(
  selectAllIncidents,
  (incidents) => incidents.find(incident => incident.identifier === identifier)
);
```

## 1.5 Incident List Component

### Checkliste
- [x] **Component erstellen**
  - Datei: `src/app/features/incident-management/components/incident-list/incident-list.component.ts`
  - Standalone Component
  - Angular Material Table

- [x] **Template erstellen**
  - Datei: `src/app/features/incident-management/components/incident-list/incident-list.component.html`
  - Responsive Design
  - ~~Sortierung und Filterung~~ (Filterung noch nicht implementiert)

- [x] **Styling erstellen**
  - Datei: `src/app/features/incident-management/components/incident-list/incident-list.component.scss`
  - Material Design Theme

### Implementierungsdetails

#### Component
```typescript
// incident-list.component.ts
import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTableModule } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDialog } from '@angular/material/dialog';
import { Store } from '@ngrx/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { Incident, IncidentType } from '../../models/incident.model';
import { IncidentActions } from '../../store/incident.actions';
import { 
  selectFilteredIncidents, 
  selectIncidentsLoading, 
  selectIncidentsError 
} from '../../store/incident.selectors';
import { CreateIncidentDialogComponent } from '../../dialogs/create-incident-dialog/create-incident-dialog.component';
import { EditIncidentDialogComponent } from '../../dialogs/edit-incident-dialog/edit-incident-dialog.component';
import { DeleteConfirmationDialogComponent } from '../../../../shared/components/delete-confirmation-dialog/delete-confirmation-dialog.component';

@Component({
  selector: 'app-incident-list',
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatTooltipModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './incident-list.component.html',
  styleUrls: ['./incident-list.component.scss']
})
export class IncidentListComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  incidents$: Observable<Incident[]> = this.store.select(selectFilteredIncidents);
  loading$: Observable<boolean> = this.store.select(selectIncidentsLoading);
  error$: Observable<string | null> = this.store.select(selectIncidentsError);

  displayedColumns: string[] = [
    'type',
    'title',
    'applications',
    'startTime',
    'plannedEndTime',
    'isResolved',
    'actions'
  ];

  readonly IncidentType = IncidentType;

  constructor(
    private store: Store,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadIncidents();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadIncidents(): void {
    this.store.dispatch(IncidentActions.loadIncidents({ isResolved: false }));
  }

  onCreateIncident(): void {
    const dialogRef = this.dialog.open(CreateIncidentDialogComponent, {
      width: '600px',
      disableClose: true
    });

    dialogRef.afterClosed().pipe(
      takeUntil(this.destroy$)
    ).subscribe(result => {
      if (result) {
        // Incident wurde erstellt, Liste wird automatisch über Store aktualisiert
      }
    });
  }

  onEditIncident(incident: Incident): void {
    const dialogRef = this.dialog.open(EditIncidentDialogComponent, {
      width: '600px',
      disableClose: true,
      data: { incident }
    });

    dialogRef.afterClosed().pipe(
      takeUntil(this.destroy$)
    ).subscribe(result => {
      if (result) {
        // Incident wurde aktualisiert
      }
    });
  }

  onResolveIncident(incident: Incident): void {
    if (confirm(`Möchten Sie den Incident "${incident.title}" als behoben markieren?`)) {
      this.store.dispatch(IncidentActions.resolveIncident({ 
        identifier: incident.identifier 
      }));
    }
  }

  onDeleteIncident(incident: Incident): void {
    const dialogRef = this.dialog.open(DeleteConfirmationDialogComponent, {
      width: '400px',
      data: {
        title: 'Incident löschen',
        message: `Möchten Sie den Incident "${incident.title}" wirklich löschen?`,
        confirmText: 'Löschen',
        cancelText: 'Abbrechen'
      }
    });

    dialogRef.afterClosed().pipe(
      takeUntil(this.destroy$)
    ).subscribe(result => {
      if (result) {
        this.store.dispatch(IncidentActions.deleteIncident({ 
          identifier: incident.identifier 
        }));
      }
    });
  }

  getIncidentTypeColor(type: IncidentType): string {
    switch (type) {
      case IncidentType.STOERUNG:
        return 'warn';
      case IncidentType.WARTUNGSFENSTER:
        return 'accent';
      case IncidentType.KEINE_STOERUNG:
        return 'primary';
      default:
        return 'primary';
    }
  }

  getIncidentTypeLabel(type: IncidentType): string {
    switch (type) {
      case IncidentType.STOERUNG:
        return 'Störung';
      case IncidentType.WARTUNGSFENSTER:
        return 'Wartung';
      case IncidentType.KEINE_STOERUNG:
        return 'Keine Störung';
      default:
        return type;
    }
  }

  formatDateTime(dateString: string): string {
    return new Date(dateString).toLocaleString('de-DE', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
}
```

#### Template
```html
<!-- incident-list.component.html -->
<div class="incident-list-container">
  <div class="header">
    <h2>Störungs- und Wartungsmeldungen</h2>
    <button mat-raised-button color="primary" (click)="onCreateIncident()">
      <mat-icon>add</mat-icon>
      Neue Meldung
    </button>
  </div>

  <div class="filters">
    <!-- Filter-Komponente wird hier eingefügt -->
    <!-- TODO: Implement incident-filters component -->
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="loading$ | async">
    <mat-spinner diameter="50"></mat-spinner>
    <p>Lade Meldungen...</p>
  </div>

  <!-- Error State -->
  <div class="error-container" *ngIf="error$ | async as error">
    <mat-icon color="warn">error</mat-icon>
    <p>{{ error }}</p>
    <button mat-button (click)="loadIncidents()">Erneut versuchen</button>
  </div>

  <!-- Table -->
  <div class="table-container" *ngIf="incidents$ | async as incidents; else emptyState">
    <mat-table [dataSource]="incidents" class="incident-table" *ngIf="incidents.length > 0">
      
      <!-- Type Column -->
      <ng-container matColumnDef="type">
        <mat-header-cell *matHeaderCellDef>Typ</mat-header-cell>
        <mat-cell *matCellDef="let incident">
          <mat-chip [color]="getIncidentTypeColor(incident.type)" selected>
            {{ getIncidentTypeLabel(incident.type) }}
          </mat-chip>
        </mat-cell>
      </ng-container>

      <!-- Title Column -->
      <ng-container matColumnDef="title">
        <mat-header-cell *matHeaderCellDef>Titel</mat-header-cell>
        <mat-cell *matCellDef="let incident">
          <div class="incident-title">
            <strong>{{ incident.title }}</strong>
            <div class="incident-description" *ngIf="incident.description">
              {{ incident.description }}
            </div>
          </div>
        </mat-cell>
      </ng-container>

      <!-- Applications Column -->
      <ng-container matColumnDef="applications">
        <mat-header-cell *matHeaderCellDef>Betroffene Anwendungen</mat-header-cell>
        <mat-cell *matCellDef="let incident">
          <div class="applications-list">
            <mat-chip *ngFor="let app of incident.applications" class="application-chip">
              {{ app.name }}
            </mat-chip>
          </div>
        </mat-cell>
      </ng-container>

      <!-- Start Time Column -->
      <ng-container matColumnDef="startTime">
        <mat-header-cell *matHeaderCellDef>Startzeit</mat-header-cell>
        <mat-cell *matCellDef="let incident">
          {{ formatDateTime(incident.startTime) }}
        </mat-cell>
      </ng-container>

      <!-- Planned End Time Column -->
      <ng-container matColumnDef="plannedEndTime">
        <mat-header-cell *matHeaderCellDef>Geplantes Ende</mat-header-cell>
        <mat-cell *matCellDef="let incident">
          {{ incident.plannedEndTime ? formatDateTime(incident.plannedEndTime) : '-' }}
        </mat-cell>
      </ng-container>

      <!-- Status Column -->
      <ng-container matColumnDef="isResolved">
        <mat-header-cell *matHeaderCellDef>Status</mat-header-cell>
        <mat-cell *matCellDef="let incident">
          <mat-chip [color]="incident.isResolved ? 'primary' : 'warn'" selected>
            <mat-icon *ngIf="incident.isResolved">check_circle</mat-icon>
            <mat-icon *ngIf="!incident.isResolved">warning</mat-icon>
            {{ incident.isResolved ? 'Behoben' : 'Aktiv' }}
          </mat-chip>
        </mat-cell>
      </ng-container>

      <!-- Actions Column -->
      <ng-container matColumnDef="actions">
        <mat-header-cell *matHeaderCellDef>Aktionen</mat-header-cell>
        <mat-cell *matCellDef="let incident">
          <div class="actions">
            <button mat-icon-button 
                    (click)="onEditIncident(incident)"
                    matTooltip="Bearbeiten">
              <mat-icon>edit</mat-icon>
            </button>
            
            <button mat-icon-button 
                    *ngIf="!incident.isResolved"
                    (click)="onResolveIncident(incident)"
                    matTooltip="Als behoben markieren"
                    color="primary">
              <mat-icon>check_circle</mat-icon>
            </button>
            
            <button mat-icon-button 
                    (click)="onDeleteIncident(incident)"
                    matTooltip="Löschen"
                    color="warn">
              <mat-icon>delete</mat-icon>
            </button>
          </div>
        </mat-cell>
      </ng-container>

      <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
      <mat-row *matRowDef="let row; columns: displayedColumns;" class="incident-row"></mat-row>
    </mat-table>
  </div>

  <!-- Empty State -->
  <ng-template #emptyState>
    <div class="empty-container" *ngIf="!(loading$ | async) && !(error$ | async)">
      <mat-icon class="empty-icon">info</mat-icon>
      <h3>Keine Meldungen vorhanden</h3>
      <p>Es wurden noch keine Störungs- oder Wartungsmeldungen erstellt.</p>
      <button mat-raised-button color="primary" (click)="onCreateIncident()">
        <mat-icon>add</mat-icon>
        Erste Meldung erstellen
      </button>
    </div>
  </ng-template>
</div>
```

#### Styling
```scss
// incident-list.component.scss
.incident-list-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    h2 {
      margin: 0;
      color: var(--mat-primary-color);
    }

    button {
      mat-icon {
        margin-right: 8px;
      }
    }
  }

  .filters {
    margin-bottom: 24px;
    padding: 16px;
    background: var(--mat-background-card);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .table-container {
    background: var(--mat-background-card);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .incident-table {
      width: 100%;

      .incident-title {
        .incident-description {
          font-size: 0.875rem;
          color: var(--mat-text-secondary);
          margin-top: 4px;
          max-width: 300px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .applications-list {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;

        .application-chip {
          font-size: 0.75rem;
          height: 24px;
        }
      }

      .actions {
        display: flex;
        gap: 4px;

        button {
          width: 32px;
          height: 32px;
          line-height: 32px;

          mat-icon {
            font-size: 18px;
            width: 18px;
            height: 18px;
          }
        }
      }

      .incident-row {
        &:hover {
          background-color: var(--mat-background-hover);
        }
      }
    }
  }

  .loading-container,
  .error-container,
  .empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px 24px;
    text-align: center;

    mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: 16px;

      &.empty-icon {
        color: var(--mat-text-secondary);
      }
    }

    h3 {
      margin: 0 0 8px 0;
      color: var(--mat-text-primary);
    }

    p {
      margin: 0 0 24px 0;
      color: var(--mat-text-secondary);
      max-width: 400px;
    }
  }

  .loading-container {
    mat-spinner {
      margin-bottom: 16px;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .incident-list-container {
    padding: 16px;

    .header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      button {
        align-self: center;
      }
    }

    .table-container {
      overflow-x: auto;

      .incident-table {
        min-width: 800px;
      }
    }
  }
}
```

---

# Phase 2: Meldungserstellung

## 2.1 Create Incident Dialog

### Checkliste
- [x] **Dialog Component erstellen**
  - Datei: `src/app/features/incident-management/dialogs/create-incident-dialog/create-incident-dialog.component.ts`
  - Reactive Forms
  - Validierung

- [x] **Application Selector Component**
  - Datei: `src/app/features/incident-management/components/application-selector/application-selector.component.ts`
  - Multi-Select Funktionalität
  - Integration mit Application Service

### Implementierungsdetails

#### Create Dialog Component
```typescript
// create-incident-dialog.component.ts
import { Component, OnInit, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { Store } from '@ngrx/store';

import { IncidentType, CreateIncidentInput } from '../../models/incident.model';
import { IncidentActions } from '../../store/incident.actions';
import { ApplicationSelectorComponent } from '../../components/application-selector/application-selector.component';

@Component({
  selector: 'app-create-incident-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatButtonModule,
    MatIconModule,
    ApplicationSelectorComponent
  ],
  templateUrl: './create-incident-dialog.component.html',
  styleUrls: ['./create-incident-dialog.component.scss']
})
export class CreateIncidentDialogComponent implements OnInit {
  incidentForm: FormGroup;
  readonly IncidentType = IncidentType;

  incidentTypes = [
    { value: IncidentType.STOERUNG, label: 'Störung' },
    { value: IncidentType.WARTUNGSFENSTER, label: 'Wartungsfenster' },
    { value: IncidentType.KEINE_STOERUNG, label: 'Keine Störung' }
  ];

  constructor(
    private fb: FormBuilder,
    private store: Store,
    private dialogRef: MatDialogRef<CreateIncidentDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.incidentForm = this.createForm();
  }

  ngOnInit(): void {
    // Form-Initialisierung
    this.setupFormValidation();
  }

  private createForm(): FormGroup {
    return this.fb.group({
      title: ['', [Validators.required, Validators.maxLength(200)]],
      type: [IncidentType.STOERUNG, Validators.required],
      description: ['', Validators.maxLength(1000)],
      startTime: [new Date(), Validators.required],
      plannedEndTime: [''],
      alternatives: [''],
      applicationIds: [[], Validators.required]
    });
  }

  private setupFormValidation(): void {
    // Dynamische Validierung basierend auf Incident-Typ
    this.incidentForm.get('type')?.valueChanges.subscribe(type => {
      const plannedEndTimeControl = this.incidentForm.get('plannedEndTime');
      
      if (type === IncidentType.WARTUNGSFENSTER) {
        plannedEndTimeControl?.setValidators([Validators.required]);
      } else {
        plannedEndTimeControl?.clearValidators();
      }
      
      plannedEndTimeControl?.updateValueAndValidity();
    });
  }

  onSubmit(): void {
    if (this.incidentForm.valid) {
      const formValue = this.incidentForm.value;
      
      const input: CreateIncidentInput = {
        title: formValue.title,
        type: formValue.type,
        description: formValue.description || undefined,
        startTime: formValue.startTime.toISOString(),
        plannedEndTime: formValue.plannedEndTime?.toISOString(),
        alternatives: formValue.alternatives || undefined,
        applicationIds: formValue.applicationIds
      };

      this.store.dispatch(IncidentActions.createIncident({ input }));
      this.dialogRef.close(true);
    } else {
      this.markFormGroupTouched();
    }
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }

  private markFormGroupTouched(): void {
    Object.keys(this.incidentForm.controls).forEach(key => {
      const control = this.incidentForm.get(key);
      control?.markAsTouched();
    });
  }

  getErrorMessage(fieldName: string): string {
    const control = this.incidentForm.get(fieldName);
    
    if (control?.hasError('required')) {
      return 'Dieses Feld ist erforderlich';
    }
    
    if (control?.hasError('maxlength')) {
      const maxLength = control.errors?.['maxlength'].requiredLength;
      return `Maximal ${maxLength} Zeichen erlaubt`;
    }
    
    return '';
  }
}
```
#### Create Dialog Template
```html
<!-- create-incident-dialog.component.html -->
<h2 mat-dialog-title>
  <mat-icon>add</mat-icon>
  Neue Meldung erstellen
</h2>

<mat-dialog-content>
  <form [formGroup]="incidentForm" class="incident-form">
    
    <!-- Titel -->
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Titel</mat-label>
      <input matInput formControlName="title" placeholder="Kurze Beschreibung des Problems">
      <mat-error>{{ getErrorMessage('title') }}</mat-error>
    </mat-form-field>

    <!-- Typ -->
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Typ</mat-label>
      <mat-select formControlName="type">
        <mat-option *ngFor="let type of incidentTypes" [value]="type.value">
          {{ type.label }}
        </mat-option>
      </mat-select>
      <mat-error>{{ getErrorMessage('type') }}</mat-error>
    </mat-form-field>

    <!-- Beschreibung -->
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Beschreibung</mat-label>
      <textarea matInput 
                formControlName="description" 
                rows="3"
                placeholder="Detaillierte Beschreibung des Problems oder der Wartung">
      </textarea>
      <mat-error>{{ getErrorMessage('description') }}</mat-error>
    </mat-form-field>

    <!-- Betroffene Anwendungen -->
    <div class="full-width">
      <app-application-selector 
        formControlName="applicationIds"
        [required]="true">
      </app-application-selector>
      <mat-error *ngIf="incidentForm.get('applicationIds')?.invalid && incidentForm.get('applicationIds')?.touched">
        {{ getErrorMessage('applicationIds') }}
      </mat-error>
    </div>

    <!-- Zeitbereich -->
    <div class="time-section">
      <h3>Zeitraum</h3>
      
      <!-- Startzeit -->
      <mat-form-field appearance="outline" class="half-width">
        <mat-label>Startzeit</mat-label>
        <input matInput 
               [matDatepicker]="startPicker" 
               formControlName="startTime">
        <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
        <mat-datepicker #startPicker></mat-datepicker>
        <mat-error>{{ getErrorMessage('startTime') }}</mat-error>
      </mat-form-field>

      <!-- Geplante Endzeit -->
      <mat-form-field appearance="outline" class="half-width">
        <mat-label>Geplante Endzeit</mat-label>
        <input matInput 
               [matDatepicker]="endPicker" 
               formControlName="plannedEndTime">
        <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
        <mat-datepicker #endPicker></mat-datepicker>
        <mat-error>{{ getErrorMessage('plannedEndTime') }}</mat-error>
      </mat-form-field>
    </div>

    <!-- Alternativen/Workaround -->
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Alternativen/Workaround</mat-label>
      <textarea matInput 
                formControlName="alternatives" 
                rows="2"
                placeholder="Mögliche Workarounds oder alternative Lösungen">
      </textarea>
    </mat-form-field>

  </form>
</mat-dialog-content>

<mat-dialog-actions align="end">
  <button mat-button (click)="onCancel()">Abbrechen</button>
  <button mat-raised-button 
          color="primary" 
          (click)="onSubmit()"
          [disabled]="incidentForm.invalid">
    <mat-icon>save</mat-icon>
    Erstellen
  </button>
</mat-dialog-actions>
```

## 2.2 Application Selector Component

### Implementierungsdetails
```typescript
// application-selector.component.ts
import { Component, OnInit, Input, forwardRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatChipsModule } from '@angular/material/chips';
import { MatIconModule } from '@angular/material/icon';
import { Observable } from 'rxjs';

import { Application } from '../../../application-management/models/application.model';
import { ApplicationService } from '../../../application-management/services/application.service';

@Component({
  selector: 'app-application-selector',
  standalone: true,
  imports: [
    CommonModule,
    MatSelectModule,
    MatFormFieldModule,
    MatChipsModule,
    MatIconModule
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => ApplicationSelectorComponent),
      multi: true
    }
  ],
  templateUrl: './application-selector.component.html',
  styleUrls: ['./application-selector.component.scss']
})
export class ApplicationSelectorComponent implements OnInit, ControlValueAccessor {
  @Input() required = false;
  @Input() placeholder = 'Anwendungen auswählen';

  applications$: Observable<Application[]>;
  selectedApplicationIds: string[] = [];
  selectedApplications: Application[] = [];

  private onChange = (value: string[]) => {};
  private onTouched = () => {};

  constructor(private applicationService: ApplicationService) {
    this.applications$ = this.applicationService.getAllApplications();
  }

  ngOnInit(): void {
    // Load applications on init
  }

  // ControlValueAccessor implementation
  writeValue(value: string[]): void {
    this.selectedApplicationIds = value || [];
    this.updateSelectedApplications();
  }

  registerOnChange(fn: (value: string[]) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  onSelectionChange(selectedIds: string[]): void {
    this.selectedApplicationIds = selectedIds;
    this.updateSelectedApplications();
    this.onChange(selectedIds);
    this.onTouched();
  }

  private updateSelectedApplications(): void {
    this.applications$.subscribe(applications => {
      this.selectedApplications = applications.filter(app => 
        this.selectedApplicationIds.includes(app.identifier)
      );
    });
  }

  removeApplication(applicationId: string): void {
    const updatedIds = this.selectedApplicationIds.filter(id => id !== applicationId);
    this.onSelectionChange(updatedIds);
  }
}
```

#### Application Selector Template
```html
<!-- application-selector.component.html -->
<mat-form-field appearance="outline" class="full-width">
  <mat-label>{{ placeholder }}</mat-label>
  <mat-select multiple 
              [value]="selectedApplicationIds"
              (selectionChange)="onSelectionChange($event.value)">
    <mat-option *ngFor="let app of applications$ | async" 
                [value]="app.identifier"
                [disabled]="app.isDeleted">
      {{ app.name }}
      <span class="app-description" *ngIf="app.description">
        - {{ app.description }}
      </span>
    </mat-option>
  </mat-select>
  
  <!-- Selected Applications Display -->
  <div class="selected-applications" *ngIf="selectedApplications.length > 0">
    <mat-chip-set>
      <mat-chip *ngFor="let app of selectedApplications" 
                (removed)="removeApplication(app.identifier)">
        {{ app.name }}
        <mat-icon matChipRemove>cancel</mat-icon>
      </mat-chip>
    </mat-chip-set>
  </div>
</mat-form-field>
```

---

# Phase 3: Meldungsbearbeitung und Status-Updates

## 3.1 Edit Incident Dialog

### Checkliste
- [x] **Edit Dialog Component erstellen**
  - Datei: `src/app/features/incident-management/dialogs/edit-incident-dialog/edit-incident-dialog.component.ts`
  - Vorausgefüllte Formulare
  - Update-Logik

### Implementierungsdetails
```typescript
// edit-incident-dialog.component.ts
import { Component, OnInit, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { Store } from '@ngrx/store';

import { Incident, IncidentType, UpdateIncidentInput } from '../../models/incident.model';
import { IncidentActions } from '../../store/incident.actions';
import { ApplicationSelectorComponent } from '../../components/application-selector/application-selector.component';

@Component({
  selector: 'app-edit-incident-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatButtonModule,
    MatIconModule,
    ApplicationSelectorComponent
  ],
  templateUrl: './edit-incident-dialog.component.html',
  styleUrls: ['./edit-incident-dialog.component.scss']
})
export class EditIncidentDialogComponent implements OnInit {
  incidentForm: FormGroup;
  readonly IncidentType = IncidentType;

  incidentTypes = [
    { value: IncidentType.STOERUNG, label: 'Störung' },
    { value: IncidentType.WARTUNGSFENSTER, label: 'Wartungsfenster' },
    { value: IncidentType.KEINE_STOERUNG, label: 'Keine Störung' }
  ];

  constructor(
    private fb: FormBuilder,
    private store: Store,
    private dialogRef: MatDialogRef<EditIncidentDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { incident: Incident }
  ) {
    this.incidentForm = this.createForm();
  }

  ngOnInit(): void {
    this.populateForm();
  }

  private createForm(): FormGroup {
    return this.fb.group({
      title: ['', [Validators.required, Validators.maxLength(200)]],
      type: [IncidentType.STOERUNG, Validators.required],
      description: ['', Validators.maxLength(1000)],
      startTime: [new Date(), Validators.required],
      plannedEndTime: [''],
      actualEndTime: [''],
      alternatives: [''],
      applicationIds: [[], Validators.required]
    });
  }

  private populateForm(): void {
    const incident = this.data.incident;
    
    this.incidentForm.patchValue({
      title: incident.title,
      type: incident.type,
      description: incident.description || '',
      startTime: new Date(incident.startTime),
      plannedEndTime: incident.plannedEndTime ? new Date(incident.plannedEndTime) : null,
      actualEndTime: incident.actualEndTime ? new Date(incident.actualEndTime) : null,
      alternatives: incident.alternatives || '',
      applicationIds: incident.applications.map(app => app.identifier)
    });
  }

  onSubmit(): void {
    if (this.incidentForm.valid) {
      const formValue = this.incidentForm.value;
      
      const input: UpdateIncidentInput = {
        identifier: this.data.incident.identifier,
        title: formValue.title,
        type: formValue.type,
        description: formValue.description || undefined,
        startTime: formValue.startTime.toISOString(),
        plannedEndTime: formValue.plannedEndTime?.toISOString(),
        actualEndTime: formValue.actualEndTime?.toISOString(),
        alternatives: formValue.alternatives || undefined,
        applicationIds: formValue.applicationIds
      };

      this.store.dispatch(IncidentActions.updateIncident({ input }));
      this.dialogRef.close(true);
    }
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }
}
```

---

# Phase 4: Meldungslöschung und Archivierung

## 4.1 Delete Confirmation Dialog

### Checkliste
- [ ] **Delete Dialog verwenden**
  - Wiederverwendung der shared Delete Confirmation Dialog
  - Abhängigkeitsprüfung implementieren

### Implementierungsdetails
```typescript
// Verwendung in incident-list.component.ts (bereits implementiert)
onDeleteIncident(incident: Incident): void {
  const dialogRef = this.dialog.open(DeleteConfirmationDialogComponent, {
    width: '400px',
    data: {
      title: 'Incident löschen',
      message: `Möchten Sie den Incident "${incident.title}" wirklich löschen?`,
      confirmText: 'Löschen',
      cancelText: 'Abbrechen'
    }
  });

  dialogRef.afterClosed().pipe(
    takeUntil(this.destroy$)
  ).subscribe(result => {
    if (result) {
      this.store.dispatch(IncidentActions.deleteIncident({ 
        identifier: incident.identifier 
      }));
    }
  });
}
```

---

# Phase 5: Real-time Updates und Benachrichtigungen

## 5.1 GraphQL Subscriptions

### Checkliste
- [ ] **Subscription Service erstellen**
  - Datei: `src/app/features/incident-management/services/incident-subscription.service.ts`
  - WebSocket-Verbindung
  - Real-time Updates

### Implementierungsdetails
```typescript
// incident-subscription.service.ts
import { Injectable } from '@angular/core';
import { Apollo } from 'apollo-angular';
import { Observable } from 'rxjs';
import { gql } from 'apollo-angular';

import { Incident } from '../models/incident.model';

const INCIDENT_CREATED_SUBSCRIPTION = gql`
  subscription OnIncidentCreated {
    currentUserIncidentCreated {
      identifier
      title
      type
      description
      startTime
      plannedEndTime
      isResolved
      applications {
        identifier
        name
      }
    }
  }
`;

const INCIDENT_UPDATED_SUBSCRIPTION = gql`
  subscription OnIncidentUpdated {
    currentUserIncidentUpdated {
      identifier
      title
      type
      description
      startTime
      plannedEndTime
      actualEndTime
      isResolved
      applications {
        identifier
        name
      }
    }
  }
`;

const INCIDENT_RESOLVED_SUBSCRIPTION = gql`
  subscription OnIncidentResolved {
    currentUserIncidentResolved {
      identifier
      title
      isResolved
      actualEndTime
      applications {
        identifier
        name
      }
    }
  }
`;

@Injectable({
  providedIn: 'root'
})
export class IncidentSubscriptionService {
  constructor(private apollo: Apollo) {}

  subscribeToIncidentCreated(): Observable<Incident> {
    return this.apollo.subscribe<{ currentUserIncidentCreated: Incident }>({
      query: INCIDENT_CREATED_SUBSCRIPTION
    }).pipe(
      map(result => result.data!.currentUserIncidentCreated)
    );
  }

  subscribeToIncidentUpdated(): Observable<Incident> {
    return this.apollo.subscribe<{ currentUserIncidentUpdated: Incident }>({
      query: INCIDENT_UPDATED_SUBSCRIPTION
    }).pipe(
      map(result => result.data!.currentUserIncidentUpdated)
    );
  }

  subscribeToIncidentResolved(): Observable<Incident> {
    return this.apollo.subscribe<{ currentUserIncidentResolved: Incident }>({
      query: INCIDENT_RESOLVED_SUBSCRIPTION
    }).pipe(
      map(result => result.data!.currentUserIncidentResolved)
    );
  }
}
```

## 5.2 Real-time Effects

### Implementierungsdetails
```typescript
// Erweiterte incident.effects.ts
@Injectable()
export class IncidentEffects {
  constructor(
    private actions$: Actions,
    private incidentService: IncidentService,
    private subscriptionService: IncidentSubscriptionService
  ) {}

  // Bestehende Effects...

  // Real-time Subscriptions
  subscribeToIncidentCreated$ = createEffect(() =>
    this.subscriptionService.subscribeToIncidentCreated().pipe(
      map(incident => IncidentActions.createIncidentSuccess({ incident }))
    )
  );

  subscribeToIncidentUpdated$ = createEffect(() =>
    this.subscriptionService.subscribeToIncidentUpdated().pipe(
      map(incident => IncidentActions.updateIncidentSuccess({ incident }))
    )
  );

  subscribeToIncidentResolved$ = createEffect(() =>
    this.subscriptionService.subscribeToIncidentResolved().pipe(
      map(incident => IncidentActions.resolveIncidentSuccess({ incident }))
    )
  );
}
```

---

# Implementierungsreihenfolge und Abhängigkeiten

## Reihenfolge der Umsetzung

### 1. Grundlagen (Phase 1)
1. **Projektstruktur erstellen** (1.1)
2. **Datenmodelle definieren** (1.2)
3. **GraphQL Service erstellen** (1.3)
4. **NgRx Store Setup** (1.4)
5. **Incident List Component** (1.5)

### 2. CRUD-Operationen (Phase 2-4)
1. **Create Dialog** (2.1)
2. **Application Selector** (2.2)
3. **Edit Dialog** (3.1)
4. **Delete Funktionalität** (4.1)

### 3. Erweiterte Features (Phase 5)
1. **Real-time Subscriptions** (5.1)
2. **Notification Service** (5.2)

## Abhängigkeiten

### Externe Abhängigkeiten
- [ ] **Application Management Feature** muss implementiert sein
- [ ] **Authentication System** muss funktional sein
- [ ] **GraphQL Backend** muss verfügbar sein
- [ ] **Shared Components** (Delete Confirmation Dialog)

### Interne Abhängigkeiten
- [ ] **Models** vor Services implementieren
- [ ] **Services** vor Store implementieren
- [ ] **Store** vor Components implementieren
- [ ] **Basic Components** vor Dialogs implementieren

## Testing-Strategie

### Unit Tests
```typescript
// Beispiel: incident.service.spec.ts
describe('IncidentService', () => {
  let service: IncidentService;
  let apolloSpy: jasmine.SpyObj<Apollo>;

  beforeEach(() => {
    const spy = jasmine.createSpyObj('Apollo', ['mutate', 'query']);
    
    TestBed.configureTestingModule({
      providers: [
        IncidentService,
        { provide: Apollo, useValue: spy }
      ]
    });
    
    service = TestBed.inject(IncidentService);
    apolloSpy = TestBed.inject(Apollo) as jasmine.SpyObj<Apollo>;
  });

  it('should create incident with correct GraphQL variables', () => {
    const input: CreateIncidentInput = {
      title: 'Test Incident',
      type: IncidentType.STOERUNG,
      startTime: '2025-05-27T10:00:00Z',
      applicationIds: ['app-1']
    };

    service.createIncident(input).subscribe();

    expect(apolloSpy.mutate).toHaveBeenCalledWith({
      mutation: CREATE_INCIDENT,
      variables: { input }, // Korrekte Verschachtelung
      update: jasmine.any(Function)
    });
  });
});
```

### Integration Tests
```typescript
// Beispiel: incident-list.component.spec.ts
describe('IncidentListComponent', () => {
  let component: IncidentListComponent;
  let store: MockStore;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [IncidentListComponent],
      providers: [
        provideMockStore({
          initialState: {
            incidents: {
              incidents: [],
              loading: false,
              error: null
            }
          }
        })
      ]
    });

    store = TestBed.inject(MockStore);
    component = TestBed.createComponent(IncidentListComponent).componentInstance;
  });

  it('should dispatch loadIncidents action on init', () => {
    spyOn(store, 'dispatch');
    
    component.ngOnInit();
    
    expect(store.dispatch).toHaveBeenCalledWith(
      IncidentActions.loadIncidents({ isResolved: false })
    );
  });
});
```

## Performance-Optimierungen

### 1. OnPush Change Detection
```typescript
@Component({
  // ...
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class IncidentListComponent {
  // Verwendung von Observables für automatische Change Detection
}
```

### 2. Virtual Scrolling
```typescript
// Für große Listen implementieren
import { ScrollingModule } from '@angular/cdk/scrolling';

// In Template:
// <cdk-virtual-scroll-viewport itemSize="50" class="viewport">
//   <div *cdkVirtualFor="let incident of incidents">
//     <!-- Incident Item -->
//   </div>
// </cdk-virtual-scroll-viewport>
```

### 3. Lazy Loading
```typescript
// Bereits in Routes implementiert
loadComponent: () => import('./components/incident-list/incident-list.component')
  .then(m => m.IncidentListComponent)
```

## Error Handling

### Service Level
```typescript
private handleError(error: any): Observable<never> {
  console.error('Incident service error:', error);
  
  // Spezifische GraphQL-Fehler behandeln
  if (error.graphQLErrors?.length > 0) {
    const graphQLError = error.graphQLErrors[0];
    
    switch (graphQLError.extensions?.code) {
      case 'UNAUTHORIZED':
        return throwError(() => new Error('Sie sind nicht berechtigt, diese Aktion auszuführen.'));
      case 'VALIDATION_ERROR':
        return throwError(() => new Error('Eingabedaten sind ungültig.'));
      default:
        return throwError(() => new Error(graphQLError.message));
    }
  }
  
  // Netzwerk-Fehler
  if (error.networkError) {
    return throwError(() => new Error('Netzwerkfehler. Bitte versuchen Sie es später erneut.'));
  }
  
  return throwError(() => new Error('Ein unbekannter Fehler ist aufgetreten.'));
}
```

### Component Level
```typescript
// Error-Handling in Components
error$ = this.store.select(selectIncidentsError);

// In Template:
// <div class="error-container" *ngIf="error$ | async as error">
//   <mat-icon color="warn">error</mat-icon>
//   <p>{{ error }}</p>
//   <button mat-button (click)="loadIncidents()">Erneut versuchen</button>
// </div>
```

---

# Abschluss und Deployment

## Pre-Deployment Checkliste

### Code Quality
- [ ] **ESLint/Prettier** konfiguriert und alle Regeln befolgt
- [ ] **TypeScript Strict Mode** aktiviert und alle Typen definiert
- [ ] **Unit Tests** für alle Services und Components geschrieben
- [ ] **Integration Tests** für kritische User Flows implementiert
- [ ] **E2E Tests** für Hauptfunktionalitäten erstellt

### Performance
- [ ] **Bundle Size** analysiert und optimiert
- [ ] **Lazy Loading** für alle Feature-Module implementiert
- [ ] **OnPush Change Detection** wo möglich verwendet
- [ ] **Virtual Scrolling** für große Listen implementiert
- [ ] **GraphQL Query-Optimierung** durchgeführt

### Accessibility
- [ ] **WCAG 2.1 AA** Compliance sichergestellt
- [ ] **Screen Reader** Tests durchgeführt
- [ ] **Keyboard Navigation** vollständig funktional
- [ ] **Color Contrast** Requirements erfüllt

### Security
- [ ] **Input Validation** auf Client und Server
- [ ] **XSS Protection** implementiert
- [ ] **CSRF Protection** aktiviert
- [ ] **Authentication/Authorization** korrekt implementiert

## Monitoring und Maintenance

### Logging
```typescript
// Structured Logging implementieren
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class LoggingService {
  logIncidentAction(action: string, incidentId: string, details?: any): void {
    console.log({
      timestamp: new Date().toISOString(),
      action,
      incidentId,
      details,
      userId: this.getCurrentUserId()
    });
  }
}
```

### Error Tracking
```typescript
// Integration mit Error-Tracking-Service
import * as Sentry from '@sentry/angular';

// In app.config.ts
Sentry.init({
  dsn: environment.sentryDsn,
  environment: environment.production ? 'production' : 'development'
});
```

### Performance Monitoring
```typescript
// Performance-Metriken sammeln
@Injectable()
export class PerformanceService {
  measureIncidentListLoad(): void {
    performance.mark('incident-list-start');
    
    // Nach dem Laden:
    performance.mark('incident-list-end');
    performance.measure('incident-list-load', 'incident-list-start', 'incident-list-end');
  }
}
```

Dieser Implementierungsplan bietet eine vollständige Anleitung zur Umsetzung des Incident Management Systems unter Berücksichtigung aller GraphQL-Learnings und Best Practices.