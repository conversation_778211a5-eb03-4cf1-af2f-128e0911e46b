<div class="incident-create-dialog">
  <!-- <PERSON><PERSON> Header -->
  <div mat-dialog-title class="dialog-header">
    <div class="title-content">
      <mat-icon class="title-icon">add_circle</mat-icon>
      <h2>{{dialogTitle}}</h2>
    </div>
    <button 
      mat-icon-button 
      [mat-dialog-close]="false"
      [disabled]="loading"
      class="close-button"
      aria-label="Dialog schließen">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <!-- Dialog Content -->
  <div mat-dialog-content class="dialog-content">
    <!-- Loading Spinner -->
    <div *ngIf="loading" class="loading-overlay">
      <mat-spinner diameter="40"></mat-spinner>
      <p>Vorfall wird erstellt...</p>
    </div>

    <!-- Error Message -->
    <div *ngIf="error && !loading" class="error-banner">
      <mat-icon class="error-icon">error</mat-icon>
      <div class="error-content">
        <h4><PERSON><PERSON> be<PERSON></h4>
        <p>{{error}}</p>
      </div>
    </div>

    <!-- Create Form -->
    <app-incident-create-form
      [class.form-disabled]="loading"
      (formSubmit)="onFormSubmit($event)"
      (formCancel)="onCancel()">
    </app-incident-create-form>
  </div>
</div>