# Implementierungs-Checkliste: Application Management UX Enhancement

**Referenz**: Dieses Dokument basiert auf den architektonischen Spezifikationen zur Verbesserung des Application Managements, um es an die UX-Muster des Incident Managements anzugleichen.

**Ziel**: Systematische Umsetzung der definierten Verbesserungen durch eine phasenweise und priorisierte Abarbeitung der Aufgaben.

---

## Geschätzter Zeitplan und Aufwand

- **Gesamtaufwand**: Hoch (ca. 8-12 Personentage)
- **Phase 1 (NgRx)**: 4-5 Tage
- **Phase 2 (Dialoge)**: 2-3 Tage
- **Phase 3 (Accessibility)**: 1-2 Tage
- **Phase 4 (Detailansicht)**: 1-2 Tage

---

## Phase 1: NgRx State Management Migration (Priorität: Hoch)

**Ziel**: Migration des lokalen State Managements der Anwendungs-Komponenten zu einem zentralisierten NgRx-Store, um eine einheitliche Datenhaltung und Reaktivität zu gewährleisten.

**Abhängigkeiten**: Keine. Diese Phase ist grundlegend für weitere Refactorings.

### Aufgaben

**Infrastruktur & Setup**
- [ ] **Task 1.1**: NgRx-Feature-Store für "Applications" erstellen (`libs/applications/data-access/src/lib/+state/`).
- [ ] **Task 1.2**: Actions definieren (`applications.actions.ts`): `loadApplications`, `loadApplicationsSuccess`, `loadApplicationsFailure`, `selectApplication`.
- [ ] **Task 1.3**: Reducer erstellen (`applications.reducer.ts`) mit Initialzustand und Behandlung der Lade-Actions.
- [ ] **Task 1.4**: Effects implementieren (`applications.effects.ts`) für das Laden der Anwendungsdaten über den `ApplicationService`.
- [ ] **Task 1.5**: Selectors definieren (`applications.selectors.ts`) für den Zugriff auf den State (`getAllApplications`, `getSelectedApplication`, `getApplicationsLoadingState`).

**Komponenten-Refactoring**
- [ ] **Task 1.6**: `ApplicationListComponent` refactoren, um Daten aus dem NgRx-Store über Selectors zu beziehen.
- [ ] **Task 1.7**: `ApplicationListComponent`: Lade- und Fehlerzustände basierend auf dem Store-State implementieren.
- [ ] **Task 1.8**: `ApplicationDetailComponent` anpassen, um die ausgewählte Anwendung über den Store zu beziehen (`selectApplication` Action beim Routenaufruf).

**Testing**
- [ ] **Task 1.9**: Unit-Tests für den Reducer schreiben.
- [ ] **Task 1.10**: Unit-Tests für die Effects mit Mocks für den Service erstellen.
- [ ] **Task 1.11**: Unit-Tests für die Selectors implementieren.
- [ ] **Task 1.12**: Komponenten-Tests für `ApplicationListComponent` und `ApplicationDetailComponent` anpassen, um den `MockStore` zu verwenden.

**Erfolgskriterien**:
- [ ] Das Laden, Anzeigen und Auswählen von Anwendungen wird vollständig über den NgRx-Store gesteuert.
- [ ] Komponenten sind frei von direkten Service-Aufrufen zur Datenbeschaffung.
- [ ] Alle Tests sind erfolgreich und die Code-Coverage ist mindestens auf dem bisherigen Niveau.

---

## Phase 2: Dialog Component Refactoring (Priorität: Mittel)

**Ziel**: Vereinheitlichung der Dialog-Erstellung durch einen zentralen `DialogService`, um Redundanz zu reduzieren und ein konsistentes UX zu gewährleisten.

**Abhängigkeiten**: Phase 1 sollte abgeschlossen sein, um State-Änderungen aus Dialogen (z.B. nach dem Speichern) konsistent zu behandeln.

### Aufgaben

**Infrastruktur**
- [ ] **Task 2.1**: Einen generischen `DialogService` erstellen, der Konfigurationen für Standard-Dialoge (Confirm, Info, Error) entgegennimmt.
- [ ] **Task 2.2**: Einen wiederverwendbaren `ConfirmationDialogComponent` erstellen, der über den `DialogService` gesteuert wird.

**Refactoring**
- [ ] **Task 2.3**: Bestehende `mat-dialog`-Implementierungen in den Anwendungs-Komponenten identifizieren.
- [ ] **Task 2.4**: `deleteApplication`-Dialog durch den neuen `DialogService` ersetzen.
- [ ] **Task 2.5**: `saveApplication`-Bestätigungsdialog (falls vorhanden) durch den neuen Service ersetzen.

**Testing**
- [ ] **Task 2.6**: Unit-Tests für den `DialogService` schreiben.
- [ ] **Task 2.7**: Komponenten-Tests für den `ConfirmationDialogComponent` erstellen.
- [ ] **Task 2.8**: E2E-Tests anpassen, um die neuen Dialogflüsse zu validieren.

**Erfolgskriterien**:
- [ ] Alle Bestätigungs- und Informationsdialoge im Application Management nutzen den zentralen `DialogService`.
- [ ] Der Code ist sauberer und frei von redundanten Dialog-Implementierungen.

---

## Phase 3: Accessibility (A11Y) Enhancement (Priorität: Mittel)

**Ziel**: Verbesserung der Barrierefreiheit gemäß WCAG 2.1 AA-Standards.

**Abhängigkeiten**: Keine kritischen Abhängigkeiten. Kann parallel zu anderen Phasen bearbeitet werden.

### Aufgaben

**Implementierung**
- [ ] **Task 3.1**: ARIA-Attribute in der `ApplicationListComponent` überprüfen und ergänzen (z.B. `aria-label` für Buttons ohne Text).
- [ ] **Task 3.2**: Tastaturnavigation in der Anwendungsliste sicherstellen (Navigation per Pfeiltasten, Auswahl per Enter/Space).
- [ ] **Task 3.3**: Fokus-Management in Dialogen implementieren: Fokus wird beim Öffnen in den Dialog gesetzt und beim Schließen zurückgegeben.
- [ ] **Task 3.4**: `ApplicationDetailComponent`: Semantisches HTML (Überschriften, Landmarks) überprüfen und anpassen.

**Validierung**
- [ ] **Task 3.5**: Manuelle Tests mit einem Screenreader (z.B. NVDA, JAWS) durchführen.
- [ ] **Task 3.6**: Automatisierte A11Y-Tests mit Tools wie `axe` in die E2E-Tests integrieren.

**Erfolgskriterien**:
- [ ] Die Benutzeroberfläche ist vollständig per Tastatur bedienbar.
- [ ] Screenreader geben alle interaktiven Elemente und Inhalte korrekt wieder.
- [ ] `axe`-Scans zeigen keine kritischen oder schwerwiegenden A11Y-Verstöße.

---

## Phase 4: ApplicationDetailComponent Enhancement (Priorität: Niedrig)

**Ziel**: Refactoring der Detailansicht zur Verbesserung der Code-Struktur und Angleichung an das UX des Incident Managements.

**Abhängigkeiten**: Phase 1 muss abgeschlossen sein.

### Aufgaben

**Refactoring**
- [ ] **Task 4.1**: `ApplicationDetailComponent` in kleinere, wiederverwendbare Präsentations-Komponenten aufteilen (z.B. `ApplicationHeaderComponent`, `ApplicationInfoComponent`).
- [ ] **Task 4.2**: Reactive Forms für die Bearbeitung von Anwendungsdaten implementieren, falls noch nicht geschehen.
- [ ] **Task 4.3**: UI-Anpassungen gemäß den Spezifikationen vornehmen (z.B. Layout, Typografie).

**Testing**
- [ ] **Task 4.4**: Unit-Tests für die neuen Präsentations-Komponenten erstellen.
- [ ] **Task 4.5**: Bestehende Tests für die `ApplicationDetailComponent` an die neue Struktur anpassen.

**Dokumentation**
- [ ] **Task 4.6**: Storybook-Stories für die neuen Präsentations-Komponenten erstellen.

**Erfolgskriterien**:
- [ ] Die `ApplicationDetailComponent` fungiert als "smarte" Komponente, die primär die State-Logik enthält.
- [ ] Die UI ist in logische, testbare und wiederverwendbare "dumme" Komponenten unterteilt.
- [ ] Das Erscheinungsbild entspricht den Vorgaben aus den architektonischen Spezifikationen.

---

## Rollback-Überlegungen

- **Git-Strategie**: Alle Änderungen sollten in einem dedizierten Feature-Branch (`feature/app-management-ux-refactor`) erfolgen.
- **Phasenweiser Merge**: Jede abgeschlossene und verifizierte Phase kann einzeln in den Main-Branch gemerged werden, um das Risiko zu minimieren.
- **Feature-Flags**: Für kritische Änderungen (insbesondere die NgRx-Migration) kann die Verwendung von Feature-Flags in Betracht gezogen werden, um ein schnelles Deaktivieren im Fehlerfall zu ermöglichen. Dies erhöht jedoch die Komplexität.

---

## Gesamtabnahmekriterien

- [ ] Alle Phasen sind abgeschlossen und die jeweiligen Erfolgskriterien erfüllt.
- [ ] Die Anwendung ist stabil, performant und frei von Regressionen.
- [ ] Das UX des Application Managements ist spürbar konsistenter mit dem Incident Management.
- [ ] Die Codebasis ist wartbarer, testbarer und besser dokumentiert.
- [ ] Das Projekt wurde vom Product Owner und einem weiteren Entwickler-Teammitglied abgenommen.