import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';

// Angular Material
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';

// Models and Services
import { Incident, IncidentType } from '../../../../core/models/incident.model';
import { IncidentsFacade } from '../../../../store/incidents/incidents.facade';
import { IncidentEditDialogComponent, IncidentEditDialogData } from '../incident-edit-dialog/incident-edit-dialog.component';
import { IncidentCreateDialogComponent, IncidentCreateDialogData } from '../incident-create-dialog/incident-create-dialog.component';
import { DeleteConfirmationDialogComponent, DeleteConfirmationData } from '../../../../shared/components/delete-confirmation-dialog/delete-confirmation-dialog.component';

@Component({
  selector: 'app-incident-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatTableModule,
    MatSortModule,
    MatPaginatorModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatChipsModule,
    MatDialogModule
  ],
  templateUrl: './incident-list.component.html',
  styleUrls: ['./incident-list.component.scss']
})
export class IncidentListComponent implements OnInit, OnDestroy {
  @ViewChild(MatSort) sort!: MatSort;
  @ViewChild(MatPaginator) paginator!: MatPaginator;

  displayedColumns: string[] = ['title', 'type', 'description', 'startTime', 'plannedEndTime', 'isResolved', 'applications', 'actions'];
  dataSource = new MatTableDataSource<Incident>([]);
  
  loading = false;
  error: string | null = null;
  isDeleting = false;
  deleteError: string | null = null;
  
  private destroy$ = new Subject<void>();

  constructor(
    private incidentsFacade: IncidentsFacade,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.setupDataSource();
    this.loadIncidents();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setupDataSource(): void {
    // Subscribe to incidents from the store
    this.incidentsFacade.allIncidents$.pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (incidents) => {
        this.dataSource.data = incidents;
        
        // Setup sorting and pagination after data is loaded
        setTimeout(() => {
          this.dataSource.sort = this.sort;
          this.dataSource.paginator = this.paginator;
        });
      }
    });

    // Subscribe to loading state
    this.incidentsFacade.isLoading$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(isLoading => {
      this.loading = isLoading;
    });

    // Subscribe to error state
    this.incidentsFacade.error$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(error => {
      this.error = error;
      if (error) {
        this.showErrorMessage(error);
      }
    });

    // Subscribe to delete state
    this.incidentsFacade.isDeleting$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(isDeleting => {
      this.isDeleting = isDeleting;
    });

    // Subscribe to delete error state
    this.incidentsFacade.deleteError$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(deleteError => {
      this.deleteError = deleteError;
      if (deleteError) {
        this.showErrorMessage(deleteError);
      }
    });
  }

  private loadIncidents(): void {
    this.incidentsFacade.loadIncidents();
  }

  getIncidentTypeText(type: IncidentType): string {
    switch (type) {
      case IncidentType.STOERUNG:
        return 'Störung';
      case IncidentType.WARTUNGSFENSTER:
        return 'Wartungsfenster';
      case IncidentType.KEINE_STOERUNG:
        return 'Keine Störung';
      default:
        return type;
    }
  }

  getIncidentTypeColor(type: IncidentType): string {
    switch (type) {
      case IncidentType.STOERUNG:
        return 'warn';
      case IncidentType.WARTUNGSFENSTER:
        return 'accent';
      case IncidentType.KEINE_STOERUNG:
        return 'primary';
      default:
        return '';
    }
  }

  getStatusChipColor(isResolved: boolean): string {
    return isResolved ? 'primary' : 'warn';
  }

  getStatusText(isResolved: boolean): string {
    return isResolved ? 'Gelöst' : 'Aktiv';
  }

  formatDate(dateString: string): string {
    if (!dateString) return '-';
    
    return new Date(dateString).toLocaleDateString('de-DE', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getAdditionalApplicationNames(applications: any[], startIndex: number): string {
    if (!applications || applications.length <= startIndex) return '';
    return applications.slice(startIndex).map(app => app.name).join(', ');
  }

  onCreateIncident(): void {
    const dialogData: IncidentCreateDialogData = {
      title: 'Neuen Vorfall erstellen'
    };

    const dialogRef = this.dialog.open(IncidentCreateDialogComponent, {
      data: dialogData,
      width: '800px',
      maxWidth: '90vw',
      maxHeight: '90vh',
      disableClose: true,
      autoFocus: true,
      restoreFocus: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result?.success) {
        this.showSuccessMessage('Vorfall wurde erfolgreich erstellt');
        // No need to reload - NgRx store is automatically updated by createIncidentSuccess action
      }
    });
  }

  onViewIncident(incident: Incident): void {
    this.router.navigate(['/incidents', incident.identifier]);
  }

  onEditIncident(incident: Incident): void {
    const dialogData: IncidentEditDialogData = {
      incident: incident,
      title: `Vorfall bearbeiten: ${incident.title}`
    };

    const dialogRef = this.dialog.open(IncidentEditDialogComponent, {
      data: dialogData,
      width: '800px',
      maxWidth: '90vw',
      maxHeight: '90vh',
      disableClose: true,
      autoFocus: true,
      restoreFocus: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result?.updated) {
        this.showSuccessMessage('Vorfall wurde erfolgreich aktualisiert');
        // Reload incidents to get the latest data
        this.loadIncidents();
      }
    });
  }

  private showSuccessMessage(message: string): void {
    this.snackBar.open(message, 'Schließen', {
      duration: 5000,
      panelClass: ['success-snackbar']
    });
  }

  onDeleteIncident(incident: Incident): void {
    const dialogData: DeleteConfirmationData = {
      title: 'Vorfall löschen',
      message: `Möchten Sie den Vorfall "${incident.title}" wirklich löschen?`,
      entityType: 'incident',
      entityId: incident.identifier,
      entityName: incident.title,
      warningMessage: 'Diese Aktion kann nicht rückgängig gemacht werden.'
    };

    const dialogRef = this.dialog.open(DeleteConfirmationDialogComponent, {
      data: dialogData,
      width: '500px',
      maxWidth: '90vw',
      disableClose: true,
      autoFocus: true,
      restoreFocus: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result?.confirmed) {
        // Clear any previous delete error
        this.incidentsFacade.clearIncidentsError();
        
        // Dispatch delete action
        this.incidentsFacade.deleteIncident(incident.identifier);
        
        // Show success message when delete completes successfully
        this.showDeleteSuccessMessage();
      }
    });
  }

  private showDeleteSuccessMessage(): void {
    // Use a more reliable approach to detect successful deletion
    let wasDeleting = false;
    
    this.incidentsFacade.isDeleting$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(isDeleting => {
      if (isDeleting) {
        wasDeleting = true;
      } else if (wasDeleting && !this.deleteError) {
        this.showSuccessMessage('Vorfall wurde erfolgreich gelöscht');
        wasDeleting = false;
      }
    });
  }

  private showErrorMessage(message: string): void {
    this.snackBar.open(message, 'Schließen', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }
}