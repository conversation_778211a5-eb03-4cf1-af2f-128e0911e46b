import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { delay, map } from 'rxjs/operators';

export interface DependencyInfo {
  canDelete: boolean;
  warnings: string[];
  blockingReasons: string[];
}

export interface DependencyCheckResult {
  entityId: string;
  entityType: string;
  dependencies: DependencyInfo;
  checkTimestamp: Date;
}

@Injectable({
  providedIn: 'root'
})
export class DependencyCheckService {

  constructor() {}

  /**
   * Check dependencies for an application before deletion
   */
  checkApplicationDependencies(applicationId: string): Observable<DependencyCheckResult> {
    // TODO: Replace with actual GraphQL query when backend is available
    return this.getMockApplicationDependencies(applicationId).pipe(
      delay(300), // Simulate API call
      map(mockData => ({
        entityId: applicationId,
        entityType: 'APPLICATION',
        dependencies: mockData,
        checkTimestamp: new Date()
      }))
    );
  }

  /**
   * Check if an application can be safely deleted
   */
  canDeleteApplication(applicationId: string): Observable<boolean> {
    return this.checkApplicationDependencies(applicationId).pipe(
      map(result => result.dependencies.canDelete)
    );
  }

  /**
   * Get dependency warnings for an application
   */
  getApplicationWarnings(applicationId: string): Observable<string[]> {
    return this.checkApplicationDependencies(applicationId).pipe(
      map(result => result.dependencies.warnings)
    );
  }

  /**
   * Get blocking reasons that prevent deletion
   */
  getBlockingReasons(applicationId: string): Observable<string[]> {
    return this.checkApplicationDependencies(applicationId).pipe(
      map(result => result.dependencies.blockingReasons)
    );
  }

  /**
   * Get impact analysis for force deletion
   */
  getForceDeleteImpact(applicationId: string): Observable<{
    dataLossWarning: string;
    confirmationRequired: boolean;
  }> {
    return this.checkApplicationDependencies(applicationId).pipe(
      map(result => ({
        dataLossWarning: result.dependencies.warnings.length > 0 
          ? result.dependencies.warnings.join(' ')
          : 'Keine verknüpften Daten betroffen.',
        confirmationRequired: result.dependencies.warnings.length > 0
      }))
    );
  }

  private getMockApplicationDependencies(applicationId: string): Observable<DependencyInfo> {
    // Mock data based on application ID for demonstration
    const mockScenarios: Record<string, DependencyInfo> = {
      // Application with no dependencies - safe to delete
      'app-safe': {
        canDelete: true,
        warnings: [],
        blockingReasons: []
      },
      
      // Application with some dependencies - can delete with warning
      'app-with-dependencies': {
        canDelete: true,
        warnings: [
          'Diese Applikation hat Abhängigkeiten, die nach der Löschung betroffen sein könnten.'
        ],
        blockingReasons: []
      },
      
      // Application with blocking dependencies - cannot delete
      'app-blocked': {
        canDelete: false,
        warnings: [],
        blockingReasons: [
          'Diese Applikation wird von anderen Systemen verwendet.',
          'Löschung ist nicht möglich, solange aktive Abhängigkeiten bestehen.'
        ]
      }
    };

    // Return specific scenario or generate random scenario
    const mockData = mockScenarios[applicationId] || this.generateRandomScenario();
    return of(mockData);
  }

  private generateRandomScenario(): DependencyInfo {
    const canDelete = Math.random() > 0.3; // 70% chance can delete
    const hasWarnings = Math.random() > 0.5; // 50% chance has warnings
    
    return {
      canDelete,
      warnings: hasWarnings && canDelete ? [
        'Diese Applikation hat einige Abhängigkeiten.',
        'Überprüfen Sie die Auswirkungen vor der Löschung.'
      ] : [],
      blockingReasons: !canDelete ? [
        'Aktive Abhängigkeiten verhindern die Löschung.',
        'Entfernen Sie zuerst alle Verknüpfungen.'
      ] : []
    };
  }

  private getMockIncidentDependencies(incidentId: string): Observable<DependencyInfo> {
    // Most applications can be deleted safely
    const mockData: DependencyInfo = {
      canDelete: true,
      warnings: [],
      blockingReasons: []
    };
    
    return of(mockData);
  }

  /**
   * Format dependency information for display
   */
  formatDependencyInfo(dependencies: DependencyInfo): {
    canDelete: boolean;
    message: string;
    severity: 'info' | 'warning' | 'error';
  } {
    if (!dependencies.canDelete) {
      return {
        canDelete: false,
        message: dependencies.blockingReasons.join(' '),
        severity: 'error'
      };
    }

    if (dependencies.warnings.length > 0) {
      return {
        canDelete: true,
        message: dependencies.warnings.join(' '),
        severity: 'warning'
      };
    }

    return {
      canDelete: true,
      message: 'Diese Applikation kann sicher gelöscht werden.',
      severity: 'info'
    };
  }
}