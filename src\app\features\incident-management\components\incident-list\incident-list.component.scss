.incident-list-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    flex-wrap: wrap;
    gap: 16px;

    h2 {
      margin: 0;
      color: #1976d2;
      font-weight: 500;
    }

    button {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .error-container {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px;
    background-color: #ffebee;
    border: 1px solid #f44336;
    border-radius: 4px;
    margin-bottom: 24px;
    color: #c62828;

    mat-icon {
      font-size: 20px;
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px;
    gap: 16px;

    p {
      margin: 0;
      color: #666;
    }
  }

  .table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .incident-table {
      width: 100%;

      .mat-mdc-header-cell {
        font-weight: 600;
        color: #1976d2;
        background-color: #f5f5f5;
      }

      .mat-mdc-cell {
        padding: 12px 16px;
        border-bottom: 1px solid #e0e0e0;

        &:first-child {
          padding-left: 24px;
        }

        &:last-child {
          padding-right: 24px;
        }
      }

      .description-text {
        display: block;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .incident-link {
        color: #1976d2;
        text-decoration: none;
        font-weight: 500;
        cursor: pointer;
        transition: color 0.2s ease;

        &:hover {
          color: #1565c0;
          text-decoration: underline;
        }

        &:visited {
          color: #1976d2;
        }
      }

      .applications-container {
        max-width: 250px;
        overflow: hidden;
      }

      .action-buttons {
        display: flex;
        gap: 4px;
        align-items: center;

        button {
          &:disabled {
            opacity: 0.5;
          }
        }
      }

      .resolved-row {
        background-color: #f8f8f8;

        .mat-mdc-cell {
          color: #666;
        }
      }
    }

    .no-data-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 48px 24px;
      text-align: center;
      color: #666;

      mat-icon {
        font-size: 48px;
        width: 48px;
        height: 48px;
        margin-bottom: 16px;
        color: #ccc;
      }

      h3 {
        margin: 0 0 8px 0;
        font-weight: 500;
      }

      p {
        margin: 0 0 24px 0;
        max-width: 400px;
      }

      button {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .incident-list-container {
    padding: 16px;

    .list-header {
      flex-direction: column;
      align-items: stretch;

      h2 {
        text-align: center;
        margin-bottom: 16px;
      }
    }

    .table-container {
      .incident-table {
        .description-text {
          max-width: 120px;
        }

        .applications-container {
          max-width: 150px;
        }

        .action-buttons {
          flex-direction: column;
          gap: 2px;
        }
      }
    }
  }
}

@media (max-width: 600px) {
  .incident-list-container {
    .table-container {
      .incident-table {
        font-size: 14px;

        .mat-mdc-cell {
          padding: 8px 12px;

          &:first-child {
            padding-left: 16px;
          }

          &:last-child {
            padding-right: 16px;
          }
        }

        .description-text {
          max-width: 80px;
        }

        .applications-container {
          max-width: 100px;
        }
      }
    }
  }
}

// Global Snackbar Styles
::ng-deep {
  .success-snackbar {
    background-color: #4caf50 !important;
    color: white !important;
  }

  .error-snackbar {
    background-color: #f44336 !important;
    color: white !important;
  }
}