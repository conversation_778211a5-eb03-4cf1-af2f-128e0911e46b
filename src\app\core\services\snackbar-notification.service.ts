import { Injectable } from '@angular/core';
import { MatSnackBar, MatSnackBarConfig, MatSnackBarRef, SimpleSnackBar } from '@angular/material/snack-bar';

export interface SnackBarAction {
  label: string;
  action: () => void;
}

@Injectable({
  providedIn: 'root'
})
export class SnackbarNotificationService {
  private defaultConfig: MatSnackBarConfig = {
    duration: 4000,
    horizontalPosition: 'end',
    verticalPosition: 'bottom'
  };

  constructor(private snackBar: MatSnackBar) {}

  /**
   * Show a success message
   */
  showSuccess(message: string, action?: SnackBarAction): MatSnackBarRef<SimpleSnackBar> {
    const config: MatSnackBarConfig = {
      ...this.defaultConfig,
      panelClass: ['success-snackbar'],
      duration: 3000
    };

    const snackBarRef = this.snackBar.open(message, action?.label || 'OK', config);

    if (action) {
      snackBarRef.onAction().subscribe(() => {
        action.action();
      });
    }

    return snackBarRef;
  }

  /**
   * Show an error message
   */
  showError(message: string, action?: SnackBarAction): MatSnackBarRef<SimpleSnackBar> {
    const config: MatSnackBarConfig = {
      ...this.defaultConfig,
      panelClass: ['error-snackbar'],
      duration: 6000 // Longer duration for errors
    };

    const snackBarRef = this.snackBar.open(message, action?.label || 'OK', config);

    if (action) {
      snackBarRef.onAction().subscribe(() => {
        action.action();
      });
    }

    return snackBarRef;
  }

  /**
   * Show a warning message
   */
  showWarning(message: string, action?: SnackBarAction): MatSnackBarRef<SimpleSnackBar> {
    const config: MatSnackBarConfig = {
      ...this.defaultConfig,
      panelClass: ['warning-snackbar'],
      duration: 5000
    };

    const snackBarRef = this.snackBar.open(message, action?.label || 'OK', config);

    if (action) {
      snackBarRef.onAction().subscribe(() => {
        action.action();
      });
    }

    return snackBarRef;
  }

  /**
   * Show an info message
   */
  showInfo(message: string, action?: SnackBarAction): MatSnackBarRef<SimpleSnackBar> {
    const config: MatSnackBarConfig = {
      ...this.defaultConfig,
      panelClass: ['info-snackbar'],
      duration: 4000
    };

    const snackBarRef = this.snackBar.open(message, action?.label || 'OK', config);

    if (action) {
      snackBarRef.onAction().subscribe(() => {
        action.action();
      });
    }

    return snackBarRef;
  }

  /**
   * Show a custom message with custom configuration
   */
  showCustom(
    message: string, 
    actionLabel?: string, 
    config?: Partial<MatSnackBarConfig>
  ): MatSnackBarRef<SimpleSnackBar> {
    const finalConfig: MatSnackBarConfig = {
      ...this.defaultConfig,
      ...config
    };

    return this.snackBar.open(message, actionLabel || 'OK', finalConfig);
  }

  /**
   * Show an undo action snackbar
   */
  showUndo(
    message: string, 
    undoAction: () => void, 
    duration: number = 5000
  ): MatSnackBarRef<SimpleSnackBar> {
    const config: MatSnackBarConfig = {
      ...this.defaultConfig,
      duration,
      panelClass: ['undo-snackbar']
    };

    const snackBarRef = this.snackBar.open(message, 'RÜCKGÄNGIG', config);

    snackBarRef.onAction().subscribe(() => {
      undoAction();
    });

    return snackBarRef;
  }

  /**
   * Dismiss all active snackbars
   */
  dismiss(): void {
    this.snackBar.dismiss();
  }

  // Convenience methods for common application management scenarios

  /**
   * Show application created success message
   */
  showApplicationCreated(applicationName: string): MatSnackBarRef<SimpleSnackBar> {
    return this.showSuccess(`Applikation "${applicationName}" wurde erfolgreich erstellt.`);
  }

  /**
   * Show application updated success message
   */
  showApplicationUpdated(applicationName: string): MatSnackBarRef<SimpleSnackBar> {
    return this.showSuccess(`Applikation "${applicationName}" wurde erfolgreich aktualisiert.`);
  }

  /**
   * Show application deleted success message with undo option
   */
  showApplicationDeleted(
    applicationName: string, 
    undoAction: () => void
  ): MatSnackBarRef<SimpleSnackBar> {
    return this.showUndo(
      `Applikation "${applicationName}" wurde gelöscht.`,
      undoAction,
      7000
    );
  }

  /**
   * Show application creation error
   */
  showApplicationCreateError(error?: string): MatSnackBarRef<SimpleSnackBar> {
    const message = error 
      ? `Fehler beim Erstellen der Applikation: ${error}`
      : 'Fehler beim Erstellen der Applikation. Bitte versuchen Sie es erneut.';
    
    return this.showError(message);
  }

  /**
   * Show application update error
   */
  showApplicationUpdateError(error?: string): MatSnackBarRef<SimpleSnackBar> {
    const message = error 
      ? `Fehler beim Aktualisieren der Applikation: ${error}`
      : 'Fehler beim Aktualisieren der Applikation. Bitte versuchen Sie es erneut.';
    
    return this.showError(message);
  }

  /**
   * Show application delete error
   */
  showApplicationDeleteError(error?: string): MatSnackBarRef<SimpleSnackBar> {
    const message = error 
      ? `Fehler beim Löschen der Applikation: ${error}`
      : 'Fehler beim Löschen der Applikation. Bitte versuchen Sie es erneut.';
    
    return this.showError(message);
  }

  /**
   * Show validation error
   */
  showValidationError(fieldName: string, error: string): MatSnackBarRef<SimpleSnackBar> {
    return this.showError(`${fieldName}: ${error}`);
  }

  /**
   * Show network error
   */
  showNetworkError(): MatSnackBarRef<SimpleSnackBar> {
    return this.showError(
      'Netzwerkfehler. Bitte überprüfen Sie Ihre Internetverbindung.',
      {
        label: 'WIEDERHOLEN',
        action: () => window.location.reload()
      }
    );
  }

  /**
   * Show loading message (no auto-dismiss)
   */
  showLoading(message: string = 'Wird geladen...'): MatSnackBarRef<SimpleSnackBar> {
    return this.showCustom(message, '', {
      duration: 0, // No auto-dismiss
      panelClass: ['loading-snackbar']
    });
  }
}