import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { map, debounceTime, distinctUntilChanged, switchMap, delay } from 'rxjs/operators';

export interface SearchResult {
  id: string;
  title: string;
  description: string;
  type: SearchResultType;
  url: string;
  metadata?: Record<string, any>;
  relevanceScore: number;
}

export enum SearchResultType {
  SYSTEM = 'system',
  USER = 'user',
  MAINTENANCE = 'maintenance',
  DOCUMENTATION = 'documentation',
  SETTING = 'setting'
}

export interface SearchFilter {
  types?: SearchResultType[];
  dateFrom?: Date;
  dateTo?: Date;
  tags?: string[];
}

export interface SearchSuggestion {
  text: string;
  type: 'recent' | 'popular' | 'suggestion';
  count?: number;
}

@Injectable({
  providedIn: 'root'
})
export class SearchService {
  private searchHistorySubject = new BehaviorSubject<string[]>([]);
  private recentSearchesKey = 'recent_searches';
  private maxRecentSearches = 10;

  public searchHistory$ = this.searchHistorySubject.asObservable();

  constructor() {
    this.loadSearchHistory();
  }

  search(query: string, filter?: SearchFilter): Observable<SearchResult[]> {
    if (!query.trim()) {
      return of([]);
    }

    // Add to search history
    this.addToSearchHistory(query);

    // Mock search implementation - replace with actual API call
    return this.mockSearch(query, filter).pipe(
      delay(300) // Simulate API delay
    );
  }

  getSuggestions(query: string): Observable<SearchSuggestion[]> {
    if (!query.trim()) {
      return this.getRecentSearches();
    }

    // Mock suggestions implementation - replace with actual API call
    return this.mockGetSuggestions(query).pipe(
      delay(200) // Simulate API delay
    );
  }

  getRecentSearches(): Observable<SearchSuggestion[]> {
    return this.searchHistory$.pipe(
      map(history => 
        history.map(search => ({
          text: search,
          type: 'recent' as const
        }))
      )
    );
  }

  clearSearchHistory(): void {
    localStorage.removeItem(this.recentSearchesKey);
    this.searchHistorySubject.next([]);
  }

  removeFromHistory(query: string): void {
    const history = this.searchHistorySubject.value;
    const updatedHistory = history.filter(item => item !== query);
    this.searchHistorySubject.next(updatedHistory);
    this.saveSearchHistory(updatedHistory);
  }

  private addToSearchHistory(query: string): void {
    const history = this.searchHistorySubject.value;
    const updatedHistory = [query, ...history.filter(item => item !== query)]
      .slice(0, this.maxRecentSearches);
    
    this.searchHistorySubject.next(updatedHistory);
    this.saveSearchHistory(updatedHistory);
  }

  private loadSearchHistory(): void {
    const stored = localStorage.getItem(this.recentSearchesKey);
    if (stored) {
      try {
        const history = JSON.parse(stored);
        this.searchHistorySubject.next(history);
      } catch {
        this.searchHistorySubject.next([]);
      }
    }
  }

  private saveSearchHistory(history: string[]): void {
    localStorage.setItem(this.recentSearchesKey, JSON.stringify(history));
  }

  // Mock implementations - replace with actual API calls
  private mockSearch(query: string, filter?: SearchFilter): Observable<SearchResult[]> {
    const mockResults: SearchResult[] = [
      {
        id: '1',
        title: 'Production Database Server',
        description: 'Main production database server handling customer data',
        type: SearchResultType.SYSTEM,
        url: '/systems/prod-db-01',
        relevanceScore: 0.95,
        metadata: { status: 'online', location: 'datacenter-1' }
      },
      {
        id: '3',
        title: 'John Doe',
        description: 'System Administrator - IT Department',
        type: SearchResultType.USER,
        url: '/users/john-doe',
        relevanceScore: 0.75,
        metadata: { role: 'admin', department: 'IT' }
      },
      {
        id: '4',
        title: 'Scheduled Maintenance Window',
        description: 'Weekly maintenance for all production servers',
        type: SearchResultType.MAINTENANCE,
        url: '/maintenance/weekly-001',
        relevanceScore: 0.70,
        metadata: { scheduled: '2024-01-15T02:00:00Z', duration: '2 hours' }
      },
      {
        id: '5',
        title: 'System Configuration Guide',
        description: 'Complete guide for configuring monitoring systems',
        type: SearchResultType.DOCUMENTATION,
        url: '/docs/system-config',
        relevanceScore: 0.65,
        metadata: { category: 'configuration', lastUpdated: '2024-01-10' }
      }
    ];

    // Filter results based on query
    const filteredResults = mockResults.filter(result => 
      result.title.toLowerCase().includes(query.toLowerCase()) ||
      result.description.toLowerCase().includes(query.toLowerCase())
    );

    // Apply additional filters
    if (filter?.types && filter.types.length > 0) {
      return of(filteredResults.filter(result => filter.types!.includes(result.type)));
    }

    return of(filteredResults.sort((a, b) => b.relevanceScore - a.relevanceScore));
  }

  private mockGetSuggestions(query: string): Observable<SearchSuggestion[]> {
    const mockSuggestions: SearchSuggestion[] = [
      { text: `${query} server`, type: 'suggestion' },
      { text: `${query} maintenance`, type: 'suggestion' },
      { text: 'database connection', type: 'popular', count: 15 },
      { text: 'server monitoring', type: 'popular', count: 12 },
      { text: 'system alerts', type: 'popular', count: 8 }
    ];

    return of(mockSuggestions.filter(suggestion => 
      suggestion.text.toLowerCase().includes(query.toLowerCase())
    ).slice(0, 6));
  }
}