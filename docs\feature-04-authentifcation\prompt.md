# Init-Prompt für Auth

```
<PERSON><PERSON><PERSON> aus @/docs/feature-04-authentifcation/requirements.md  einen Implementierungsplan "plan.md" mit Checklisten zur Umsetzung ins selbe Verzeichnis. Stoppe dann zunächst!

Nutze die Backend-Spezifikation von @/docs/backend-doc-link/AuthenticationAPI.md 

BEACHTE
- Ignoriere Timeline & Responsibility
- Ignoriere Tests

Beachte die Erkenntnisse aus@/rules-bank/graphql-learnings.md
```

# Run

```
<PERSON><PERSON> den @/docs/feature-04-authentifcation/plan-architect.md schrittweise um! Gehe Phasenweise um und stoppe nach jeder Phase!
```