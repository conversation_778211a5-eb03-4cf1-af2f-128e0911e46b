import { Injectable, inject } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { of } from 'rxjs';
import { map, exhaustMap, catchError, tap } from 'rxjs/operators';
import { Router } from '@angular/router';
import { AuthService } from '../../core/services/auth.service';
import * as AuthActions from './auth.actions';

@Injectable()
export class AuthEffects {
  private actions$ = inject(Actions);
  private authService = inject(AuthService);
  private router = inject(Router);

  login$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AuthActions.login),
      exhaustMap(action =>
        this.authService.login(action.credentials).pipe(
          map(response => AuthActions.loginSuccess({ response })),
          catchError(error => of(AuthActions.loginFailure({ 
            error: error.message || 'Login failed' 
          })))
        )
      )
    )
  );

  loginSuccess$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AuthActions.loginSuccess),
      tap(() => {
        this.router.navigate(['/dashboard']);
      })
    ),
    { dispatch: false }
  );

  logout$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AuthActions.logout),
      tap(() => {
        this.authService.logout();
      }),
      map(() => AuthActions.logoutSuccess())
    )
  );

  logoutSuccess$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AuthActions.logoutSuccess),
      tap(() => {
        this.router.navigate(['/login']);
      })
    ),
    { dispatch: false }
  );

  refreshToken$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AuthActions.refreshToken),
      exhaustMap(() =>
        this.authService.refreshToken().pipe(
          map(token => AuthActions.refreshTokenSuccess({ token })),
          catchError(error => of(AuthActions.refreshTokenFailure({ 
            error: error.message || 'Token refresh failed' 
          })))
        )
      )
    )
  );

  refreshTokenFailure$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AuthActions.refreshTokenFailure),
      tap(() => {
        // Redirect to login on refresh token failure
        this.router.navigate(['/login']);
      })
    ),
    { dispatch: false }
  );

  // New hydrateAuth effect as per plan
  hydrateAuth$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AuthActions.hydrateAuth),
      map(() => {
        const token = this.authService.getToken();
        const user = this.authService.getCurrentUser();
        
        if (token && user && this.authService.isAuthenticated()) {
          const refreshToken = localStorage.getItem('refresh_token') || '';
          return AuthActions.hydrateAuthSuccess({ user, token, refreshToken });
        } else {
          return AuthActions.hydrateAuthFailure();
        }
      })
    )
  );

  // Keep old effect for backward compatibility
  initializeAuth$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AuthActions.initializeAuth),
      map(() => {
        const token = this.authService.getToken();
        const user = this.authService.getCurrentUser();
        
        if (token && user && this.authService.isAuthenticated()) {
          const refreshToken = localStorage.getItem('refresh_token') || '';
          return AuthActions.initializeAuthSuccess({ user, token, refreshToken });
        } else {
          return AuthActions.initializeAuthFailure();
        }
      })
    )
  );

  updateUserProfile$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AuthActions.updateUserProfile),
      exhaustMap(action => {
        // Mock implementation - replace with actual API call
        const currentUser = this.authService.getCurrentUser();
        if (currentUser) {
          const updatedUser = { ...currentUser, ...action.user };
          // Simulate API delay
          return of(updatedUser).pipe(
            map(user => AuthActions.updateUserProfileSuccess({ user })),
            catchError(error => of(AuthActions.updateUserProfileFailure({ 
              error: error.message || 'Profile update failed' 
            })))
          );
        } else {
          return of(AuthActions.updateUserProfileFailure({ 
            error: 'No user found' 
          }));
        }
      })
    )
  );
}