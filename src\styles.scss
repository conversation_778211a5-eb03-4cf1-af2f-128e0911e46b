/* You can add global styles to this file, and also import other style files */

// Angular Material Theme Configuration
@use '@angular/material' as mat;

// Import modern button styles
@use 'app/shared/styles/modern-buttons.scss';

// Include the common styles for Angular Material
@include mat.core();

// Define Funk Corporate Design palette
$funk-blue-palette: (
  50: #e3f2fd,
  100: #bbdefb,
  200: #90caf9,
  300: #64b5f6,
  400: #42a5f5,
  500: #002D74,
  600: #1e88e5,
  700: #1976d2,
  800: #1565c0,
  900: #0d47a1,
  A100: #82b1ff,
  A200: #448aff,
  A400: #2979ff,
  A700: #2962ff,
  contrast: (
    50: rgba(black, 0.87),
    100: rgba(black, 0.87),
    200: rgba(black, 0.87),
    300: rgba(black, 0.87),
    400: rgba(black, 0.87),
    500: white,
    600: white,
    700: white,
    800: white,
    900: white,
    A100: rgba(black, 0.87),
    A200: white,
    A400: white,
    A700: white,
  )
);

$funk-light-blue-palette: (
  50: #f3f8fc,
  100: #e1ecf4,
  200: #c3d9e9,
  300: #8EAEC8,
  400: #7ba3c0,
  500: #6898b8,
  600: #5a8aad,
  700: #4c7ca2,
  800: #3e6e97,
  900: #30608c,
  A100: #8EAEC8,
  A200: #7ba3c0,
  A400: #6898b8,
  A700: #5a8aad,
  contrast: (
    50: rgba(black, 0.87),
    100: rgba(black, 0.87),
    200: rgba(black, 0.87),
    300: rgba(black, 0.87),
    400: rgba(black, 0.87),
    500: white,
    600: white,
    700: white,
    800: white,
    900: white,
    A100: rgba(black, 0.87),
    A200: rgba(black, 0.87),
    A400: white,
    A700: white,
  )
);

// Define Funk Corporate Design theme
$primary: mat.m2-define-palette($funk-blue-palette, 500);
$accent: mat.m2-define-palette($funk-light-blue-palette, 300);
$warn: mat.m2-define-palette(mat.$m2-red-palette);

// Custom typography configuration for better readability
$typography: mat.m2-define-typography-config(
  $font-family: '"Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
  $headline-1: mat.m2-define-typography-level(2rem, 2.5rem, 600),
  $headline-2: mat.m2-define-typography-level(1.75rem, 2.25rem, 600),
  $headline-3: mat.m2-define-typography-level(1.5rem, 2rem, 600),
  $headline-4: mat.m2-define-typography-level(1.25rem, 1.75rem, 500),
  $headline-5: mat.m2-define-typography-level(1.125rem, 1.5rem, 500),
  $headline-6: mat.m2-define-typography-level(1rem, 1.5rem, 500),
  $body-1: mat.m2-define-typography-level(1rem, 1.5rem, 400),
  $body-2: mat.m2-define-typography-level(0.875rem, 1.25rem, 400),
  $caption: mat.m2-define-typography-level(0.75rem, 1rem, 400),
  $button: mat.m2-define-typography-level(0.875rem, 1rem, 500)
);

$theme: mat.m2-define-light-theme((
  color: (
    primary: $primary,
    accent: $accent,
    warn: $warn,
  ),
  typography: $typography,
  density: 0,
));

// Include theme styles for core and each component used in your app
@include mat.all-component-themes($theme);

// Global styles
html, body { 
  height: 100%; 
  margin: 0; 
  font-family: Roboto, "Helvetica Neue", sans-serif; 
}

// Funk Corporate Design Color System - Flat Design
:root {
  // Funk Primary Colors
  --funk-blue: #002D74;
  --funk-blue-rgb: 0, 45, 116;
  
  // Funk Secondary Colors
  --funk-gray: #58585A;
  --funk-light-gray: #D9DADB;
  --funk-gray-rgb: 88, 88, 90;
  --funk-light-gray-rgb: 217, 218, 219;
  
  // Funk Accent Colors
  --funk-light-blue: #8EAEC8;
  --funk-white: #FFFFFF;
  --funk-light-blue-rgb: 142, 174, 200;
  
  // Status Colors
  --success-color: #28a745;
  --warning-color: #ffc107;
  --error-color: #dc3545;
  --info-color: #8EAEC8;
  
  // Mapped Colors for Application
  --primary-color: var(--funk-blue);
  --accent-color: var(--funk-light-blue);
  --warn-color: var(--error-color);
  
  // Background Colors - Flat Design
  --background-color: var(--funk-white);
  --surface-color: var(--funk-white);
  --surface-elevated: var(--funk-white);
  
  // Text Colors
  --text-primary: var(--funk-blue);
  --text-secondary: var(--funk-gray);
  --text-muted: var(--funk-light-gray);
  --text-on-primary: var(--funk-white);
  
  // Border and Divider Colors - Flat Design
  --divider-color: var(--funk-light-gray);
  --border-color: var(--funk-light-gray);
  --border-subtle: var(--funk-light-gray);
  
  // Interactive States - Flat Design
  --hover-color: var(--funk-light-blue);
  --active-color: var(--funk-blue);
  --focus-color: rgba(var(--funk-blue-rgb), 0.25);
  
  // Minimal Shadows for Flat Design
  --shadow-sm: 0 1px 2px rgba(var(--funk-gray-rgb), 0.1);
  --shadow-md: 0 2px 4px rgba(var(--funk-gray-rgb), 0.1);
  --shadow-lg: 0 4px 8px rgba(var(--funk-gray-rgb), 0.1);
  
  // Minimal Border Radius for Flat Design
  --border-radius-sm: 2px;
  --border-radius-md: 4px;
  --border-radius-lg: 6px;
}

// Layout utilities
.full-width {
  width: 100%;
}

.full-height {
  height: 100%;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.spacer {
  flex: 1 1 auto;
}

// Responsive breakpoints
.mobile-only {
  @media (min-width: 768px) {
    display: none !important;
  }
}

.desktop-only {
  @media (max-width: 767px) {
    display: none !important;
  }
}

// Flat Material Design overrides - Funk Corporate Design
.mat-mdc-card {
  border: 1px solid var(--border-color) !important;
  box-shadow: none !important;
  border-radius: var(--border-radius-md) !important;
  background-color: var(--surface-color) !important;
  transition: border-color 0.2s ease !important;
}

.mat-mdc-card:hover {
  border-color: var(--funk-light-blue) !important;
  box-shadow: none !important;
  transform: none !important;
}

.mat-mdc-button {
  border-radius: var(--border-radius-sm) !important;
  font-weight: 500 !important;
  text-transform: none !important;
  letter-spacing: 0 !important;
  transition: background-color 0.2s ease !important;
  box-shadow: none !important;
}

.mat-mdc-outlined-button {
  border-color: var(--border-color) !important;
  color: var(--text-primary) !important;
  border-width: 1px !important;
}

.mat-mdc-outlined-button:hover {
  background-color: var(--hover-color) !important;
  border-color: var(--primary-color) !important;
  transform: none !important;
}

.mat-mdc-raised-button,
.mat-mdc-flat-button {
  box-shadow: none !important;
  border-radius: var(--border-radius-sm) !important;
  background-color: var(--primary-color) !important;
  color: var(--text-on-primary) !important;
}

.mat-mdc-raised-button:hover,
.mat-mdc-flat-button:hover {
  background-color: var(--funk-gray) !important;
  box-shadow: none !important;
  transform: none !important;
}

.mat-mdc-stroked-button {
  border-radius: var(--border-radius-sm) !important;
  border-width: 1px !important;
  border-color: var(--primary-color) !important;
  color: var(--primary-color) !important;
  transition: all 0.2s ease !important;
}

.mat-mdc-stroked-button:hover {
  background-color: var(--primary-color) !important;
  color: var(--text-on-primary) !important;
  transform: none !important;
}

// Flat form field styling - Funk Corporate Design
.mat-mdc-form-field {
  .mat-mdc-text-field-wrapper {
    border-radius: var(--border-radius-sm) !important;
    transition: border-color 0.2s ease !important;
    border: 1px solid var(--border-color) !important;
  }

  .mat-mdc-form-field-focus-overlay {
    border-radius: var(--border-radius-sm) !important;
  }

  &.mat-focused .mat-mdc-text-field-wrapper {
    border-color: var(--funk-blue) !important;
    box-shadow: none !important;
  }

  .mat-mdc-form-field-error-wrapper {
    padding-top: 8px !important;
  }

  .mat-mdc-form-field-hint-wrapper {
    padding-top: 6px !important;
  }

  .mat-mdc-form-field-subscript-wrapper {
    font-size: 0.75rem !important;
  }

  .mat-mdc-form-field-label {
    color: var(--funk-gray) !important;
  }

  &.mat-focused .mat-mdc-form-field-label {
    color: var(--funk-blue) !important;
  }
}

// Flat dialog styling - Funk Corporate Design
.mat-mdc-dialog-container {
  border-radius: var(--border-radius-md) !important;
  box-shadow: var(--shadow-md) !important;
  overflow: hidden !important;
  border: 1px solid var(--border-color) !important;
}

.mat-mdc-dialog-content {
  padding: 0 !important;
  margin: 0 !important;
  max-height: none !important;
}

.mat-mdc-dialog-actions {
  padding: 0 !important;
  margin: 0 !important;
  min-height: auto !important;
}

// Flat expansion panel styling - Funk Corporate Design
.mat-expansion-panel {
  border-radius: var(--border-radius-sm) !important;
  box-shadow: none !important;
  border: 1px solid var(--border-color) !important;
  margin-bottom: 16px !important;

  .mat-expansion-panel-header {
    border-radius: var(--border-radius-sm) var(--border-radius-sm) 0 0 !important;
    transition: background-color 0.2s ease !important;
    background-color: var(--funk-white) !important;
  }

  .mat-expansion-panel-header:hover {
    background-color: var(--funk-light-blue) !important;
    color: var(--funk-white) !important;
  }

  &.mat-expanded .mat-expansion-panel-header {
    border-radius: var(--border-radius-sm) var(--border-radius-sm) 0 0 !important;
    background-color: var(--funk-blue) !important;
    color: var(--funk-white) !important;
  }

  .mat-expansion-panel-body {
    padding: 0 !important;
  }
}

// Flat checkbox styling - Funk Corporate Design
.mat-mdc-checkbox {
  .mdc-checkbox {
    border-radius: var(--border-radius-sm) !important;
  }

  .mat-mdc-checkbox-ripple {
    border-radius: 50% !important;
  }

  &.mat-mdc-checkbox-checked .mdc-checkbox {
    background-color: var(--funk-blue) !important;
    border-color: var(--funk-blue) !important;
  }
}

// Improved focus states for accessibility - Funk Corporate Design
.mat-mdc-button:focus,
.mat-mdc-outlined-button:focus,
.mat-mdc-raised-button:focus,
.mat-mdc-flat-button:focus,
.mat-mdc-stroked-button:focus {
  outline: 2px solid var(--funk-blue) !important;
  outline-offset: 2px !important;
}

.mat-mdc-form-field:focus-within {
  .mat-mdc-text-field-wrapper {
    outline: 2px solid var(--funk-blue) !important;
    outline-offset: 1px !important;
  }
}

// Custom scrollbar styling - Funk Corporate Design
* {
  scrollbar-width: thin;
  scrollbar-color: var(--funk-light-blue) transparent;
}

*::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

*::-webkit-scrollbar-track {
  background: transparent;
}

*::-webkit-scrollbar-thumb {
  background-color: var(--funk-light-blue);
  border-radius: 3px;
}

*::-webkit-scrollbar-thumb:hover {
  background-color: var(--funk-blue);
}

// Text selection styling - Funk Corporate Design
::selection {
  background-color: var(--funk-light-blue);
  color: var(--funk-white);
}

::-moz-selection {
  background-color: var(--funk-light-blue);
  color: var(--funk-white);
}

// Improved typography
h1, h2, h3, h4, h5, h6 {
  color: var(--text-primary);
  font-weight: 600;
  line-height: 1.2;
  margin: 0;
}

p {
  color: var(--text-primary);
  line-height: 1.5;
  margin: 0;
}

small {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

// Utility classes for the neutral design
.text-primary {
  color: var(--text-primary) !important;
}

.text-secondary {
  color: var(--text-secondary) !important;
}

.text-muted {
  color: var(--text-muted) !important;
}

.bg-surface {
  background-color: var(--surface-color) !important;
}

.bg-background {
  background-color: var(--background-color) !important;
}

.border-subtle {
  border: 1px solid var(--border-subtle) !important;
}

.border-default {
  border: 1px solid var(--border-color) !important;
}

.shadow-sm {
  box-shadow: var(--shadow-sm) !important;
}

.shadow-md {
  box-shadow: var(--shadow-md) !important;
}

.rounded-sm {
  border-radius: var(--border-radius-sm) !important;
}

.rounded-md {
  border-radius: var(--border-radius-md) !important;
}

.rounded-lg {
  border-radius: var(--border-radius-lg) !important;
}
