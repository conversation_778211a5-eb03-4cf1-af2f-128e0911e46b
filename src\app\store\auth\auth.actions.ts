import { createAction, props } from '@ngrx/store';
import { User, LoginCredentials, LoginResponse } from '../../core/models/user.model';

// Login Actions
export const login = createAction(
  '[Auth] Login',
  props<{ credentials: LoginCredentials }>()
);

export const loginSuccess = createAction(
  '[Auth] Login Success',
  props<{ response: LoginResponse }>()
);

export const loginFailure = createAction(
  '[Auth] Login Failure',
  props<{ error: string }>()
);

// Logout Actions
export const logout = createAction('[Auth] Logout');

export const logoutSuccess = createAction('[Auth] Logout Success');

// Token Refresh Actions
export const refreshToken = createAction('[Auth] Refresh Token');

export const refreshTokenSuccess = createAction(
  '[Auth] Refresh Token Success',
  props<{ token: string }>()
);

export const refreshTokenFailure = createAction(
  '[Auth] Refresh Token Failure',
  props<{ error: string }>()
);

// Initialize Auth (renamed to hydrateAuth as per plan)
export const hydrateAuth = createAction('[Auth] Hydrate Auth');

export const hydrateAuthSuccess = createAction(
  '[Auth] Hydrate Auth Success',
  props<{ user: User; token: string; refreshToken: string }>()
);

export const hydrateAuthFailure = createAction('[Auth] Hydrate Auth Failure');

// Keep old actions for backward compatibility during transition
export const initializeAuth = createAction('[Auth] Initialize Auth');

export const initializeAuthSuccess = createAction(
  '[Auth] Initialize Auth Success',
  props<{ user: User; token: string; refreshToken: string }>()
);

export const initializeAuthFailure = createAction('[Auth] Initialize Auth Failure');

// Clear Auth Error
export const clearAuthError = createAction('[Auth] Clear Error');

// Update User Profile
export const updateUserProfile = createAction(
  '[Auth] Update User Profile',
  props<{ user: Partial<User> }>()
);

export const updateUserProfileSuccess = createAction(
  '[Auth] Update User Profile Success',
  props<{ user: User }>()
);

export const updateUserProfileFailure = createAction(
  '[Auth] Update User Profile Failure',
  props<{ error: string }>()
);