import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { of } from 'rxjs';

import { ApplicationSelectorComponent } from './application-selector.component';
import { ApplicationService } from '../../../../core/services/application.service';
import { Application } from '../../../../core/models/application.model';

describe('ApplicationSelectorComponent', () => {
  let component: ApplicationSelectorComponent;
  let fixture: ComponentFixture<ApplicationSelectorComponent>;
  let mockApplicationService: jasmine.SpyObj<ApplicationService>;

  const mockApplications: Application[] = [
    {
      identifier: '1',
      name: 'Test App 1',
      description: 'Test Description 1',
      isDeleted: false,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      identifier: '2',
      name: 'Test App 2',
      description: 'Test Description 2',
      isDeleted: false,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    }
  ];

  beforeEach(async () => {
    const spy = jasmine.createSpyObj('ApplicationService', ['getAllApplications', 'searchApplicationsByName']);

    await TestBed.configureTestingModule({
      imports: [
        ApplicationSelectorComponent,
        ReactiveFormsModule,
        NoopAnimationsModule
      ],
      providers: [
        { provide: ApplicationService, useValue: spy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(ApplicationSelectorComponent);
    component = fixture.componentInstance;
    mockApplicationService = TestBed.inject(ApplicationService) as jasmine.SpyObj<ApplicationService>;

    mockApplicationService.getAllApplications.and.returnValue(of(mockApplications));
    mockApplicationService.searchApplicationsByName.and.returnValue(of(mockApplications));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load applications on init', () => {
    fixture.detectChanges();
    
    expect(mockApplicationService.getAllApplications).toHaveBeenCalledWith({ isDeleted: false });
    expect(component.allApplications).toEqual(mockApplications);
    expect(component.filteredApplications).toEqual(mockApplications);
  });

  it('should emit selection change when application is selected', () => {
    spyOn(component.selectionChange, 'emit');
    const testApp = mockApplications[0];

    component.onApplicationSelected(testApp);

    expect(component.selectedApplications).toContain(testApp);
    expect(component.selectionChange.emit).toHaveBeenCalledWith([testApp]);
  });

  it('should emit selection change when application is removed', () => {
    spyOn(component.selectionChange, 'emit');
    const testApp = mockApplications[0];
    component.selectedApplications = [testApp];

    component.onApplicationRemoved(testApp);

    expect(component.selectedApplications).not.toContain(testApp);
    expect(component.selectionChange.emit).toHaveBeenCalledWith([]);
  });

  it('should not add duplicate applications', () => {
    const testApp = mockApplications[0];
    component.selectedApplications = [testApp];

    component.onApplicationSelected(testApp);

    expect(component.selectedApplications.length).toBe(1);
  });

  it('should show error when required and no applications selected', () => {
    component.required = true;
    component.selectedApplications = [];

    expect(component.hasError).toBeTruthy();
    expect(component.errorMessage).toBe('Mindestens eine Anwendung muss ausgewählt werden');
  });

  it('should not show error when not required', () => {
    component.required = false;
    component.selectedApplications = [];

    expect(component.hasError).toBeFalsy();
  });

  it('should filter out selected applications from filtered list', () => {
    const testApp = mockApplications[0];
    component.allApplications = mockApplications;
    component.selectedApplications = [testApp];

    component.onApplicationSelected(testApp);

    expect(component.filteredApplications).not.toContain(testApp);
  });

  it('should display application name correctly', () => {
    const testApp = mockApplications[0];
    
    const result = component.displayFn(testApp);
    
    expect(result).toBe(testApp.name);
  });

  it('should return empty string for null application in displayFn', () => {
    const result = component.displayFn(null as any);
    
    expect(result).toBe('');
  });
});