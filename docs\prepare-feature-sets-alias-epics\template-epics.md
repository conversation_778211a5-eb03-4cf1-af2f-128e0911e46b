Du bist ein erfahrener Software-Architekt und schreibst ein detailliertes EPIC für die Softwareentwicklung. Verwende die folgende Struktur und Checkliste:



## EPIC-Struktur Template

### 1. EPIC Header
- **Titel:** Prä<PERSON>se Beschreibung in 3-5 Worten
- **Ziel:** Ein klarer Satz, was erreicht werden soll
- **Referenz:** Feature-ID (z.B. F-UI-001)

### 2. Phasen-Aufbau (3-5 Phasen max)
Für jede Phase:
- **Phase X: [Name] ([Feature-ID])**
- **Kurzbeschreibung:** <PERSON>tz, was in dieser Phase umgesetzt wird

#### Checkliste [Phase-Name]
- [ ] Konkrete, messbare Aufgaben
- [ ] Datei-/Component-Namen mit Pfaden
- [ ] Technische Implementierungsdetails
- [ ] API-Integration falls nötig

#### Akzeptanzkriterien [Phase-Name]
- [ ] Testbare Funktionalitäten
- [ ] Messbare Ergebnisse
- [ ] Benutzer-sichtbare Features

### 3. Technische Anforderungen
#### Entwicklungsumgebung Setup
- [ ] Framework/Library Setup
- [ ] Dependencies Installation
- [ ] Konfiguration

#### Code-Struktur
```
Zeige klare Ordnerstruktur mit:
- Komponenten-Pfaden
- Service-Dateien
- Type-Definitionen
```

#### Performance-Ziele
- [ ] Messbare Ladezeiten
- [ ] Response-Zeiten
- [ ] Optimierungsziele


### 3. Abhängigkeiten & Risiken
#### Abhängigkeiten
- [ ] Vor Entwicklungsstart
- [ ] Externe Dependencies

#### Risiken & Lösungen
| Risiko | Lösung |
|--------|--------|
| [Problem] | [Konkrete Lösung] |

### 7. Nächste Schritte
1. **Sofort starten:** [Erste konkrete Aufgabe]
2. **Nach [Phase 1]:** [Zweite Aufgabe]
3. **Parallel möglich:** [Parallele Aufgaben]
4. **Abschluss:** [Finale Integration]

## KI-Entwicklungshinweise
- Verwende moderne Best Practices
- Implementiere von Anfang an responsive Design
- Nutze TypeScript für Type Safety
- Befolge Clean Code Prinzipien
- Erstelle wiederverwendbare Components
- Implementiere Error Handling

---

## Anwendung
Verwende dieses Template mit dem referenzierten EPIC:
@/docs/EPIC-admin-ui-foundation.md

**Auftrag:** Schreibe das EPIC neu unter Verwendung dieser optimierten Struktur für maximale KI-Verständlichkeit und Umsetzbarkeit.