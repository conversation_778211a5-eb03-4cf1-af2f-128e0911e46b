import { of } from 'rxjs';
import { Application } from '../../../core/models/application.model';

export const mockApplication: Application = {
  identifier: 'app1',
  name: 'Application 1',
  isDeleted: false,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

export const mockApplications: Application[] = [
  mockApplication,
  {
    identifier: 'app2',
    name: 'Application 2',
    isDeleted: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

export class MockApplicationsFacade {
  applications$ = of(mockApplications);
  selectedApplication$ = of(mockApplication);
  loading$ = of(false);
  error$ = of(null);

  loadApplications = jasmine.createSpy('loadApplications');
  selectApplication = jasmine.createSpy('selectApplication');
  createApplication = jasmine.createSpy('createApplication');
  updateApplication = jasmine.createSpy('updateApplication');
  deleteApplication = jasmine.createSpy('deleteApplication');
}