import { TestBed } from '@angular/core/testing';
import { provideMockActions } from '@ngrx/effects/testing';
import { provideMockStore } from '@ngrx/store/testing';
import { Observable, of, throwError } from 'rxjs';
import { ApplicationsEffects } from './applications.effects';
import * as ApplicationActions from './applications.actions';
import { ApplicationService } from '../../core/services/application.service';
import { Application } from '../../core/models/application.model';

describe('ApplicationsEffects', () => {
  let actions$: Observable<any>;
  let effects: ApplicationsEffects;
  let applicationService: jasmine.SpyObj<ApplicationService>;

  const mockApplication: Application = {
    identifier: 'app1',
    name: 'Application 1',
    isDeleted: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  beforeEach(() => {
    const spy = jasmine.createSpyObj('ApplicationService', [
      'getAllApplications',
      'createApplication',
      'updateApplication',
      'deleteApplication',
    ]);

    TestBed.configureTestingModule({
      providers: [
        ApplicationsEffects,
        provideMockActions(() => actions$),
        provideMockStore(),
        { provide: ApplicationService, useValue: spy },
      ],
    });

    effects = TestBed.inject(ApplicationsEffects);
    applicationService = TestBed.inject(ApplicationService) as jasmine.SpyObj<ApplicationService>;
  });

  describe('loadApplications$', () => {
    it('should return loadApplicationsSuccess on success', (done) => {
      const applications = [mockApplication];
      applicationService.getAllApplications.and.returnValue(of(applications));
      actions$ = of(ApplicationActions.loadApplications({ filter: {} }));

      effects.loadApplications$.subscribe((result) => {
        expect(result).toEqual(ApplicationActions.loadApplicationsSuccess({ applications }));
        done();
      });
    });

    it('should return loadApplicationsFailure on error', (done) => {
      const error = 'Error';
      applicationService.getAllApplications.and.returnValue(throwError(() => error));
      actions$ = of(ApplicationActions.loadApplications({ filter: {} }));

      effects.loadApplications$.subscribe((result) => {
        expect(result).toEqual(ApplicationActions.loadApplicationsFailure({ error }));
        done();
      });
    });
  });

  describe('createApplication$', () => {
    it('should return createApplicationSuccess on success', (done) => {
      applicationService.createApplication.and.returnValue(of(mockApplication));
      actions$ = of(ApplicationActions.createApplication({ application: { name: 'new' } }));

      effects.createApplication$.subscribe((result) => {
        expect(result).toEqual(ApplicationActions.createApplicationSuccess({ application: mockApplication }));
        done();
      });
    });

    it('should return createApplicationFailure on error', (done) => {
      const error = 'Error';
      applicationService.createApplication.and.returnValue(throwError(() => error));
      actions$ = of(ApplicationActions.createApplication({ application: { name: 'new' } }));

      effects.createApplication$.subscribe((result) => {
        expect(result).toEqual(ApplicationActions.createApplicationFailure({ error }));
        done();
      });
    });
  });

  describe('updateApplication$', () => {
    it('should return updateApplicationSuccess on success', (done) => {
      applicationService.updateApplication.and.returnValue(of(mockApplication));
      actions$ = of(ApplicationActions.updateApplication({ application: { identifier: 'app1' } }));

      effects.updateApplication$.subscribe((result) => {
        expect(result).toEqual(ApplicationActions.updateApplicationSuccess({ application: mockApplication }));
        done();
      });
    });

    it('should return updateApplicationFailure on error', (done) => {
      const error = 'Error';
      applicationService.updateApplication.and.returnValue(throwError(() => error));
      actions$ = of(ApplicationActions.updateApplication({ application: { identifier: 'app1' } }));

      effects.updateApplication$.subscribe((result) => {
        expect(result).toEqual(ApplicationActions.updateApplicationFailure({ error }));
        done();
      });
    });
  });

  describe('deleteApplication$', () => {
    it('should return deleteApplicationSuccess on success', (done) => {
      const deletedApplication = { ...mockApplication, isDeleted: true };
      applicationService.deleteApplication.and.returnValue(of(deletedApplication));
      actions$ = of(ApplicationActions.deleteApplication({ identifier: 'app1' }));

      effects.deleteApplication$.subscribe((result) => {
        expect(result).toEqual(ApplicationActions.deleteApplicationSuccess({ identifier: 'app1' }));
        done();
      });
    });

    it('should return deleteApplicationFailure on error', (done) => {
      const error = 'Error';
      applicationService.deleteApplication.and.returnValue(throwError(() => error));
      actions$ = of(ApplicationActions.deleteApplication({ identifier: 'app1' }));

      effects.deleteApplication$.subscribe((result) => {
        expect(result).toEqual(ApplicationActions.deleteApplicationFailure({ error }));
        done();
      });
    });
  });
});