import { Component, Input, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { Incident, IncidentType } from '../../../../core/models/incident.model';

@Component({
  selector: 'app-active-incidents-list',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatListModule, MatIconModule, MatChipsModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <mat-card class="incidents-card">
      <mat-card-header>
        <mat-card-title>Aktive Vorfälle</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div *ngIf="incidents.length === 0" class="empty-state">
          <mat-icon>check_circle</mat-icon>
          <p>Keine aktiven Vorfälle</p>
        </div>
        <mat-list *ngIf="incidents.length > 0">
          <mat-list-item *ngFor="let incident of incidents; trackBy: trackByIncidentId">
            <mat-icon matListItemIcon [class]="getIncidentIconClass(incident.type)">
              {{ getIncidentIcon(incident.type) }}
            </mat-icon>
            <div matListItemTitle>{{ incident.title }}</div>
            <div matListItemLine>
              <div class="incident-meta">
                <mat-chip [class]="getIncidentTypeClass(incident.type)">
                  {{ getIncidentTypeLabel(incident.type) }}
                </mat-chip>
              </div>
              <span class="incident-time">{{ incident.startTime | date:'short' }}</span>
            </div>
          </mat-list-item>
        </mat-list>
      </mat-card-content>
    </mat-card>
  `,
  styles: [`
    .incidents-card {
      border: 1px solid var(--border-color);
      box-shadow: none;
      border-radius: var(--border-radius-md);
      background-color: var(--funk-white);
    }

    .incidents-card mat-card-title {
      font-size: 1rem;
      font-weight: 600;
      color: var(--funk-blue);
      margin: 0;
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 2rem;
      color: var(--text-secondary);
      text-align: center;
    }

    .empty-state mat-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
      margin-bottom: 1rem;
      opacity: 0.5;
      color: var(--success-color);
    }

    .empty-state p {
      margin: 0;
      font-size: 0.875rem;
    }

    mat-list {
      padding: 0;
    }

    mat-list-item {
      border-bottom: 1px solid var(--border-color);
      padding: 1rem 1rem;
      min-height: 72px;
    }

    mat-list-item:last-child {
      border-bottom: none;
    }

    mat-list-item:hover {
      background-color: rgba(0, 0, 0, 0.02);
    }

    .incident-icon {
      font-size: 1.25rem;
      width: 1.25rem;
      height: 1.25rem;
    }

    .incident-icon.error {
      color: var(--error-color);
    }

    .incident-icon.warning {
      color: var(--warning-color);
    }

    .incident-icon.info {
      color: var(--info-color);
    }

    .incident-chip {
      font-size: 0.6875rem;
      height: 1.25rem;
      min-height: 1.25rem;
      padding: 0 0.5rem;
      border-radius: 0.75rem;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.025em;
    }

    .incident-chip.error {
      background-color: #d32f2f;
      color: white;
    }

    .incident-chip.warning {
      background-color: #f57c00;
      color: white;
    }

    .incident-chip.info {
      background-color: #1976d2;
      color: white;
    }

    .incident-time {
      font-size: 0.6875rem;
      color: var(--text-secondary);
      font-weight: 400;
      white-space: nowrap;
    }

    [matListItemLine] {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 0.75rem;
      margin-top: 0.5rem;
    }

    [matListItemTitle] {
      font-weight: 500;
      color: var(--text-primary);
      margin-bottom: 0.25rem;
    }

    .incident-meta {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      flex-wrap: wrap;
    }
  `]
})
export class ActiveIncidentsListComponent {
  @Input() incidents: Incident[] = [];

  getIncidentIcon(type: IncidentType): string {
    switch (type) {
      case IncidentType.STOERUNG: return 'error';
      case IncidentType.WARTUNGSFENSTER: return 'build';
      case IncidentType.KEINE_STOERUNG: return 'info';
      default: return 'info';
    }
  }

  getIncidentIconClass(type: IncidentType): string {
    switch (type) {
      case IncidentType.STOERUNG: return 'incident-icon error';
      case IncidentType.WARTUNGSFENSTER: return 'incident-icon warning';
      case IncidentType.KEINE_STOERUNG: return 'incident-icon info';
      default: return 'incident-icon info';
    }
  }

  getIncidentTypeClass(type: IncidentType): string {
    switch (type) {
      case IncidentType.STOERUNG: return 'incident-chip error';
      case IncidentType.WARTUNGSFENSTER: return 'incident-chip warning';
      case IncidentType.KEINE_STOERUNG: return 'incident-chip info';
      default: return 'incident-chip info';
    }
  }

  getIncidentTypeLabel(type: IncidentType): string {
    switch (type) {
      case IncidentType.STOERUNG: return 'Störung';
      case IncidentType.WARTUNGSFENSTER: return 'Wartung';
      case IncidentType.KEINE_STOERUNG: return 'Info';
      default: return 'Info';
    }
  }

  trackByIncidentId(index: number, incident: Incident): string {
    return incident.identifier;
  }
}