# EPIC: Applikationsverwaltung (Stammdatenpflege)

## 1. EPIC Header
- **Titel:** Application Management Dashboard
- **Ziel:** Vollständige CRUD-Verwaltung von Applikationen als Grundlage für Störungsmeldungen
- **Referenz:** F-APP-001 bis F-APP-005

## 2. Phasen-Aufbau

### Phase 1: Applikations-Liste Foundation (F-APP-001)
**Kurzbeschreibung:** Implementierung der grundlegenden Applikationsliste mit Anzeige, Filter und Suchfunktionen

#### Checkliste Applikations-Liste Foundation
- [ ] Component erstellen: `src/app/features/application-management/components/application-list/application-list.component.ts`
- [ ] Template erstellen: `src/app/features/application-management/components/application-list/application-list.component.html`
- [ ] Styling implementieren: `src/app/features/application-management/components/application-list/application-list.component.scss`
- [ ] Application Model definieren: `src/app/core/models/application.model.ts`
- [ ] Application Service erstellen: `src/app/core/services/application.service.ts`
- [ ] GraphQL Queries definieren: `src/app/core/graphql/application.queries.ts`
- [ ] Angular Material Table mit Sortierung implementieren
- [ ] Filter-Component für gelöschte Applikationen: `src/app/features/application-management/components/application-filter/application-filter.component.ts`
- [ ] Suchfunktion mit Debounce implementieren (basierend auf applicationsByName Query)
- [ ] Loading States und Error Handling

#### Akzeptanzkriterien Applikations-Liste Foundation
- [ ] Tabellarische Darstellung aller Applikationen mit Name, Beschreibung, Erstellungsdatum, letzte Aktualisierung
- [ ] Filterung nach gelöschten/aktiven Applikationen (isDeleted Flag)
- [ ] Suchfunktion nach Applikationsname mit Live-Suche (applicationsByName Query)
- [ ] Sortierung nach allen Spalten (aufsteigend/absteigend)
- [ ] Responsive Design für Mobile und Desktop
- [ ] Loading Spinner während Datenabfrage
- [ ] Error Messages bei API-Fehlern

### Phase 2: Applikations CRUD Operations (F-APP-002, F-APP-003, F-APP-004)
**Kurzbeschreibung:** Implementierung aller CRUD-Operationen mit Formularen und Validierung

#### Checkliste Applikations CRUD Operations
- [ ] Application-Form Component: `src/app/features/application-management/components/application-form/application-form.component.ts`
- [ ] Add-Application Dialog: `src/app/features/application-management/dialogs/add-application-dialog/add-application-dialog.component.ts`
- [ ] Edit-Application Dialog: `src/app/features/application-management/dialogs/edit-application-dialog/edit-application-dialog.component.ts`
- [ ] Delete-Confirmation Dialog: `src/app/shared/components/delete-confirmation-dialog/delete-confirmation-dialog.component.ts`
- [ ] GraphQL Mutations definieren: `src/app/core/graphql/application.mutations.ts`
- [ ] Reactive Forms mit Validierung implementieren
- [ ] Optimistic Updates für bessere UX
- [ ] Success/Error Notifications
- [ ] Dependency Check Service für Löschoperationen (prüfe incidents Beziehung)

#### Akzeptanzkriterien Applikations CRUD Operations
- [ ] Applikation hinzufügen: Formular mit Name (Pflicht), Beschreibung (optional) - createApplication Mutation
- [ ] Validierung: Name nicht leer, Name eindeutig, Beschreibung optional
- [ ] Applikation bearbeiten: Vorausgefülltes Formular mit allen Feldern editierbar - updateApplication Mutation
- [ ] Applikation löschen: Soft Delete mit isDeleted Flag - deleteApplication Mutation
- [ ] Warnung bei bestehenden Abhängigkeiten (verknüpfte Incidents)
- [ ] Bestätigungsmeldungen nach jeder Operation
- [ ] Automatische Aktualisierung der Applikationsliste nach Änderungen
- [ ] Form-Validierung mit aussagekräftigen Fehlermeldungen

### Phase 3: Applikations-Details & Navigation (F-APP-005)
**Kurzbeschreibung:** Detailansicht mit erweiterten Informationen und Navigation zu verwandten Entitäten

#### Checkliste Applikations-Details & Navigation
- [ ] Application-Detail Component: `src/app/features/application-management/components/application-detail/application-detail.component.ts`
- [ ] Application-Detail Route: `/applications/:id` in `src/app/features/application-management/application-management-routing.module.ts`
- [ ] Related-Incidents Component: `src/app/features/application-management/components/related-incidents/related-incidents.component.ts`
- [ ] Change-History Component: `src/app/features/application-management/components/change-history/change-history.component.ts`
- [ ] Breadcrumb Navigation implementieren
- [ ] GraphQL Query für Application-Details mit Relations (application Query mit incidents und changeHistory)
- [ ] Lazy Loading für Related Data
- [ ] Back-Navigation und Deep-Linking

#### Akzeptanzkriterien Applikations-Details & Navigation
- [ ] Vollständige Applikationsinformationen (Name, Beschreibung, isDeleted, Timestamps)
- [ ] Anzeige von Erstellungs- und letztem Änderungsdatum (createdAt, updatedAt)
- [ ] Liste der zugehörigen Störungsmeldungen (incidents Beziehung)
- [ ] Change History Anzeige (changeHistory Beziehung)
- [ ] Navigation zurück zur Applikationsliste
- [ ] Direct-Link zu Applikations-Details funktioniert
- [ ] Responsive Layout für alle Bildschirmgrößen

## 3. Technische Anforderungen

### Entwicklungsumgebung Setup
- [ ] Angular Material Components: MatTable, MatDialog, MatFormField, MatSelect, MatButton
- [ ] Apollo Client für GraphQL-Integration
- [ ] NgRx für State Management (optional für Phase 1)
- [ ] Angular Reactive Forms für Formulare
- [ ] RxJS für asynchrone Operationen

### Code-Struktur
```
src/app/features/application-management/
├── components/
│   ├── application-list/
│   │   ├── application-list.component.ts
│   │   ├── application-list.component.html
│   │   └── application-list.component.scss
│   ├── application-form/
│   │   ├── application-form.component.ts
│   │   ├── application-form.component.html
│   │   └── application-form.component.scss
│   ├── application-detail/
│   │   ├── application-detail.component.ts
│   │   ├── application-detail.component.html
│   │   └── application-detail.component.scss
│   ├── application-filter/
│   │   ├── application-filter.component.ts
│   │   ├── application-filter.component.html
│   │   └── application-filter.component.scss
│   ├── related-incidents/
│   │   ├── related-incidents.component.ts
│   │   ├── related-incidents.component.html
│   │   └── related-incidents.component.scss
│   └── change-history/
│       ├── change-history.component.ts
│       ├── change-history.component.html
│       └── change-history.component.scss
├── dialogs/
│   ├── add-application-dialog/
│   ├── edit-application-dialog/
│   └── delete-confirmation-dialog/
├── application-management-routing.module.ts
└── application-management.module.ts

src/app/core/
├── models/
│   ├── application.model.ts
│   ├── incident.model.ts
│   └── change-history.model.ts
├── services/
│   └── application.service.ts
├── graphql/
│   ├── application.queries.ts
│   └── application.mutations.ts
└── enums/
    ├── incident-type.enum.ts
    └── entity-type.enum.ts
```

### Performance-Ziele
- [ ] Applikationsliste lädt in < 500ms bei 100 Applikationen
- [ ] Filter-/Suchoperationen reagieren in < 200ms
- [ ] CRUD-Operationen bestätigt in < 1s
- [ ] Optimistic Updates für sofortige UI-Reaktion

## 4. Abhängigkeiten & Risiken

### Abhängigkeiten
- [ ] GraphQL-Backend-API mit Application-Schema verfügbar (siehe docs/api/graphql-api.md)
- [ ] Apollo Client konfiguriert und funktionsfähig
- [ ] Authentifizierung/Autorisierung implementiert
- [ ] Angular Material Theme konfiguriert

### Risiken & Lösungen
| Risiko | Lösung |
|--------|--------|
| GraphQL-Schema Änderungen | Code-First Approach mit TypeScript Interfaces basierend auf API-Dokumentation |
| Performance bei vielen Applikationen | Pagination implementieren (API unterstützt noch keine Pagination) |
| Concurrent Modifications | Change History für Audit Trail nutzen |
| Dependency-Check Komplexität | incidents Beziehung prüfen vor Soft Delete |

## 5. Nächste Schritte

1. **Sofort starten:** Application Model und Service-Grundstruktur erstellen basierend auf GraphQL API
2. **Nach Phase 1:** CRUD-Dialoge implementieren und testen mit korrekten Mutations
3. **Parallel möglich:** GraphQL-Queries/Mutations aus API-Dokumentation implementieren
4. **Abschluss:** Integration Tests und E2E-Tests für kompletten Workflow

## KI-Entwicklungshinweise

### Angular-spezifische Best Practices
- Verwende Standalone Components (Angular 19)
- Implementiere OnPush Change Detection für Performance
- Nutze Angular Material CDK für erweiterte UI-Funktionen
- Reactive Forms mit Custom Validators für Geschäftslogik

### GraphQL Integration
- Type-safe GraphQL mit Apollo Angular
- Fragment-basierte Queries für Wiederverwendbarkeit
- Error Handling mit Apollo Error Link
- Caching-Strategien für bessere Performance

### State Management
- Lokaler Component State für UI-spezifische Daten
- Service-basierter State für geteilte Daten
- NgRx nur bei komplexer State-Logik einsetzen

### Testing Strategy
- Unit Tests für alle Services und Components
- Integration Tests für GraphQL-Operationen
- E2E Tests für kritische User Journeys
- Mock GraphQL-Responses für isolierte Tests

### Accessibility & UX
- ARIA-Labels für Screen Reader
- Keyboard Navigation für alle Interaktionen
- Loading States und Progress Indicators
- Consistent Error Messaging


### Hauptentitäten (basierend auf GraphQL API)
```mermaid
erDiagram
    Application {
        ID identifier PK
        string name
        string description
        boolean isDeleted
        string createdAt
        string updatedAt
    }
    
    Incident {
        ID identifier PK
        string title
        string description
        IncidentType type
        string startTime
        string plannedEndTime
        string actualEndTime
        string alternatives
        boolean isResolved
        string createdAt
        string updatedAt
    }
    
    ChangeHistoryEntry {
        ID identifier PK
        EntityType entityType
        ID entityIdentifier FK
        string changedBy
        string changeDescription
        string changedAt
    }
    
    Application ||--o{ Incident : "N:M relationship"
    Application ||--o{ ChangeHistoryEntry : "tracks changes"
    Incident ||--o{ ChangeHistoryEntry : "tracks changes"
```

### GraphQL API Integration

#### Verfügbare Queries
- `application(identifier: ID!)`: Einzelne Applikation abrufen
- `allApplications(isDeleted: Boolean = false)`: Alle Applikationen mit Filter
- `applicationsByName(name: String!)`: Suche nach Name
- `incidentsByApplication(applicationIdentifier: ID!)`: Incidents einer Applikation

#### Verfügbare Mutations
- `createApplication(name: String!, description: String)`: Neue Applikation erstellen
- `updateApplication(identifier: ID!, name: String, description: String, isDeleted: Boolean)`: Applikation aktualisieren
- `deleteApplication(identifier: ID!)`: Soft Delete einer Applikation

#### Enums
- `IncidentType`: STOERUNG, WARTUNGSFENSTER, KEINE_STOERUNG
- `EntityType`: APPLIKATION, STOERUNG

---

**Status:** Ready for Implementation  
**Estimated Effort:** 3-5 Entwicklungstage  
**Priority:** High (Grundlage für alle anderen Features)