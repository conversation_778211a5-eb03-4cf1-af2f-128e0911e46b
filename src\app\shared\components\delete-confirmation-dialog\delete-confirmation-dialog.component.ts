import { Component, Inject, ChangeDetectionStrategy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatListModule } from '@angular/material/list';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { FormsModule } from '@angular/forms';
import { DependencyCheckService, DependencyCheckResult, DependencyInfo } from '../../../core/services/dependency-check.service';

export interface DeleteConfirmationData {
  title: string;
  message: string;
  entityType: string;
  entityId: string;
  entityName: string;
  warningMessage?: string;
  hasDependendencies?: boolean;
  allowForceDelete?: boolean;
}

@Component({
  selector: 'app-delete-confirmation-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatExpansionModule,
    MatListModule,
    MatCheckboxModule,
    FormsModule
  ],
  templateUrl: './delete-confirmation-dialog.component.html',
  styleUrls: ['./delete-confirmation-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DeleteConfirmationDialogComponent implements OnInit {
  loading = false;
  checkingDependencies = false;
  dependencyResult: DependencyCheckResult | null = null;
  forceDelete = false;
  confirmForceDelete = false;

  constructor(
    public dialogRef: MatDialogRef<DeleteConfirmationDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DeleteConfirmationData,
    public dependencyCheckService: DependencyCheckService
  ) {}

  ngOnInit(): void {
    this.checkDependencies();
  }

  private checkDependencies(): void {
    if (this.data.entityType.toLowerCase() === 'application') {
      this.checkingDependencies = true;
      
      this.dependencyCheckService.checkApplicationDependencies(this.data.entityId)
        .subscribe({
          next: (result) => {
            this.dependencyResult = result;
            this.checkingDependencies = false;
          },
          error: (error) => {
            console.error('Error checking dependencies:', error);
            this.checkingDependencies = false;
            // Continue without dependency check
          }
        });
    }
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }

  onConfirm(): void {
    this.loading = true;
    
    const result = {
      confirmed: true,
      forceDelete: this.forceDelete,
      dependencyInfo: this.dependencyResult?.dependencies
    };

    // Simulate API call delay
    setTimeout(() => {
      this.dialogRef.close(result);
    }, 500);
  }

  get canDelete(): boolean {
    if (!this.dependencyResult) {
      return true; // Allow deletion if no dependency check was performed
    }
    
    return this.dependencyResult.dependencies.canDelete || this.forceDelete;
  }

  get showForceDeleteOption(): boolean {
    return !!(this.data.allowForceDelete &&
           this.dependencyResult &&
           !this.dependencyResult.dependencies.canDelete);
  }

  get confirmButtonText(): string {
    if (this.forceDelete) {
      return 'Trotzdem löschen';
    }
    
    if (this.dependencyResult?.dependencies.canDelete === false) {
      return 'Kann nicht gelöscht werden';
    }
    
    return 'Löschen';
  }

  get confirmButtonColor(): string {
    if (this.forceDelete) {
      return 'warn';
    }
    
    if (this.dependencyResult?.dependencies.warnings.length) {
      return 'accent';
    }
    
    return 'primary';
  }

  // Added methods that were previously in DependencyCheckService
  getDependencyIcon(): string {
    if (!this.dependencyResult) {
      return 'help';
    }
    
    const dependencies = this.dependencyResult.dependencies;
    
    if (!dependencies.canDelete) {
      return 'block';
    }
    
    if (dependencies.warnings.length > 0) {
      return 'warning';
    }
    
    return 'check_circle';
  }

  getDependencyColor(): string {
    if (!this.dependencyResult) {
      return '';
    }
    
    const dependencies = this.dependencyResult.dependencies;
    
    if (!dependencies.canDelete) {
      return 'warn';
    }
    
    if (dependencies.warnings.length > 0) {
      return 'accent';
    }
    
    return 'primary';
  }

  getDependencySummary(): string {
    if (!this.dependencyResult) {
      return 'Abhängigkeiten werden überprüft...';
    }
    
    const dependencies = this.dependencyResult.dependencies;
    
    if (!dependencies.canDelete) {
      return 'Löschen nicht möglich aufgrund von Abhängigkeiten';
    }
    
    if (dependencies.warnings.length > 0) {
      return 'Löschen möglich, aber es gibt Warnungen';
    }
    
    return 'Keine Abhängigkeiten gefunden, Löschen ist sicher';
  }
  
  // Added method for severity that was previously in DependencyCheckService
  getDependencySeverity(dependencies: DependencyInfo): string {
    if (!dependencies.canDelete) {
      return 'error';
    }
    
    if (dependencies.warnings.length > 0) {
      return 'warning';
    }
    
    return 'success';
  }

  onForceDeleteChange(): void {
    if (this.forceDelete) {
      this.confirmForceDelete = false;
    }
  }
}