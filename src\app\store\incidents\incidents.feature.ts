import { createFeature } from '@ngrx/store';
import { incidentsReducer } from './incidents.reducer';

export const incidentsFeature = createFeature({
  name: 'incidents',
  reducer: incidentsReducer
});

// Export the generated selectors with different names to avoid conflicts
export const {
  selectIncidentsState: selectFeatureIncidentsState,
  selectIncidents: selectFeatureIncidents,
  selectIsLoading: selectFeatureIsLoading,
  selectError: selectFeatureError
} = incidentsFeature;