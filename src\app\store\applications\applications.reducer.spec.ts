import { applicationsReducer } from './applications.reducer';
import { initialApplicationsState } from './applications.state';
import * as ApplicationActions from './applications.actions';
import { Application } from '../../core/models/application.model';

describe('Applications Reducer', () => {
  const mockApplication: Application = {
    identifier: 'app1',
    name: 'Application 1',
    isDeleted: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  const anotherMockApplication: Application = {
    identifier: 'app2',
    name: 'Application 2',
    isDeleted: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  it('should return the initial state', () => {
    const action = {} as any;
    const state = applicationsReducer(initialApplicationsState, action);
    expect(state).toBe(initialApplicationsState);
  });

  describe('Load Applications', () => {
    it('should set loading to true on loadApplications', () => {
      const action = ApplicationActions.loadApplications({ filter: {} });
      const state = applicationsReducer(initialApplicationsState, action);
      expect(state.loading).toBe(true);
      expect(state.error).toBe(null);
    });

    it('should add applications on loadApplicationsSuccess', () => {
      const applications = [mockApplication];
      const action = ApplicationActions.loadApplicationsSuccess({ applications });
      const state = applicationsReducer(
        { ...initialApplicationsState, loading: true },
        action
      );
      expect(state.applications).toEqual(applications);
      expect(state.loading).toBe(false);
    });

    it('should set error on loadApplicationsFailure', () => {
      const error = 'Error loading applications';
      const action = ApplicationActions.loadApplicationsFailure({ error });
      const state = applicationsReducer(
        { ...initialApplicationsState, loading: true },
        action
      );
      expect(state.error).toBe(error);
      expect(state.loading).toBe(false);
    });
  });

  describe('Select Application', () => {
    it('should select an application by identifier', () => {
      const currentState = {
        ...initialApplicationsState,
        applications: [mockApplication, anotherMockApplication],
      };
      const action = ApplicationActions.selectApplication({ identifier: 'app2' });
      const state = applicationsReducer(currentState, action);
      expect(state.selectedApplication).toEqual(anotherMockApplication);
    });
  });

  describe('Create Application', () => {
    it('should set loading to true on createApplication', () => {
      const action = ApplicationActions.createApplication({
        application: { name: 'New App' },
      });
      const state = applicationsReducer(initialApplicationsState, action);
      expect(state.loading).toBe(true);
    });

    it('should add a new application on createApplicationSuccess', () => {
      const action = ApplicationActions.createApplicationSuccess({
        application: mockApplication,
      });
      const state = applicationsReducer(
        { ...initialApplicationsState, loading: true },
        action
      );
      expect(state.applications).toEqual([mockApplication]);
      expect(state.loading).toBe(false);
    });

    it('should set error on createApplicationFailure', () => {
      const error = 'Error creating application';
      const action = ApplicationActions.createApplicationFailure({ error });
      const state = applicationsReducer(
        { ...initialApplicationsState, loading: true },
        action
      );
      expect(state.error).toBe(error);
      expect(state.loading).toBe(false);
    });
  });

  describe('Update Application', () => {
    it('should set loading to true on updateApplication', () => {
      const action = ApplicationActions.updateApplication({
        application: { identifier: 'app1', name: 'Updated App' },
      });
      const state = applicationsReducer(initialApplicationsState, action);
      expect(state.loading).toBe(true);
    });

    it('should update an application on updateApplicationSuccess', () => {
      const updatedApplication = { ...mockApplication, name: 'Updated Name' };
      const currentState = {
        ...initialApplicationsState,
        applications: [mockApplication],
      };
      const action = ApplicationActions.updateApplicationSuccess({
        application: updatedApplication,
      });
      const state = applicationsReducer(currentState, action);
      expect(state.applications).toEqual([updatedApplication]);
    });

    it('should set error on updateApplicationFailure', () => {
      const error = 'Error updating application';
      const action = ApplicationActions.updateApplicationFailure({ error });
      const state = applicationsReducer(
        { ...initialApplicationsState, loading: true },
        action
      );
      expect(state.error).toBe(error);
      expect(state.loading).toBe(false);
    });
  });

  describe('Delete Application', () => {
    it('should set loading to true on deleteApplication', () => {
      const action = ApplicationActions.deleteApplication({ identifier: 'app1' });
      const state = applicationsReducer(initialApplicationsState, action);
      expect(state.loading).toBe(true);
    });

    it('should remove an application on deleteApplicationSuccess', () => {
      const currentState = {
        ...initialApplicationsState,
        applications: [mockApplication, anotherMockApplication],
      };
      const action = ApplicationActions.deleteApplicationSuccess({
        identifier: 'app1',
      });
      const state = applicationsReducer(currentState, action);
      expect(state.applications).toEqual([anotherMockApplication]);
    });

    it('should set error on deleteApplicationFailure', () => {
      const error = 'Error deleting application';
      const action = ApplicationActions.deleteApplicationFailure({ error });
      const state = applicationsReducer(
        { ...initialApplicationsState, loading: true },
        action
      );
      expect(state.error).toBe(error);
      expect(state.loading).toBe(false);
    });
  });
});