# Technical Constraints - StoerungsBuddy Frontend v2

## Technology Stack Overview

This document defines the core technology stack and architectural constraints for the StoerungsBuddy Frontend v2 project. All development work must adhere to these technical specifications.

### Core Framework
- **Framework**: Angular 19
  - Latest stable version with standalone components
  - TypeScript-based development
  - SCSS for styling
  - Routing enabled for SPA navigation

### State Management
- **State Management**: NgRx
  - Centralized state management using NgRx Store
  - Side effects handling with NgRx Effects
  - Reactive state management patterns
  - Immutable state updates

### UI Framework
- **UI Framework**: Angular Material
  - Material Design components
  - Consistent design system
  - Accessibility features built-in
  - Theme: Indigo-Pink
  - Typography and animations enabled

### API Integration
- **API Integration**: Apollo Client for GraphQL
  - GraphQL client for efficient data fetching
  - Caching and optimistic updates
  - Type-safe GraphQL operations
  - Integration with Angular services

## Development Environment

### Package Manager
- **Package Manager**: npm
- Node.js version: 20.19.1
- Angular CLI: 19.2.13

### Build System
- Angular CLI build system
- TypeScript compilation
- SCSS preprocessing
- Production optimization

## Architectural Principles

### Component Architecture
- Standalone components (Angular 19 default)
- Smart/Container and Dumb/Presentational component pattern
- Reactive forms for user input
- OnPush change detection strategy where applicable

### State Management Patterns
- Unidirectional data flow
- Actions, Reducers, Effects pattern
- Selectors for state queries
- Immutable state updates

### API Communication
- GraphQL-first approach
- Type-safe API operations
- Centralized error handling
- Optimistic updates for better UX

## Code Quality Standards

### TypeScript
- Strict TypeScript configuration
- Type safety enforcement
- Interface definitions for all data models
- Generic types where appropriate

### Testing
- Unit testing with Jasmine/Karma
- Component testing
- Service testing
- E2E testing capabilities

### Code Style
- Angular style guide compliance
- Consistent naming conventions
- Modular architecture
- Separation of concerns

## Performance Considerations

### Bundle Optimization
- Lazy loading for feature modules
- Tree shaking for unused code
- Code splitting strategies
- Optimized build configurations

### Runtime Performance
- OnPush change detection
- TrackBy functions for ngFor
- Async pipe usage
- Memory leak prevention

## Security Guidelines

### Data Handling
- Input sanitization
- XSS prevention
- CSRF protection
- Secure API communication

### Authentication
- Token-based authentication
- Secure storage practices
- Session management
- Role-based access control

## Deployment Constraints

### Build Requirements
- Production build optimization
- Environment-specific configurations
- Asset optimization
- Source map generation for debugging

### Browser Support
- Modern browser support (ES2020+)
- Progressive enhancement
- Responsive design
- Accessibility compliance (WCAG 2.1)

