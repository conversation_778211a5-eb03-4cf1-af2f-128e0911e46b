import { Compo<PERSON>, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';

// Angular Material
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { MatDialog } from '@angular/material/dialog';

// Models and Services
import { Incident, IncidentType } from '../../../../core/models/incident.model';
import { IncidentsFacade } from '../../../../store/incidents/incidents.facade';
import { IncidentEditDialogComponent } from '../incident-edit-dialog/incident-edit-dialog.component';
import { DeleteConfirmationDialogComponent } from '../../../../shared/components/delete-confirmation-dialog/delete-confirmation-dialog.component';

@Component({
  selector: 'app-incident-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatChipsModule,
    MatDividerModule
  ],
  templateUrl: './incident-detail.component.html',
  styleUrls: ['./incident-detail.component.scss']
})
export class IncidentDetailComponent implements OnInit, OnDestroy {
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private incidentsFacade = inject(IncidentsFacade);
  private snackBar = inject(MatSnackBar);
  private dialog = inject(MatDialog);

  incident: Incident | null = null;
  loading = false;
  error: string | null = null;
  incidentId: string | null = null;

  private destroy$ = new Subject<void>();

  ngOnInit(): void {
    this.setupSubscriptions();
    this.loadIncidentFromRoute();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setupSubscriptions(): void {
    // Subscribe to selected incident
    this.incidentsFacade.selectedIncident$.pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (incident) => {
        this.incident = incident;
      }
    });

    // Subscribe to loading state
    this.incidentsFacade.selectedIncidentLoading$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(isLoading => {
      this.loading = isLoading;
    });

    // Subscribe to error state
    this.incidentsFacade.selectedIncidentError$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(error => {
      this.error = error;
      if (error) {
        this.showErrorMessage(error);
      }
    });
  }

  private loadIncidentFromRoute(): void {
    this.route.paramMap.pipe(
      takeUntil(this.destroy$)
    ).subscribe(params => {
      const id = params.get('id');
      if (id) {
        this.incidentId = id;
        this.incidentsFacade.loadIncident(id);
      } else {
        this.navigateToList();
      }
    });
  }

  getIncidentTypeText(type: IncidentType): string {
    switch (type) {
      case IncidentType.STOERUNG:
        return 'Störung';
      case IncidentType.WARTUNGSFENSTER:
        return 'Wartungsfenster';
      case IncidentType.KEINE_STOERUNG:
        return 'Keine Störung';
      default:
        return type;
    }
  }

  getIncidentTypeColor(type: IncidentType): string {
    switch (type) {
      case IncidentType.STOERUNG:
        return 'warn';
      case IncidentType.WARTUNGSFENSTER:
        return 'accent';
      case IncidentType.KEINE_STOERUNG:
        return 'primary';
      default:
        return '';
    }
  }

  getStatusChipColor(isResolved: boolean): string {
    return isResolved ? 'primary' : 'warn';
  }

  getStatusText(isResolved: boolean): string {
    return isResolved ? 'Gelöst' : 'Aktiv';
  }

  formatDate(dateValue: string | Date): string {
    if (!dateValue) return '-';
    
    const date = dateValue instanceof Date ? dateValue : new Date(dateValue);
    return date.toLocaleDateString('de-DE', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  formatDateLong(dateValue: string | Date): string {
    if (!dateValue) return '-';
    
    const date = dateValue instanceof Date ? dateValue : new Date(dateValue);
    return date.toLocaleDateString('de-DE', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  calculateDuration(): string {
    if (!this.incident?.startTime) return '-';
    
    const startTime = new Date(this.incident.startTime);
    const endTime = this.incident.actualEndTime 
      ? new Date(this.incident.actualEndTime)
      : this.incident.plannedEndTime 
        ? new Date(this.incident.plannedEndTime)
        : new Date();

    const durationMs = endTime.getTime() - startTime.getTime();
    const hours = Math.floor(durationMs / (1000 * 60 * 60));
    const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  }

  navigateToList(): void {
    this.router.navigate(['/incidents']);
  }

  openEditDialog(): void {
    if (!this.incident) return;

    const dialogRef = this.dialog.open(IncidentEditDialogComponent, {
      width: '800px',
      data: {
        incident: this.incident,
        title: `Vorfall bearbeiten: ${this.incident.title}`
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result?.updated) {
        this.showInfoMessage('Vorfall wurde erfolgreich aktualisiert');
        // Reload the incident to get updated data
        if (this.incidentId) {
          this.incidentsFacade.loadIncident(this.incidentId);
        }
      }
    });
  }

  openDeleteDialog(): void {
    if (!this.incident) return;

    const dialogRef = this.dialog.open(DeleteConfirmationDialogComponent, {
      width: '500px',
      data: {
        title: 'Vorfall löschen',
        message: `Möchten Sie den Vorfall "${this.incident.title}" wirklich löschen?`,
        entityType: 'incident',
        entityId: this.incident.identifier,
        entityName: this.incident.title,
        warningMessage: 'Diese Aktion kann nicht rückgängig gemacht werden.',
        allowForceDelete: false
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result?.confirmed && this.incident) {
        this.incidentsFacade.deleteIncident(this.incident.identifier);
        this.showInfoMessage('Vorfall wurde gelöscht');
        this.navigateToList();
      }
    });
  }

  onToggleResolution(): void {
    if (!this.incident) return;

    const updateInput = {
      identifier: this.incident.identifier,
      // Toggle the resolution status
      // Note: This would typically require an API endpoint to handle resolution
      // For now, we'll show a message that this feature needs implementation
    };

    this.showInfoMessage('Auflösungsstatus ändern ist noch nicht implementiert');
  }

  private showErrorMessage(message: string): void {
    this.snackBar.open(message, 'Schließen', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  private showInfoMessage(message: string): void {
    this.snackBar.open(message, 'Schließen', {
      duration: 3000,
      panelClass: ['info-snackbar']
    });
  }
}