import { TestBed } from '@angular/core/testing';
import { Apollo } from 'apollo-angular';
import { of } from 'rxjs';

import { ApplicationService } from './application.service';
import { UpdateApplicationInput } from '../models/application.model';
import { UPDATE_APPLICATION } from '../graphql/application.mutations';

describe('ApplicationService - Update Application', () => {
  let service: ApplicationService;
  let apolloSpy: jasmine.SpyObj<Apollo>;

  beforeEach(() => {
    const spy = jasmine.createSpyObj('Apollo', ['mutate']);

    TestBed.configureTestingModule({
      providers: [
        ApplicationService,
        { provide: Apollo, useValue: spy }
      ]
    });

    service = TestBed.inject(ApplicationService);
    apolloSpy = TestBed.inject(Apollo) as jasmine.SpyObj<Apollo>;
  });

  it('should call updateApplication with correct GraphQL variables', () => {
    // Arrange
    const input: UpdateApplicationInput = {
      identifier: 'c904312225494995a80f45b3dad66ff1',
      name: 'Demo2',
      description: 'Demo2',
      isDeleted: false
    };

    const expectedResponse = {
      data: {
        updateApplication: {
          identifier: 'c904312225494995a80f45b3dad66ff1',
          name: 'Demo2',
          description: 'Demo2',
          isDeleted: false,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        }
      }
    };

    apolloSpy.mutate.and.returnValue(of(expectedResponse));

    // Act
    service.updateApplication(input).subscribe();

    // Assert
    expect(apolloSpy.mutate).toHaveBeenCalledWith({
      mutation: UPDATE_APPLICATION,
      variables: {
        identifier: 'c904312225494995a80f45b3dad66ff1',
        name: 'Demo2',
        description: 'Demo2',
        isDeleted: false
      },
      optimisticResponse: {
        updateApplication: {
          identifier: 'c904312225494995a80f45b3dad66ff1',
          name: 'Demo2',
          description: 'Demo2',
          isDeleted: false,
          createdAt: '',
          updatedAt: jasmine.any(String)
        }
      }
    });
  });

  it('should handle null description correctly', () => {
    // Arrange
    const input: UpdateApplicationInput = {
      identifier: 'test-id',
      name: 'Test App',
      description: undefined,
      isDeleted: false
    };

    const expectedResponse = {
      data: {
        updateApplication: {
          identifier: 'test-id',
          name: 'Test App',
          description: null,
          isDeleted: false,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        }
      }
    };

    apolloSpy.mutate.and.returnValue(of(expectedResponse));

    // Act
    service.updateApplication(input).subscribe();

    // Assert
    expect(apolloSpy.mutate).toHaveBeenCalledWith(jasmine.objectContaining({
      variables: jasmine.objectContaining({
        description: null
      })
    }));
  });
});