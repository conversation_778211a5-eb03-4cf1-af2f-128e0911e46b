# GraphQL Learnings - Create, Update & Delete Mutations

---

# GraphQL Learnings - Create Mutations (Add-Mutations)

## Fehlerhafte Implementierung bei Create-Mutations

### Problem: Inkonsistente Mutation-Parameter-Struktur

Bei der Implementierung von GraphQL Create-Mutations wurde eine inkonsistente Parameter-Struktur verwendet, die nicht mit der erwarteten Backend-API übereinstimmte.

## Fehlerhafter Ansatz

### 1. GraphQL Mutation Definition
**Falsch implementiert:**
```graphql
mutation CreateApplication($name: String!, $description: String) {
  createApplication(name: $name, description: $description) {
    identifier
    name
    description
    isDeleted
    createdAt
    updatedAt
  }
}
```

**Problem:** Verwendung direkter Parameter anstatt eines Input-Objekts.

### 2. Service-Implementierung
**Falsch implementiert:**
```typescript
return this.apollo.mutate<{ createApplication: Application }>({
  mutation: CREATE_APPLICATION,
  variables: input,  // Direktes Input-Objekt ohne Verschachtelung
  // ...
});
```

**Problem:** Das Input-Objekt wurde direkt als variables übergeben, aber die Mutation erwartete individuelle Parameter.

### 3. TypeScript Interface
**Korrekt definiert, aber inkompatibel mit Mutation:**
```typescript
export interface CreateApplicationInput {
  name: string;
  description?: string;
}
```

**Problem:** Interface war korrekt, aber die GraphQL-Mutation erwartete ein anderes Format.

## Korrekte Implementierung

### 1. GraphQL Mutation Definition
**Korrekt:**
```graphql
mutation CreateApplication($input: CreateApplicationInput!) {
  createApplication(input: $input) {
    identifier
    name
    description
    isDeleted
    createdAt
    updatedAt
  }
}
```

**Lösung:** Input-Objekt Pattern für bessere Typsicherheit und Erweiterbarkeit.

### 2. Service-Implementierung
**Korrekt:**
```typescript
return this.apollo.mutate<{ createApplication: Application }>({
  mutation: CREATE_APPLICATION,
  variables: { input },  // Input-Objekt verschachtelt
  update: (cache, { data }) => {
    if (data?.createApplication) {
      // Cache-Update Logic
      const existingApplications = cache.readQuery<{ allApplications: Application[] }>({
        query: GET_ALL_APPLICATIONS
      });

      if (existingApplications) {
        cache.writeQuery({
          query: GET_ALL_APPLICATIONS,
          data: {
            allApplications: [...existingApplications.allApplications, data.createApplication]
          }
        });
      }
    }
  }
});
```

**Lösung:** Korrekte Verschachtelung des Input-Objekts als `{ input }`.

### 3. TypeScript Interface
**Unverändert korrekt:**
```typescript
export interface CreateApplicationInput {
  name: string;
  description?: string;
}
```

**Lösung:** Interface bleibt unverändert, da es bereits korrekt definiert war.

## Lessons Learned für Create-Mutations

### 1. GraphQL Input-Object Pattern
- **Problem:** Direkte Parameter vs. Input-Objekt Inkonsistenz
- **Lösung:** Konsistente Verwendung des Input-Object Patterns für alle Mutations
- **Vorteil:** Bessere Typsicherheit, einfachere Erweiterung, klarere API-Struktur

### 2. Variable-Passing Konsistenz
- **Problem:** Mismatch zwischen Service-Variable-Passing und Mutation-Erwartung
- **Lösung:** Immer prüfen, ob Mutation Input-Objekt oder direkte Parameter erwartet
- **Pattern:** `variables: { input }` für Input-Object Pattern

### 3. Backend-Schema Alignment
- **Problem:** Frontend-Implementierung stimmte nicht mit Backend-Schema überein
- **Lösung:** Zuerst Backend-Schema analysieren, dann Frontend entsprechend implementieren
- **Wichtig:** Mutation-Signatur muss exakt mit Backend übereinstimmen

### 4. Cache-Update Strategie
- **Erfolg:** Cache-Update Logic war bereits korrekt implementiert
- **Best Practice:** Nach erfolgreicher Creation das neue Objekt zum Cache hinzufügen
- **Optimierung:** Optimistic Updates für bessere User Experience

## Best Practices für Create-Mutations

### 1. Konsistente Mutation-Patterns
```graphql
# Input-Object Pattern (empfohlen)
mutation CreateEntity($input: CreateEntityInput!) {
  createEntity(input: $input) {
    # Vollständige Entity-Felder
  }
}

# Direkte Parameter (nur für einfache Fälle)
mutation CreateSimpleEntity($name: String!) {
  createSimpleEntity(name: $name) {
    # Vollständige Entity-Felder
  }
}
```

### 2. Service-Implementierung Template
```typescript
createEntity(input: CreateEntityInput): Observable<Entity> {
  return this.apollo.mutate<{ createEntity: Entity }>({
    mutation: CREATE_ENTITY,
    variables: { input }, // Für Input-Object Pattern
    update: (cache, { data }) => {
      if (data?.createEntity) {
        // Cache-Update Logic
        this.updateCacheAfterCreate(cache, data.createEntity);
      }
    },
    optimisticResponse: {
      createEntity: {
        // Optimistic Response für bessere UX
        ...this.generateOptimisticEntity(input),
        __typename: 'Entity'
      }
    }
  }).pipe(
    map(result => result.data!.createEntity),
    catchError(this.handleError)
  );
}
```

### 3. TypeScript Type Safety
```typescript
// Zentrale Input-Type Definition
export interface CreateEntityInput {
  name: string;
  description?: string;
  // Weitere erforderliche Felder
}

// Response-Type für bessere Typsicherheit
export interface CreateEntityResponse {
  createEntity: Entity;
}
```

### 4. Testing für Create-Mutations
```typescript
// Unit Test für korrekte Create-Mutation
it('should call createApplication with correct GraphQL variables', () => {
  const input: CreateApplicationInput = {
    name: 'Test App',
    description: 'Test Description'
  };

  service.createApplication(input).subscribe();

  expect(apolloSpy.mutate).toHaveBeenCalledWith({
    mutation: CREATE_APPLICATION,
    variables: { input }, // Korrekte Verschachtelung
    update: jasmine.any(Function)
  });
});

// Integration Test für Cache-Update
it('should update cache after successful creation', () => {
  // Test Cache-Update Logic
});
```

### 5. Error Handling für Create-Mutations
```typescript
private handleCreateError(error: any): Observable<never> {
  console.error('Create mutation failed:', error);
  
  // Spezifische Behandlung für Create-Fehler
  if (error.graphQLErrors?.some(e => e.extensions?.code === 'DUPLICATE_NAME')) {
    return throwError(() => new Error('Eine Applikation mit diesem Namen existiert bereits.'));
  }
  
  return throwError(() => new Error('Fehler beim Erstellen der Applikation.'));
}
```

---

# GraphQL Learnings - Update Mutations

## Fehlerhafte Implementierung bei Update-Mutations

### Problem: Inkonsistente Mutation-Parameter-Struktur

Bei der Implementierung von GraphQL Update-Mutations wurde eine inkonsistente Parameter-Struktur verwendet, die nicht mit der erwarteten Backend-API übereinstimmte.

## Fehlerhafter Ansatz

### 1. GraphQL Mutation Definition
**Falsch implementiert:**
```graphql
mutation UpdateApplication($input: UpdateApplicationInput!) {
  updateApplication(input: $input) {
    identifier
    name
    description
    isDeleted
    createdAt
    updatedAt
  }
}
```

**Problem:** Verwendung eines verschachtelten Input-Objekts anstatt direkter Parameter.

### 2. Service-Implementierung
**Falsch implementiert:**
```typescript
return this.apollo.mutate<{ updateApplication: Application }>({
  mutation: UPDATE_APPLICATION,
  variables: { input },  // Verschachteltes Objekt
  // ...
});
```

**Problem:** Das gesamte Input-Objekt wurde als verschachtelter Parameter übergeben.

### 3. TypeScript Interface
**Falsch implementiert:**
```typescript
export interface UpdateApplicationInput {
  identifier: string;
  name?: string;        // Optional, aber Backend erwartet required
  description?: string;
  isDeleted?: boolean;  // Optional, aber Backend erwartet required
}
```

**Problem:** Erforderliche Felder waren als optional definiert.

## Korrekte Implementierung

### 1. GraphQL Mutation Definition
**Korrekt:**
```graphql
mutation UpdateApplication($identifier: String!, $name: String!, $description: String, $isDeleted: Boolean!) {
  updateApplication(identifier: $identifier, name: $name, description: $description, isDeleted: $isDeleted) {
    identifier
    name
    description
    isDeleted
    createdAt
    updatedAt
  }
}
```

**Lösung:** Direkte Parameter anstatt verschachteltes Input-Objekt.

### 2. Service-Implementierung
**Korrekt:**
```typescript
return this.apollo.mutate<{ updateApplication: Application }>({
  mutation: UPDATE_APPLICATION,
  variables: {
    identifier: input.identifier,
    name: input.name,
    description: input.description || null,
    isDeleted: input.isDeleted
  },
  // ...
});
```

**Lösung:** Individuelle Parameter aus dem Input-Objekt extrahieren.

### 3. TypeScript Interface
**Korrekt:**
```typescript
export interface UpdateApplicationInput {
  identifier: string;
  name: string;        // Required
  description?: string;
  isDeleted: boolean;  // Required
}
```

**Lösung:** Erforderliche Felder als required definieren.

## Lessons Learned

### 1. Backend-API Konsistenz
- **Problem:** Frontend-Implementierung stimmte nicht mit Backend-Erwartungen überein
- **Lösung:** Immer die tatsächliche Backend-API-Spezifikation prüfen und befolgen

### 2. GraphQL Parameter-Patterns
- **Input-Objekt Pattern:** `updateEntity(input: UpdateEntityInput!)`
- **Direkte Parameter Pattern:** `updateEntity(id: ID!, field1: String!, field2: String)`
- **Wichtig:** Konsistenz zwischen Frontend und Backend sicherstellen

### 3. TypeScript Type Safety
- **Problem:** Optionale Felder führten zu Runtime-Fehlern
- **Lösung:** Required/Optional-Status muss Backend-Anforderungen entsprechen

### 4. Komponenten-Updates
- **Problem:** Komponenten verwendeten unvollständige Input-Objekte
- **Lösung:** Alle aufrufenden Komponenten müssen aktualisiert werden:
  - Edit-Dialoge
  - Form-Komponenten
  - Direkte Service-Aufrufe

## Best Practices für Update-Mutations

### 1. API-First Approach
```typescript
// 1. Zuerst Backend-API analysieren
// 2. GraphQL Schema entsprechend definieren
// 3. TypeScript Interfaces anpassen
// 4. Service-Implementierung aktualisieren
// 5. Komponenten aktualisieren
```

### 2. Vollständige Parameter-Validierung
```typescript
// Alle erforderlichen Felder prüfen
if (!input.name || input.isDeleted === undefined) {
  throw new Error('Required fields missing');
}
```

### 3. Konsistente Error Handling
```typescript
// Einheitliche Fehlerbehandlung für alle Mutations
private handleMutationError(error: any): Observable<never> {
  // Spezifische Behandlung für Update-Fehler
  return throwError(() => new Error(this.formatErrorMessage(error)));
}
```

### 4. Testing
```typescript
// Unit Tests für korrekte Parameter-Struktur
it('should call updateApplication with correct GraphQL variables', () => {
  expect(apolloSpy.mutate).toHaveBeenCalledWith({
    mutation: UPDATE_APPLICATION,
    variables: {
      identifier: 'test-id',
      name: 'Test Name',
      description: 'Test Description',
      isDeleted: false
    }
  });
});
```

---

# GraphQL Learnings - Delete Mutations

## Fehlerhafte Implementierung bei Delete-Mutations

### Problem 1: Unvollständige Mutation-Response

Bei der Implementierung von GraphQL Delete-Mutations wurde eine unvollständige Response-Struktur verwendet, die nicht alle erforderlichen Felder zurückgab.

### Problem 2: Falsche GraphQL-Typen

Die GraphQL-Schema-Typen stimmten nicht mit den Backend-Erwartungen überein (`ID!` vs `UUID!`).

## Fehlerhafter Ansatz

### 1. GraphQL Mutation Definition
**Falsch implementiert:**
```graphql
export const DELETE_APPLICATION = gql`
  mutation DeleteApplication($identifier: ID!) {
    deleteApplication(identifier: $identifier) {
      identifier
      isDeleted
    }
  }
`;
```

**Probleme:**
- Verwendung von `ID!` anstatt `UUID!` für identifier
- Nur `identifier` und `isDeleted` werden zurückgegeben, nicht das vollständige Application-Objekt

### 2. TypeScript Response Interface
**Falsch implementiert:**
```typescript
export interface DeleteApplicationResponse {
  deleteApplication: {
    identifier: string;
    isDeleted: boolean;
  };
}
```

**Problem:** Interface entspricht nicht der vollständigen Application-Struktur.

### 3. Inkonsistente Query-Typen
**Falsch implementiert:**
```graphql
// In application.queries.ts
export const GET_APPLICATION_BY_ID = gql`
  query GetApplication($identifier: ID!) {  // Falsch: ID! statt UUID!
    application(identifier: $identifier) {
      // ...
    }
  }
`;
```

**Problem:** Inkonsistente Verwendung von `ID!` vs `UUID!` zwischen Queries und Mutations.

## Korrekte Implementierung

### 1. GraphQL Mutation Definition
**Korrekt:**
```graphql
export const DELETE_APPLICATION = gql`
  mutation DeleteApplication($identifier: UUID!) {
    deleteApplication(identifier: $identifier) {
      identifier
      name
      description
      isDeleted
      createdAt
      updatedAt
    }
  }
`;
```

**Lösung:**
- Verwendung von `UUID!` für identifier-Parameter
- Vollständige Application-Felder in der Response

### 2. TypeScript Response Interface
**Korrekt:**
```typescript
export interface DeleteApplicationResponse {
  deleteApplication: ApplicationResponse;
}
```

**Lösung:** Verwendung der vollständigen ApplicationResponse-Struktur.

### 3. Konsistente Query-Typen
**Korrekt:**
```graphql
// Alle identifier-Parameter verwenden UUID!
export const GET_APPLICATION_BY_ID = gql`
  query GetApplication($identifier: UUID!) {
    application(identifier: $identifier) {
      identifier
      name
      description
      isDeleted
      createdAt
      updatedAt
    }
  }
`;

export const GET_INCIDENTS_BY_APPLICATION = gql`
  query GetIncidentsByApplication($applicationIdentifier: UUID!) {
    incidentsByApplication(applicationIdentifier: $applicationIdentifier) {
      // ...
    }
  }
`;

export const CHECK_APPLICATION_DEPENDENCIES = gql`
  query CheckApplicationDependencies($identifier: UUID!) {
    applicationDependencies(identifier: $identifier) {
      // ...
    }
  }
`;
```

**Lösung:** Konsistente Verwendung von `UUID!` für alle identifier-Parameter.

## Lessons Learned für Delete-Mutations

### 1. Vollständige Response-Objekte
- **Problem:** Unvollständige Responses erschweren Frontend-Updates und Caching
- **Lösung:** Immer das vollständige Objekt zurückgeben, auch bei Delete-Operationen

### 2. GraphQL-Schema-Typen-Konsistenz
- **Problem:** Inkonsistente Typen (`ID!` vs `UUID!`) führen zu Runtime-Fehlern
- **Lösung:** Schema-Typen müssen exakt mit Backend-Definitionen übereinstimmen

### 3. Type Safety über alle GraphQL-Operationen
- **Problem:** Verschiedene Operationen verwendeten unterschiedliche Typen für dieselben Felder
- **Lösung:** Konsistente Typen für identifier-Felder in allen Queries und Mutations

### 4. Error-Message-Analyse
- **Fehler:** `The variable 'identifier' is not compatible with the type of the current location`
- **Ursache:** Type-Mismatch zwischen Frontend (`ID!`) und Backend (`UUID!`)
- **Lösung:** Genaue Analyse der GraphQL-Error-Messages für Type-Probleme

## Best Practices für Delete-Mutations

### 1. Vollständige Response-Struktur
```graphql
mutation DeleteEntity($identifier: UUID!) {
  deleteEntity(identifier: $identifier) {
    # Alle Felder des Entity-Objekts zurückgeben
    identifier
    name
    description
    isDeleted
    createdAt
    updatedAt
  }
}
```

### 2. Konsistente Type-Definitionen
```typescript
// Zentrale Type-Definition für identifier
type EntityIdentifier = string; // UUID format

// Konsistente Verwendung in allen Interfaces
export interface DeleteEntityInput {
  identifier: EntityIdentifier;
}

export interface EntityResponse {
  identifier: EntityIdentifier;
  // ... andere Felder
}
```

### 3. Schema-Validierung
```typescript
// Vor Deployment: Schema-Kompatibilität prüfen
// 1. Backend GraphQL Schema exportieren
// 2. Frontend GraphQL Operationen gegen Schema validieren
// 3. Type-Mismatches identifizieren und beheben
```

### 4. Testing für Delete-Operationen
```typescript
// Unit Test für korrekte Delete-Mutation
it('should call deleteApplication with correct GraphQL variables and return full object', () => {
  const mockResponse = {
    data: {
      deleteApplication: {
        identifier: 'test-uuid',
        name: 'Test App',
        description: 'Test Description',
        isDeleted: true,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }
    }
  };

  expect(apolloSpy.mutate).toHaveBeenCalledWith({
    mutation: DELETE_APPLICATION,
    variables: {
      identifier: 'test-uuid'
    }
  });

  expect(result).toEqual(mockResponse.data.deleteApplication);
});
```
