.filter-card {
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .filter-form {
    .filter-row {
      display: flex;
      gap: 16px;
      align-items: flex-end;
      flex-wrap: wrap;

      .search-field {
        flex: 2;
        min-width: 250px;
        
        .mat-mdc-form-field-icon-suffix {
          .mat-mdc-icon-button {
            width: 40px;
            height: 40px;
            
            .mat-icon {
              font-size: 20px;
              width: 20px;
              height: 20px;
            }
          }
        }
      }

      .status-field {
        flex: 1;
        min-width: 220px;
        
        ::ng-deep .mat-mdc-select-value {
          display: flex;
          align-items: center;
          gap: 8px;
        }
        
        ::ng-deep .mat-mdc-option {
          display: flex;
          align-items: center;
          gap: 8px;
          
          .mat-icon {
            font-size: 18px;
            width: 18px;
            height: 18px;
          }
        }
      }

      .clear-button {
        display: flex;
        align-items: center;
        gap: 8px;
        height: 56px; // Match form field height
        white-space: nowrap;
        margin-bottom: 1.25em; // Align with form field baseline

        &:disabled {
          opacity: 0.5;
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .filter-card {
    .filter-form {
      .filter-row {
        flex-direction: column;
        align-items: stretch;

        .search-field,
        .status-field {
          flex: none;
          min-width: auto;
          width: 100%;
        }

        .clear-button {
          height: auto;
          padding: 12px 16px;
          justify-content: center;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .filter-card {
    margin: 0 -8px 16px -8px;
    border-radius: 0;
  }
}