<div class="application-create-dialog">
  <!-- <PERSON>alog Header -->
  <div class="dialog-header" mat-dialog-title>
    <div class="header-content">
      <mat-icon class="header-icon">add_circle</mat-icon>
      <h2 class="dialog-title">Neue Applikation erstellen</h2>
    </div>
    <button
      mat-icon-button
      class="close-button"
      (click)="onCancel()"
      aria-label="Dialog schließen"
      matTooltip="Schließen">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <!-- Dialog Content -->
  <div class="dialog-content" mat-dialog-content>
    <!-- Loading Overlay -->
    <div class="loading-overlay" *ngIf="loading$ | async">
      <mat-spinner diameter="40" aria-label="Wird erstellt..."></mat-spinner>
      <p class="loading-text">Applikation wird erstellt...</p>
    </div>

    <!-- Error Message -->
    <div class="error-banner" *ngIf="error$ | async as error">
      <mat-icon class="error-icon">error</mat-icon>
      <div class="error-content">
        <h4>Fehler beim Erstellen</h4>
        <p>{{ error.message || 'Ein unbekannter Fehler ist aufgetreten.' }}</p>
      </div>
    </div>

    <!-- Create Form -->
    <div class="form-container" [class.disabled]="loading$ | async">
      <app-application-create-form
        [loading]="(loading$ | async) ?? false"
        [disabled]="(loading$ | async) ?? false"
        (formSubmit)="onSubmit($event)"
        (formCancel)="onCancel()">
      </app-application-create-form>
    </div>
  </div>
</div>