import { Injectable, inject } from '@angular/core';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { User, LoginCredentials } from '../../core/models/user.model';
import * as AuthActions from './auth.actions';
import * as AuthSelectors from './auth.selectors';

@Injectable({
  providedIn: 'root'
})
export class AuthFacade {
  private store = inject(Store);

  // Selectors
  readonly currentUser$ = this.store.select(AuthSelectors.selectCurrentUser);
  readonly isAuthenticated$ = this.store.select(AuthSelectors.selectIsAuthenticated);
  readonly isLoading$ = this.store.select(AuthSelectors.selectAuthLoading);
  readonly error$ = this.store.select(AuthSelectors.selectAuthError);
  readonly token$ = this.store.select(AuthSelectors.selectAuthToken);
  readonly userRole$ = this.store.select(AuthSelectors.selectUserRole);
  readonly userFullName$ = this.store.select(AuthSelectors.selectUserFullName);
  readonly userInitials$ = this.store.select(AuthSelectors.selectUserInitials);
  readonly isAdmin$ = this.store.select(AuthSelectors.selectIsAdmin);
  readonly isManager$ = this.store.select(AuthSelectors.selectIsManager);
  readonly canManageUsers$ = this.store.select(AuthSelectors.selectCanManageUsers);
  readonly canManageSystems$ = this.store.select(AuthSelectors.selectCanManageSystems);
  readonly authStatus$ = this.store.select(AuthSelectors.selectAuthStatus);

  // Actions
  login(credentials: LoginCredentials): void {
    this.store.dispatch(AuthActions.login({ credentials }));
  }

  logout(): void {
    this.store.dispatch(AuthActions.logout());
  }

  refreshToken(): void {
    this.store.dispatch(AuthActions.refreshToken());
  }

  hydrateAuth(): void {
    this.store.dispatch(AuthActions.hydrateAuth());
  }

  clearAuthError(): void {
    this.store.dispatch(AuthActions.clearAuthError());
  }

  updateUserProfile(user: Partial<User>): void {
    this.store.dispatch(AuthActions.updateUserProfile({ user }));
  }

  // Convenience methods
  getCurrentUser(): Observable<User | null> {
    return this.currentUser$;
  }

  isAuthenticated(): Observable<boolean> {
    return this.isAuthenticated$;
  }

  hasRole(role: string): Observable<boolean> {
    return this.store.select(state => {
      const currentUser = AuthSelectors.selectCurrentUser(state);
      return currentUser?.role === role;
    });
  }

  hasAnyRole(roles: string[]): Observable<boolean> {
    return this.store.select(state => {
      const currentUser = AuthSelectors.selectCurrentUser(state);
      return currentUser ? roles.includes(currentUser.role) : false;
    });
  }
}